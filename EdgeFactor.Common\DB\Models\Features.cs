namespace EdgeFactor.Common.DB.Models;

public partial class Features
{
    public Guid Id { get; set; }

    public Guid? FeatureTypeId { get; set; }

    public string? Code { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string? Slug { get; set; }

    public Guid? IconAssetId { get; set; }

    public Guid? CoverMediaAssetId { get; set; }

    public bool IsActive { get; set; }

    public string? Descriptors { get; set; }

    public Guid? TextureAssetId { get; set; }

    public Guid? JourneyStageId { get; set; }

    public Guid? RoleObjectiveId { get; set; }

    public Guid? TargetAudienceId { get; set; }

    public Guid? ContinuumTimelineId { get; set; }

    public Guid? FeatureCategoryId { get; set; }

    public string? InstanceDescriptors { get; set; }

    public bool? DisplayObjectCount { get; set; }

    public bool? IsFullWidth { get; set; }

    public virtual ICollection<Campaigns> Campaigns { get; set; } = new List<Campaigns>();

    public virtual ICollection<FeatureCommunications> FeatureCommunications { get; set; } = new List<FeatureCommunications>();

    public virtual ICollection<FeatureTabs> FeatureTabs { get; set; } = new List<FeatureTabs>();

    public virtual ICollection<FeatureTags> FeatureTags { get; set; } = new List<FeatureTags>();

    public virtual FeatureTypes? FeatureType { get; set; }

    public virtual ICollection<Instances> Instances { get; set; } = new List<Instances>();

    public virtual ICollection<ProductFeatures> ProductFeatures { get; set; } = new List<ProductFeatures>();

    public virtual ICollection<Questions> Questions { get; set; } = new List<Questions>();
}
