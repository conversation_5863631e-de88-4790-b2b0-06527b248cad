"use strict";(self.webpackChunkpublic=self.webpackChunkpublic||[]).push([[1577],{41577:(E,s,o)=>{o.r(s),o.d(s,{ion_text:()=>a});var r=o(54261),u=o(80333),l=o(9483);const a=(()=>{let c=class{constructor(t){(0,r.r)(this,t),this.color=void 0}render(){const t=(0,l.b)(this);return(0,r.h)(r.f,{key:"4b76333b1ea5cab134b9dc1f5670c0d5a253fc32",class:(0,u.c)(this.color,{[t]:!0})},(0,r.h)("slot",{key:"3dee5f16bc58b3d92547d910bd4f441a00ce2039"}))}};return c.style=":host(.ion-color){color:var(--ion-color-base)}",c})()},80333:(E,s,o)=>{o.d(s,{c:()=>l,g:()=>_,h:()=>u,o:()=>c});var r=o(10467);const u=(t,e)=>null!==e.closest(t),l=(t,e)=>"string"==typeof t&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},e):e,_=t=>{const e={};return(t=>void 0!==t?(Array.isArray(t)?t:t.split(" ")).filter(n=>null!=n).map(n=>n.trim()).filter(n=>""!==n):[])(t).forEach(n=>e[n]=!0),e},a=/^[a-z][a-z0-9+\-.]*:/,c=function(){var t=(0,r.A)(function*(e,n,d,h){if(null!=e&&"#"!==e[0]&&!a.test(e)){const i=document.querySelector("ion-router");if(i)return n?.preventDefault(),i.push(e,d,h)}return!1});return function(n,d,h,i){return t.apply(this,arguments)}}()}}]);