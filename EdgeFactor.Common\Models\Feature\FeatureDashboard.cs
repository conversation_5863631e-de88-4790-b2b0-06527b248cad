using EdgeFactor.Common.DB.Models;
using EdgeFactor.Common.Models.Template;

namespace EdgeFactor.Common.Models.Feature
{
    public class FeatureDashboard
    {
        public FeatureDashboard(Instances x) : this(x.Id, x.Title, x.Title, x.Feature.CoverMediaAssetId, "", x.Description, x.Status, x.IsDefault) { }

        public FeatureDashboard(Guid id, string objectName, string? internalName, Guid? coverMedia, string? block, string? shortDescription, string? status, bool? isDefault)
        {
            Id = id;
            ObjectName = objectName;
            InternalName = internalName;
            CoverMedia = coverMedia;
            Block = block;
            ShortDescription = shortDescription;
            Status = status;
            IsDefault = isDefault;
        }

        public Guid Id { get; set; }
        public string ObjectName { get; set; }
        public string? InternalName { get; set; }
        public Guid? CoverMedia { get; set; }
        public string? Block { get; set; }
        public string? ShortDescription { get; set; }
        public string? Status { get; set; }
        public bool? IsDefault { get; set; }
    }

    public class DashboardGrid
    {
        public IEnumerable<string>? ColumnNames { get; set; }
        public IEnumerable<DashboardGridRow>? Rows { get; set; }
    }

    public class DashboardGridRow
    {
        public Guid TableId { get; set; }
        public Guid? UserId { get; set; }
        public string? Name { get; set; }
        public IEnumerable<DashboardGridColumn>? Columns { get; set; }
    }

    public class DashboardGridColumn
    {
        public string? ColumnName { get; set; }
        public string? Value { get; set; }
        public IEnumerable<object>? ListItems { get; set; }
        public Guid? Id { get; set; }
        public bool IsMultiple { get; set; }
        public Component Component { get; set; } = null!;
    }

    public class DashboardGridFilters
    {
        public string? RepoSearchValue { get; set; }
        public int? CurrentAmount { get; set; }
        public int? GetAmount { get; set; }
        public string? SortBy { get; set; }
        public string? SortDir { get; set; }
        public string? FilterField { get; set; }
        public string? FilterValue { get; set; }
    }
}
