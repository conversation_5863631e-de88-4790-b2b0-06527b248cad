@font-face{font-family:swiper-icons;src:url(data:application/font-woff;charset=utf-8;base64,\ 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);font-weight:400;font-style:normal}:root{--swiper-theme-color: #007aff}:host{position:relative;display:block;margin-left:auto;margin-right:auto;z-index:1}.swiper{margin-left:auto;margin-right:auto;position:relative;overflow:hidden;overflow:clip;list-style:none;padding:0;z-index:1;display:block}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;transition-timing-function:var(--swiper-wrapper-transition-timing-function, initial);box-sizing:content-box}.swiper-android .swiper-slide,.swiper-ios .swiper-slide,.swiper-wrapper{transform:translateZ(0)}.swiper-horizontal{touch-action:pan-y}.swiper-vertical{touch-action:pan-x}.swiper-slide{flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform;display:block}.swiper-slide-invisible-blank{visibility:hidden}.swiper-autoheight,.swiper-autoheight .swiper-slide{height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden .swiper-slide{transform:translateZ(0);backface-visibility:hidden}.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:1200px}.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d{perspective:1200px}.swiper-3d .swiper-slide,.swiper-3d .swiper-cube-shadow{transform-style:preserve-3d}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode>.swiper-wrapper>.swiper-slide{scroll-snap-align:start start}.swiper-css-mode.swiper-horizontal>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-css-mode.swiper-vertical>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-css-mode.swiper-free-mode>.swiper-wrapper{scroll-snap-type:none}.swiper-css-mode.swiper-free-mode>.swiper-wrapper>.swiper-slide{scroll-snap-align:none}.swiper-css-mode.swiper-centered>.swiper-wrapper:before{content:"";flex-shrink:0;order:9999}.swiper-css-mode.swiper-centered>.swiper-wrapper>.swiper-slide{scroll-snap-align:center center;scroll-snap-stop:always}.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper>.swiper-slide:first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper:before{height:100%;min-height:1px;width:var(--swiper-centered-offset-after)}.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper>.swiper-slide:first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper:before{width:100%;min-width:1px;height:var(--swiper-centered-offset-after)}.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top,.swiper-3d .swiper-slide-shadow-bottom{position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}.swiper-3d .swiper-slide-shadow{background:#00000026}.swiper-3d .swiper-slide-shadow-left{background-image:linear-gradient(to left,#00000080,#0000)}.swiper-3d .swiper-slide-shadow-right{background-image:linear-gradient(to right,#00000080,#0000)}.swiper-3d .swiper-slide-shadow-top{background-image:linear-gradient(to top,#00000080,#0000)}.swiper-3d .swiper-slide-shadow-bottom{background-image:linear-gradient(to bottom,#00000080,#0000)}.swiper-lazy-preloader{width:42px;height:42px;position:absolute;left:50%;top:50%;margin-left:-21px;margin-top:-21px;z-index:10;transform-origin:50%;box-sizing:border-box;border:4px solid var(--swiper-preloader-color, var(--swiper-theme-color));border-radius:50%;border-top-color:transparent}.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader{animation:swiper-preloader-spin 1s infinite linear}.swiper-lazy-preloader-white{--swiper-preloader-color: #fff}.swiper-lazy-preloader-black{--swiper-preloader-color: #000}@keyframes swiper-preloader-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.swiper-virtual .swiper-slide{-webkit-backface-visibility:hidden;transform:translateZ(0)}.swiper-virtual.swiper-css-mode .swiper-wrapper:after{content:"";position:absolute;left:0;top:0;pointer-events:none}.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper:after{height:1px;width:var(--swiper-virtual-size)}.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper:after{width:1px;height:var(--swiper-virtual-size)}:root{--swiper-navigation-size: 44px}.swiper-button-prev,.swiper-button-next{position:absolute;top:var(--swiper-navigation-top-offset, 50%);width:calc(var(--swiper-navigation-size) / 44 * 27);height:var(--swiper-navigation-size);margin-top:calc(0px - (var(--swiper-navigation-size) / 2));z-index:10;cursor:pointer;display:flex;align-items:center;justify-content:center;color:var(--swiper-navigation-color, var(--swiper-theme-color))}.swiper-button-prev.swiper-button-disabled,.swiper-button-next.swiper-button-disabled{opacity:.35;cursor:auto;pointer-events:none}.swiper-button-prev.swiper-button-hidden,.swiper-button-next.swiper-button-hidden{opacity:0;cursor:auto;pointer-events:none}.swiper-navigation-disabled .swiper-button-prev,.swiper-navigation-disabled .swiper-button-next{display:none!important}.swiper-button-prev svg,.swiper-button-next svg{width:100%;height:100%;object-fit:contain;transform-origin:center}.swiper-rtl .swiper-button-prev svg,.swiper-rtl .swiper-button-next svg{transform:rotate(180deg)}.swiper-button-prev,.swiper-rtl .swiper-button-next{left:var(--swiper-navigation-sides-offset, 10px);right:auto}.swiper-button-lock{display:none}.swiper-button-prev:after,.swiper-button-next:after{font-family:swiper-icons;font-size:var(--swiper-navigation-size);text-transform:none!important;letter-spacing:0;font-variant:initial;line-height:1}.swiper-button-prev:after,.swiper-rtl .swiper-button-next:after{content:"prev"}.swiper-button-next,.swiper-rtl .swiper-button-prev{right:var(--swiper-navigation-sides-offset, 10px);left:auto}.swiper-button-next:after,.swiper-rtl .swiper-button-prev:after{content:"next"}.swiper-pagination{position:absolute;text-align:center;transition:.3s opacity;transform:translateZ(0);z-index:10}.swiper-pagination.swiper-pagination-hidden{opacity:0}.swiper-pagination-disabled>.swiper-pagination,.swiper-pagination.swiper-pagination-disabled{display:none!important}.swiper-pagination-fraction,.swiper-pagination-custom,.swiper-horizontal>.swiper-pagination-bullets,.swiper-pagination-bullets.swiper-pagination-horizontal{bottom:var(--swiper-pagination-bottom, 8px);top:var(--swiper-pagination-top, auto);left:0;width:100%}.swiper-pagination-bullets-dynamic{overflow:hidden;font-size:0}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transform:scale(.33);position:relative}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active,.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev{transform:scale(.33)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next{transform:scale(.33)}.swiper-pagination-bullet{width:var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));height:var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));display:inline-block;border-radius:var(--swiper-pagination-bullet-border-radius, 50%);background:var(--swiper-pagination-bullet-inactive-color, #000);opacity:var(--swiper-pagination-bullet-inactive-opacity, .2)}button.swiper-pagination-bullet{border:none;margin:0;padding:0;box-shadow:none;appearance:none}.swiper-pagination-clickable .swiper-pagination-bullet{cursor:pointer}.swiper-pagination-bullet:only-child{display:none!important}.swiper-pagination-bullet-active{opacity:var(--swiper-pagination-bullet-opacity, 1);background:var(--swiper-pagination-color, var(--swiper-theme-color))}.swiper-vertical>.swiper-pagination-bullets,.swiper-pagination-vertical.swiper-pagination-bullets{right:var(--swiper-pagination-right, 8px);left:var(--swiper-pagination-left, auto);top:50%;transform:translate3d(0,-50%,0)}.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet{margin:var(--swiper-pagination-bullet-vertical-gap, 6px) 0;display:block}.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{top:50%;transform:translateY(-50%);width:8px}.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{display:inline-block;transition:.2s transform,.2s top}.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet{margin:0 var(--swiper-pagination-bullet-horizontal-gap, 4px)}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{left:50%;transform:translate(-50%);white-space:nowrap}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s left}.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s right}.swiper-pagination-fraction{color:var(--swiper-pagination-fraction-color, inherit)}.swiper-pagination-progressbar{background:var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, .25));position:absolute}.swiper-pagination-progressbar .swiper-pagination-progressbar-fill{background:var(--swiper-pagination-color, var(--swiper-theme-color));position:absolute;left:0;top:0;width:100%;height:100%;transform:scale(0);transform-origin:left top}.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{transform-origin:right top}.swiper-horizontal>.swiper-pagination-progressbar,.swiper-pagination-progressbar.swiper-pagination-horizontal,.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite{width:100%;height:var(--swiper-pagination-progressbar-size, 4px);left:0;top:0}.swiper-vertical>.swiper-pagination-progressbar,.swiper-pagination-progressbar.swiper-pagination-vertical,.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite{width:var(--swiper-pagination-progressbar-size, 4px);height:100%;left:0;top:0}.swiper-pagination-lock{display:none}.swiper-scrollbar{border-radius:var(--swiper-scrollbar-border-radius, 10px);position:relative;touch-action:none;background:var(--swiper-scrollbar-bg-color, rgba(0, 0, 0, .1))}.swiper-scrollbar-disabled>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-disabled{display:none!important}.swiper-horizontal>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-horizontal{position:absolute;left:var(--swiper-scrollbar-sides-offset, 1%);bottom:var(--swiper-scrollbar-bottom, 4px);top:var(--swiper-scrollbar-top, auto);z-index:50;height:var(--swiper-scrollbar-size, 4px);width:calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%))}.swiper-vertical>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-vertical{position:absolute;left:var(--swiper-scrollbar-left, auto);right:var(--swiper-scrollbar-right, 4px);top:var(--swiper-scrollbar-sides-offset, 1%);z-index:50;width:var(--swiper-scrollbar-size, 4px);height:calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%))}.swiper-scrollbar-drag{height:100%;width:100%;position:relative;background:var(--swiper-scrollbar-drag-bg-color, rgba(0, 0, 0, .5));border-radius:var(--swiper-scrollbar-border-radius, 10px);left:0;top:0}.swiper-scrollbar-cursor-drag{cursor:move}.swiper-scrollbar-lock{display:none}.swiper-zoom-container{width:100%;height:100%;display:flex;justify-content:center;align-items:center;text-align:center}.swiper-zoom-container>img,.swiper-zoom-container>svg,.swiper-zoom-container>canvas{max-width:100%;max-height:100%;object-fit:contain}.swiper-slide-zoomed{cursor:move;touch-action:none}.swiper .swiper-notification{position:absolute;left:0;top:0;pointer-events:none;opacity:0;z-index:-1000}.swiper-free-mode>.swiper-wrapper{transition-timing-function:ease-out;margin:0 auto}.swiper-grid>.swiper-wrapper{flex-wrap:wrap}.swiper-grid-column>.swiper-wrapper{flex-wrap:wrap;flex-direction:column}.swiper-fade.swiper-free-mode .swiper-slide{transition-timing-function:ease-out}.swiper-fade .swiper-slide{pointer-events:none;transition-property:opacity}.swiper-fade .swiper-slide .swiper-slide{pointer-events:none}.swiper-fade .swiper-slide-active,.swiper-fade .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-cube{overflow:visible}.swiper-cube .swiper-slide{pointer-events:none;backface-visibility:hidden;z-index:1;visibility:hidden;transform-origin:0 0;width:100%;height:100%}.swiper-cube .swiper-slide .swiper-slide{pointer-events:none}.swiper-cube.swiper-rtl .swiper-slide{transform-origin:100% 0}.swiper-cube .swiper-slide-active,.swiper-cube .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-cube .swiper-slide-active,.swiper-cube .swiper-slide-next,.swiper-cube .swiper-slide-prev{pointer-events:auto;visibility:visible}.swiper-cube .swiper-cube-shadow{position:absolute;left:0;bottom:0;width:100%;height:100%;opacity:.6;z-index:0}.swiper-cube .swiper-cube-shadow:before{content:"";background:#000;position:absolute;inset:0;filter:blur(50px)}.swiper-cube .swiper-slide-next+.swiper-slide{pointer-events:auto;visibility:visible}.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-top,.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-bottom,.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-left,.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-right{z-index:0;backface-visibility:hidden}.swiper-flip{overflow:visible}.swiper-flip .swiper-slide{pointer-events:none;backface-visibility:hidden;z-index:1}.swiper-flip .swiper-slide .swiper-slide{pointer-events:none}.swiper-flip .swiper-slide-active,.swiper-flip .swiper-slide-active .swiper-slide-active{pointer-events:auto}.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-top,.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-bottom,.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-left,.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-right{z-index:0;backface-visibility:hidden}.swiper-creative .swiper-slide{backface-visibility:hidden;overflow:hidden;transition-property:transform,opacity,height}.swiper-cards{overflow:visible}.swiper-cards .swiper-slide{transform-origin:center bottom;backface-visibility:hidden;overflow:hidden}@font-face{font-family:videogular;src:url(videogular.3fd92bcf683e015f.eot?hj1wei);src:url(videogular.3fd92bcf683e015f.eot?hj1wei#iefix) format("embedded-opentype"),url(videogular.a2e9f11f226cda93.ttf?hj1wei) format("truetype"),url(videogular.ef07f01ce399477e.woff?hj1wei) format("woff"),url(videogular.b5fa324995460a3b.svg?hj1wei#videogular) format("svg");font-weight:400;font-style:normal}[class*=" vg-icon-"],[class^=vg-icon-]{font-family:videogular!important;speak:none;font-style:normal;font-weight:400;font-variant:normal;text-transform:none;line-height:1;display:flex;align-items:center;justify-content:center;width:50px;font-size:24px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.vg-icon-closed_caption:before{content:"\e006"}.vg-icon-pause:before{content:"\e018"}.vg-icon-play_arrow:before{content:"\e01b"}.vg-icon-repeat:before{content:"\e023"}.vg-icon-replay:before{content:"\e025"}.vg-icon-skip_next:before{content:"\e027"}.vg-icon-skip_previous:before{content:"\e028"}.vg-icon-stop:before{content:"\e02a"}.vg-icon-volume_down:before{content:"\e030"}.vg-icon-volume_mute:before{content:"\e031"}.vg-icon-volume_off:before{content:"\e032"}.vg-icon-volume_up:before{content:"\e033"}.vg-icon-hd:before{content:"\e035"}.vg-icon-forward_10:before{content:"\e038"}.vg-icon-forward_30:before{content:"\e039"}.vg-icon-replay_10:before{content:"\e03b"}.vg-icon-replay_30:before{content:"\e03c"}.vg-icon-fullscreen:before{content:"\e20c"}.vg-icon-fullscreen_exit:before{content:"\e20d"}vg-player video{width:100%;height:100%}:root{--ion-color-primary: #f99e00;--ion-color-primary-rgb: 249, 158, 0;--ion-color-primary-contrast: #000000;--ion-color-primary-contrast-rgb: 0, 0, 0;--ion-color-primary-shade: #db8b00;--ion-color-primary-tint: #faa81a;--ion-color-secondary: #111111;--ion-color-secondary-rgb: 17, 17, 17;--ion-color-secondary-contrast: #ffffff;--ion-color-secondary-contrast-rgb: 255, 255, 255;--ion-color-secondary-shade: #0f0f0f;--ion-color-secondary-tint: #292929;--ion-color-tertiary: #5260ff;--ion-color-tertiary-rgb: 82, 96, 255;--ion-color-tertiary-contrast: #ffffff;--ion-color-tertiary-contrast-rgb: 255, 255, 255;--ion-color-tertiary-shade: #4854e0;--ion-color-tertiary-tint: #6370ff;--ion-color-success: #2dd36f;--ion-color-success-rgb: 45, 211, 111;--ion-color-success-contrast: #ffffff;--ion-color-success-contrast-rgb: 255, 255, 255;--ion-color-success-shade: #28ba62;--ion-color-success-tint: #42d77d;--ion-color-warning: #ffc409;--ion-color-warning-rgb: 255, 196, 9;--ion-color-warning-contrast: #000000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0ac08;--ion-color-warning-tint: #ffca22;--ion-color-danger: #eb445a;--ion-color-danger-rgb: 235, 68, 90;--ion-color-danger-contrast: #ffffff;--ion-color-danger-contrast-rgb: 255, 255, 255;--ion-color-danger-shade: #cf3c4f;--ion-color-danger-tint: #ed576b;--ion-color-dark: #222428;--ion-color-dark-rgb: 34, 36, 40;--ion-color-dark-contrast: #ffffff;--ion-color-dark-contrast-rgb: 255, 255, 255;--ion-color-dark-shade: #1e2023;--ion-color-dark-tint: #383a3e;--ion-color-medium: #aaa;--ion-color-medium-rgb: 146, 148, 156;--ion-color-medium-contrast: #ffffff;--ion-color-medium-contrast-rgb: 255, 255, 255;--ion-color-medium-shade: #808289;--ion-color-medium-tint: #9d9fa6;--ion-color-light: #f4f5f8;--ion-color-light-rgb: 244, 245, 248;--ion-color-light-contrast: #000000;--ion-color-light-contrast-rgb: 0, 0, 0;--ion-color-light-shade: #d7d8da;--ion-color-light-tint: #f5f6f9;--ion-toolbar-background: transparent}@media (prefers-color-scheme: dark){body{--ion-color-primary: #f99e00;--ion-color-primary-rgb: 249, 158, 0;--ion-color-primary-contrast: #000000;--ion-color-primary-contrast-rgb: 0, 0, 0;--ion-color-primary-shade: #db8b00;--ion-color-primary-tint: #faa81a;--ion-color-secondary: #111111;--ion-color-secondary-rgb: 17, 17, 17;--ion-color-secondary-contrast: #ffffff;--ion-color-secondary-contrast-rgb: 255, 255, 255;--ion-color-secondary-shade: #0f0f0f;--ion-color-secondary-tint: #292929;--ion-color-tertiary: #5260ff;--ion-color-tertiary-rgb: 82, 96, 255;--ion-color-tertiary-contrast: #ffffff;--ion-color-tertiary-contrast-rgb: 255, 255, 255;--ion-color-tertiary-shade: #4854e0;--ion-color-tertiary-tint: #6370ff;--ion-color-success: #2dd36f;--ion-color-success-rgb: 45, 211, 111;--ion-color-success-contrast: #ffffff;--ion-color-success-contrast-rgb: 255, 255, 255;--ion-color-success-shade: #28ba62;--ion-color-success-tint: #42d77d;--ion-color-warning: #ffc409;--ion-color-warning-rgb: 255, 196, 9;--ion-color-warning-contrast: #000000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0ac08;--ion-color-warning-tint: #ffca22;--ion-color-danger: #eb445a;--ion-color-danger-rgb: 235, 68, 90;--ion-color-danger-contrast: #ffffff;--ion-color-danger-contrast-rgb: 255, 255, 255;--ion-color-danger-shade: #cf3c4f;--ion-color-danger-tint: #ed576b;--ion-color-dark: #222428;--ion-color-dark-rgb: 34, 36, 40;--ion-color-dark-contrast: #ffffff;--ion-color-dark-contrast-rgb: 255, 255, 255;--ion-color-dark-shade: #1e2023;--ion-color-dark-tint: #383a3e;--ion-color-medium: #aaa;--ion-color-medium-rgb: 146, 148, 156;--ion-color-medium-contrast: #ffffff;--ion-color-medium-contrast-rgb: 255, 255, 255;--ion-color-medium-shade: #808289;--ion-color-medium-tint: #9d9fa6;--ion-color-light: #f4f5f8;--ion-color-light-rgb: 244, 245, 248;--ion-color-light-contrast: #000000;--ion-color-light-contrast-rgb: 0, 0, 0;--ion-color-light-shade: #d7d8da;--ion-color-light-tint: #f5f6f9;--ion-toolbar-background: transparent}.ios body{--ion-background-color: #000000;--ion-background-color-rgb: 0, 0, 0;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-color-step-50: #0d0d0d;--ion-color-step-100: #1a1a1a;--ion-color-step-150: #262626;--ion-color-step-200: #333333;--ion-color-step-250: #404040;--ion-color-step-300: #4d4d4d;--ion-color-step-350: #595959;--ion-color-step-400: #666666;--ion-color-step-450: #737373;--ion-color-step-500: #808080;--ion-color-step-550: #8c8c8c;--ion-color-step-600: #999999;--ion-color-step-650: #a6a6a6;--ion-color-step-700: #b3b3b3;--ion-color-step-750: #bfbfbf;--ion-color-step-800: #cccccc;--ion-color-step-850: #d9d9d9;--ion-color-step-900: #e6e6e6;--ion-color-step-950: #f2f2f2;--ion-item-background: #000000;--ion-card-background: #1c1c1d}.ios ion-modal{--ion-background-color: var(--ion-color-step-100);--ion-toolbar-background: var(--ion-color-step-150);--ion-toolbar-border-color: var(--ion-color-step-250)}.md body{--ion-background-color: #121212;--ion-background-color-rgb: 18, 18, 18;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-border-color: #222222;--ion-color-step-50: #1e1e1e;--ion-color-step-100: #2a2a2a;--ion-color-step-150: #363636;--ion-color-step-200: #414141;--ion-color-step-250: #4d4d4d;--ion-color-step-300: #595959;--ion-color-step-350: #656565;--ion-color-step-400: #717171;--ion-color-step-450: #7d7d7d;--ion-color-step-500: #898989;--ion-color-step-550: #949494;--ion-color-step-600: #a0a0a0;--ion-color-step-650: #acacac;--ion-color-step-700: #b8b8b8;--ion-color-step-750: #c4c4c4;--ion-color-step-800: #d0d0d0;--ion-color-step-850: #dbdbdb;--ion-color-step-900: #e7e7e7;--ion-color-step-950: #f3f3f3;--ion-item-background: #1e1e1e;--ion-toolbar-background: #1f1f1f;--ion-tab-bar-background: #1f1f1f;--ion-card-background: #1e1e1e}}body.dark{--ion-color-primary: #f99e00;--ion-color-primary-rgb: 249, 158, 0;--ion-color-primary-contrast: #000000;--ion-color-primary-contrast-rgb: 0, 0, 0;--ion-color-primary-shade: #db8b00;--ion-color-primary-tint: #faa81a;--ion-color-secondary: #111111;--ion-color-secondary-rgb: 17, 17, 17;--ion-color-secondary-contrast: #ffffff;--ion-color-secondary-contrast-rgb: 255, 255, 255;--ion-color-secondary-shade: #0f0f0f;--ion-color-secondary-tint: #292929;--ion-color-tertiary: #5260ff;--ion-color-tertiary-rgb: 82, 96, 255;--ion-color-tertiary-contrast: #ffffff;--ion-color-tertiary-contrast-rgb: 255, 255, 255;--ion-color-tertiary-shade: #4854e0;--ion-color-tertiary-tint: #6370ff;--ion-color-success: #2dd36f;--ion-color-success-rgb: 45, 211, 111;--ion-color-success-contrast: #ffffff;--ion-color-success-contrast-rgb: 255, 255, 255;--ion-color-success-shade: #28ba62;--ion-color-success-tint: #42d77d;--ion-color-warning: #ffc409;--ion-color-warning-rgb: 255, 196, 9;--ion-color-warning-contrast: #000000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0ac08;--ion-color-warning-tint: #ffca22;--ion-color-danger: #eb445a;--ion-color-danger-rgb: 235, 68, 90;--ion-color-danger-contrast: #ffffff;--ion-color-danger-contrast-rgb: 255, 255, 255;--ion-color-danger-shade: #cf3c4f;--ion-color-danger-tint: #ed576b;--ion-color-dark: #222428;--ion-color-dark-rgb: 34, 36, 40;--ion-color-dark-contrast: #ffffff;--ion-color-dark-contrast-rgb: 255, 255, 255;--ion-color-dark-shade: #1e2023;--ion-color-dark-tint: #383a3e;--ion-color-medium: #aaa;--ion-color-medium-rgb: 146, 148, 156;--ion-color-medium-contrast: #ffffff;--ion-color-medium-contrast-rgb: 255, 255, 255;--ion-color-medium-shade: #808289;--ion-color-medium-tint: #9d9fa6;--ion-color-light: #f4f5f8;--ion-color-light-rgb: 244, 245, 248;--ion-color-light-contrast: #000000;--ion-color-light-contrast-rgb: 0, 0, 0;--ion-color-light-shade: #d7d8da;--ion-color-light-tint: #f5f6f9;--ion-toolbar-background: transparent}.ios body.dark{--ion-background-color: #000000;--ion-background-color-rgb: 0, 0, 0;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-color-step-50: #0d0d0d;--ion-color-step-100: #1a1a1a;--ion-color-step-150: #262626;--ion-color-step-200: #333333;--ion-color-step-250: #404040;--ion-color-step-300: #4d4d4d;--ion-color-step-350: #595959;--ion-color-step-400: #666666;--ion-color-step-450: #737373;--ion-color-step-500: #808080;--ion-color-step-550: #8c8c8c;--ion-color-step-600: #999999;--ion-color-step-650: #a6a6a6;--ion-color-step-700: #b3b3b3;--ion-color-step-750: #bfbfbf;--ion-color-step-800: #cccccc;--ion-color-step-850: #d9d9d9;--ion-color-step-900: #e6e6e6;--ion-color-step-950: #f2f2f2;--ion-item-background: #000000;--ion-card-background: #1c1c1d}.ios ion-modal{--ion-background-color: var(--ion-color-step-100);--ion-toolbar-background: var(--ion-color-step-150);--ion-toolbar-border-color: var(--ion-color-step-250)}.md body.dark{--ion-background-color: #121212;--ion-background-color-rgb: 18, 18, 18;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-border-color: #222222;--ion-color-step-50: #1e1e1e;--ion-color-step-100: #2a2a2a;--ion-color-step-150: #363636;--ion-color-step-200: #414141;--ion-color-step-250: #4d4d4d;--ion-color-step-300: #595959;--ion-color-step-350: #656565;--ion-color-step-400: #717171;--ion-color-step-450: #7d7d7d;--ion-color-step-500: #898989;--ion-color-step-550: #949494;--ion-color-step-600: #a0a0a0;--ion-color-step-650: #acacac;--ion-color-step-700: #b8b8b8;--ion-color-step-750: #c4c4c4;--ion-color-step-800: #d0d0d0;--ion-color-step-850: #dbdbdb;--ion-color-step-900: #e7e7e7;--ion-color-step-950: #f3f3f3;--ion-item-background: #1e1e1e;--ion-toolbar-background: #1f1f1f;--ion-tab-bar-background: #1f1f1f;--ion-card-background: #1e1e1e}.mat-ripple{overflow:hidden;position:relative}.mat-ripple:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded{overflow:visible}.mat-ripple-element{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0,0,.2,1);transform:scale3d(0,0,0);background-color:var(--mat-ripple-color, rgba(0, 0, 0, .1))}.cdk-high-contrast-active .mat-ripple-element,.cdk-drag-preview .mat-ripple-element,.cdk-drag-placeholder .mat-ripple-element{display:none}.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}.cdk-overlay-container,.cdk-global-overlay-wrapper{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container{position:fixed;z-index:1000}.cdk-overlay-container:empty{display:none}.cdk-global-overlay-wrapper{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane{position:absolute;pointer-events:auto;box-sizing:border-box;z-index:1000;display:flex;max-width:100%;max-height:100%}.cdk-overlay-backdrop{position:absolute;inset:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:transparent;transition:opacity .4s cubic-bezier(.25,.8,.25,1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:1}.cdk-high-contrast-active .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:.6}.cdk-overlay-dark-backdrop{background:#00000052}.cdk-overlay-transparent-backdrop{transition:visibility 1ms linear,opacity 1ms linear;visibility:hidden;opacity:1}.cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing{opacity:0;visibility:visible}.cdk-overlay-backdrop-noop-animation{transition:none}.cdk-overlay-connected-position-bounding-box{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock{position:fixed;width:100%;overflow-y:scroll}textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0!important;box-sizing:content-box!important;height:auto!important;overflow:hidden!important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0!important;box-sizing:content-box!important;height:0!important}@keyframes cdk-text-field-autofill-start{}@keyframes cdk-text-field-autofill-end{}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}.mat-focus-indicator{position:relative}.mat-focus-indicator:before{inset:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border:var(--mat-focus-indicator-border-width, 3px) var(--mat-focus-indicator-border-style, solid) var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus:before{content:""}.cdk-high-contrast-active{--mat-focus-indicator-display: block}.mat-mdc-focus-indicator{position:relative}.mat-mdc-focus-indicator:before{inset:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-mdc-focus-indicator-display, none);border:var(--mat-mdc-focus-indicator-border-width, 3px) var(--mat-mdc-focus-indicator-border-style, solid) var(--mat-mdc-focus-indicator-border-color, transparent);border-radius:var(--mat-mdc-focus-indicator-border-radius, 4px)}.mat-mdc-focus-indicator:focus:before{content:""}.cdk-high-contrast-active{--mat-mdc-focus-indicator-display: block}.mat-app-background{background-color:var(--mat-app-background-color, var(--mat-app-background, transparent));color:var(--mat-app-text-color, var(--mat-app-on-background, inherit))}.mat-elevation-z0,.mat-mdc-elevation-specific.mat-elevation-z0{box-shadow:var(--mat-app-elevation-shadow-level-0, none)}.mat-elevation-z1,.mat-mdc-elevation-specific.mat-elevation-z1{box-shadow:var(--mat-app-elevation-shadow-level-1, none)}.mat-elevation-z2,.mat-mdc-elevation-specific.mat-elevation-z2{box-shadow:var(--mat-app-elevation-shadow-level-2, none)}.mat-elevation-z3,.mat-mdc-elevation-specific.mat-elevation-z3{box-shadow:var(--mat-app-elevation-shadow-level-3, none)}.mat-elevation-z4,.mat-mdc-elevation-specific.mat-elevation-z4{box-shadow:var(--mat-app-elevation-shadow-level-4, none)}.mat-elevation-z5,.mat-mdc-elevation-specific.mat-elevation-z5{box-shadow:var(--mat-app-elevation-shadow-level-5, none)}.mat-elevation-z6,.mat-mdc-elevation-specific.mat-elevation-z6{box-shadow:var(--mat-app-elevation-shadow-level-6, none)}.mat-elevation-z7,.mat-mdc-elevation-specific.mat-elevation-z7{box-shadow:var(--mat-app-elevation-shadow-level-7, none)}.mat-elevation-z8,.mat-mdc-elevation-specific.mat-elevation-z8{box-shadow:var(--mat-app-elevation-shadow-level-8, none)}.mat-elevation-z9,.mat-mdc-elevation-specific.mat-elevation-z9{box-shadow:var(--mat-app-elevation-shadow-level-9, none)}.mat-elevation-z10,.mat-mdc-elevation-specific.mat-elevation-z10{box-shadow:var(--mat-app-elevation-shadow-level-10, none)}.mat-elevation-z11,.mat-mdc-elevation-specific.mat-elevation-z11{box-shadow:var(--mat-app-elevation-shadow-level-11, none)}.mat-elevation-z12,.mat-mdc-elevation-specific.mat-elevation-z12{box-shadow:var(--mat-app-elevation-shadow-level-12, none)}.mat-elevation-z13,.mat-mdc-elevation-specific.mat-elevation-z13{box-shadow:var(--mat-app-elevation-shadow-level-13, none)}.mat-elevation-z14,.mat-mdc-elevation-specific.mat-elevation-z14{box-shadow:var(--mat-app-elevation-shadow-level-14, none)}.mat-elevation-z15,.mat-mdc-elevation-specific.mat-elevation-z15{box-shadow:var(--mat-app-elevation-shadow-level-15, none)}.mat-elevation-z16,.mat-mdc-elevation-specific.mat-elevation-z16{box-shadow:var(--mat-app-elevation-shadow-level-16, none)}.mat-elevation-z17,.mat-mdc-elevation-specific.mat-elevation-z17{box-shadow:var(--mat-app-elevation-shadow-level-17, none)}.mat-elevation-z18,.mat-mdc-elevation-specific.mat-elevation-z18{box-shadow:var(--mat-app-elevation-shadow-level-18, none)}.mat-elevation-z19,.mat-mdc-elevation-specific.mat-elevation-z19{box-shadow:var(--mat-app-elevation-shadow-level-19, none)}.mat-elevation-z20,.mat-mdc-elevation-specific.mat-elevation-z20{box-shadow:var(--mat-app-elevation-shadow-level-20, none)}.mat-elevation-z21,.mat-mdc-elevation-specific.mat-elevation-z21{box-shadow:var(--mat-app-elevation-shadow-level-21, none)}.mat-elevation-z22,.mat-mdc-elevation-specific.mat-elevation-z22{box-shadow:var(--mat-app-elevation-shadow-level-22, none)}.mat-elevation-z23,.mat-mdc-elevation-specific.mat-elevation-z23{box-shadow:var(--mat-app-elevation-shadow-level-23, none)}.mat-elevation-z24,.mat-mdc-elevation-specific.mat-elevation-z24{box-shadow:var(--mat-app-elevation-shadow-level-24, none)}.mat-theme-loaded-marker{display:none}html{--mat-ripple-color: rgba(0, 0, 0, .1)}html{--mat-option-selected-state-label-text-color: #ff9800;--mat-option-label-text-color: rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color: rgba(0, 0, 0, .04)}.mat-accent{--mat-option-selected-state-label-text-color: #424242;--mat-option-label-text-color: rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color: rgba(0, 0, 0, .04)}.mat-warn{--mat-option-selected-state-label-text-color: #f44336;--mat-option-label-text-color: rgba(0, 0, 0, .87);--mat-option-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-option-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-option-selected-state-layer-color: rgba(0, 0, 0, .04)}html{--mat-optgroup-label-text-color: rgba(0, 0, 0, .87)}.mat-primary{--mat-full-pseudo-checkbox-selected-icon-color: #ff9800;--mat-full-pseudo-checkbox-selected-checkmark-color: #fafafa;--mat-full-pseudo-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color: #fafafa;--mat-full-pseudo-checkbox-disabled-unselected-icon-color: #b0b0b0;--mat-full-pseudo-checkbox-disabled-selected-icon-color: #b0b0b0;--mat-minimal-pseudo-checkbox-selected-checkmark-color: #ff9800;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: #b0b0b0}html,.mat-accent{--mat-full-pseudo-checkbox-selected-icon-color: #424242;--mat-full-pseudo-checkbox-selected-checkmark-color: #fafafa;--mat-full-pseudo-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color: #fafafa;--mat-full-pseudo-checkbox-disabled-unselected-icon-color: #b0b0b0;--mat-full-pseudo-checkbox-disabled-selected-icon-color: #b0b0b0;--mat-minimal-pseudo-checkbox-selected-checkmark-color: #424242;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: #b0b0b0}.mat-warn{--mat-full-pseudo-checkbox-selected-icon-color: #f44336;--mat-full-pseudo-checkbox-selected-checkmark-color: #fafafa;--mat-full-pseudo-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mat-full-pseudo-checkbox-disabled-selected-checkmark-color: #fafafa;--mat-full-pseudo-checkbox-disabled-unselected-icon-color: #b0b0b0;--mat-full-pseudo-checkbox-disabled-selected-icon-color: #b0b0b0;--mat-minimal-pseudo-checkbox-selected-checkmark-color: #f44336;--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color: #b0b0b0}html{--mat-app-background-color: #fafafa;--mat-app-text-color: rgba(0, 0, 0, .87);--mat-app-elevation-shadow-level-0: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-1: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-2: 0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-3: 0px 3px 3px -2px rgba(0, 0, 0, .2), 0px 3px 4px 0px rgba(0, 0, 0, .14), 0px 1px 8px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-4: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-5: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 5px 8px 0px rgba(0, 0, 0, .14), 0px 1px 14px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-6: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-7: 0px 4px 5px -2px rgba(0, 0, 0, .2), 0px 7px 10px 1px rgba(0, 0, 0, .14), 0px 2px 16px 1px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-8: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-9: 0px 5px 6px -3px rgba(0, 0, 0, .2), 0px 9px 12px 1px rgba(0, 0, 0, .14), 0px 3px 16px 2px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-10: 0px 6px 6px -3px rgba(0, 0, 0, .2), 0px 10px 14px 1px rgba(0, 0, 0, .14), 0px 4px 18px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-11: 0px 6px 7px -4px rgba(0, 0, 0, .2), 0px 11px 15px 1px rgba(0, 0, 0, .14), 0px 4px 20px 3px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-12: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-13: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 13px 19px 2px rgba(0, 0, 0, .14), 0px 5px 24px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-14: 0px 7px 9px -4px rgba(0, 0, 0, .2), 0px 14px 21px 2px rgba(0, 0, 0, .14), 0px 5px 26px 4px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-15: 0px 8px 9px -5px rgba(0, 0, 0, .2), 0px 15px 22px 2px rgba(0, 0, 0, .14), 0px 6px 28px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-16: 0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-17: 0px 8px 11px -5px rgba(0, 0, 0, .2), 0px 17px 26px 2px rgba(0, 0, 0, .14), 0px 6px 32px 5px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-18: 0px 9px 11px -5px rgba(0, 0, 0, .2), 0px 18px 28px 2px rgba(0, 0, 0, .14), 0px 7px 34px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-19: 0px 9px 12px -6px rgba(0, 0, 0, .2), 0px 19px 29px 2px rgba(0, 0, 0, .14), 0px 7px 36px 6px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-20: 0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 20px 31px 3px rgba(0, 0, 0, .14), 0px 8px 38px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-21: 0px 10px 13px -6px rgba(0, 0, 0, .2), 0px 21px 33px 3px rgba(0, 0, 0, .14), 0px 8px 40px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-22: 0px 10px 14px -6px rgba(0, 0, 0, .2), 0px 22px 35px 3px rgba(0, 0, 0, .14), 0px 8px 42px 7px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-23: 0px 11px 14px -7px rgba(0, 0, 0, .2), 0px 23px 36px 3px rgba(0, 0, 0, .14), 0px 9px 44px 8px rgba(0, 0, 0, .12);--mat-app-elevation-shadow-level-24: 0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html{--mat-option-label-text-font: Roboto, sans-serif;--mat-option-label-text-line-height: 24px;--mat-option-label-text-size: 16px;--mat-option-label-text-tracking: .03125em;--mat-option-label-text-weight: 400}html{--mat-optgroup-label-text-font: Roboto, sans-serif;--mat-optgroup-label-text-line-height: 24px;--mat-optgroup-label-text-size: 16px;--mat-optgroup-label-text-tracking: .03125em;--mat-optgroup-label-text-weight: 400}html{--mdc-elevated-card-container-shape: 4px;--mdc-outlined-card-container-shape: 4px;--mdc-outlined-card-outline-width: 1px}html{--mdc-elevated-card-container-color: white;--mdc-elevated-card-container-elevation: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-outlined-card-container-color: white;--mdc-outlined-card-outline-color: rgba(0, 0, 0, .12);--mdc-outlined-card-container-elevation: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mat-card-subtitle-text-color: rgba(0, 0, 0, .54)}html{--mat-card-title-text-font: Roboto, sans-serif;--mat-card-title-text-line-height: 32px;--mat-card-title-text-size: 20px;--mat-card-title-text-tracking: .0125em;--mat-card-title-text-weight: 500;--mat-card-subtitle-text-font: Roboto, sans-serif;--mat-card-subtitle-text-line-height: 22px;--mat-card-subtitle-text-size: 14px;--mat-card-subtitle-text-tracking: .0071428571em;--mat-card-subtitle-text-weight: 500}html{--mdc-linear-progress-active-indicator-height: 4px;--mdc-linear-progress-track-height: 4px;--mdc-linear-progress-track-shape: 0}.mat-mdc-progress-bar{--mdc-linear-progress-active-indicator-color: #ff9800;--mdc-linear-progress-track-color: rgba(255, 152, 0, .25)}.mat-mdc-progress-bar.mat-accent{--mdc-linear-progress-active-indicator-color: #424242;--mdc-linear-progress-track-color: rgba(66, 66, 66, .25)}.mat-mdc-progress-bar.mat-warn{--mdc-linear-progress-active-indicator-color: #f44336;--mdc-linear-progress-track-color: rgba(244, 67, 54, .25)}html{--mdc-plain-tooltip-container-shape: 4px;--mdc-plain-tooltip-supporting-text-line-height: 16px}html{--mdc-plain-tooltip-container-color: #616161;--mdc-plain-tooltip-supporting-text-color: #fff}html{--mdc-plain-tooltip-supporting-text-font: Roboto, sans-serif;--mdc-plain-tooltip-supporting-text-size: 12px;--mdc-plain-tooltip-supporting-text-weight: 400;--mdc-plain-tooltip-supporting-text-tracking: .0333333333em}html{--mdc-filled-text-field-active-indicator-height: 1px;--mdc-filled-text-field-focus-active-indicator-height: 2px;--mdc-filled-text-field-container-shape: 4px;--mdc-outlined-text-field-outline-width: 1px;--mdc-outlined-text-field-focus-outline-width: 2px;--mdc-outlined-text-field-container-shape: 4px}html{--mdc-filled-text-field-caret-color: #ff9800;--mdc-filled-text-field-focus-active-indicator-color: #ff9800;--mdc-filled-text-field-focus-label-text-color: rgba(255, 152, 0, .87);--mdc-filled-text-field-container-color: whitesmoke;--mdc-filled-text-field-disabled-container-color: #fafafa;--mdc-filled-text-field-label-text-color: rgba(0, 0, 0, .6);--mdc-filled-text-field-hover-label-text-color: rgba(0, 0, 0, .6);--mdc-filled-text-field-disabled-label-text-color: rgba(0, 0, 0, .38);--mdc-filled-text-field-input-text-color: rgba(0, 0, 0, .87);--mdc-filled-text-field-disabled-input-text-color: rgba(0, 0, 0, .38);--mdc-filled-text-field-input-text-placeholder-color: rgba(0, 0, 0, .6);--mdc-filled-text-field-error-hover-label-text-color: #f44336;--mdc-filled-text-field-error-focus-label-text-color: #f44336;--mdc-filled-text-field-error-label-text-color: #f44336;--mdc-filled-text-field-error-caret-color: #f44336;--mdc-filled-text-field-active-indicator-color: rgba(0, 0, 0, .42);--mdc-filled-text-field-disabled-active-indicator-color: rgba(0, 0, 0, .06);--mdc-filled-text-field-hover-active-indicator-color: rgba(0, 0, 0, .87);--mdc-filled-text-field-error-active-indicator-color: #f44336;--mdc-filled-text-field-error-focus-active-indicator-color: #f44336;--mdc-filled-text-field-error-hover-active-indicator-color: #f44336;--mdc-outlined-text-field-caret-color: #ff9800;--mdc-outlined-text-field-focus-outline-color: #ff9800;--mdc-outlined-text-field-focus-label-text-color: rgba(255, 152, 0, .87);--mdc-outlined-text-field-label-text-color: rgba(0, 0, 0, .6);--mdc-outlined-text-field-hover-label-text-color: rgba(0, 0, 0, .6);--mdc-outlined-text-field-disabled-label-text-color: rgba(0, 0, 0, .38);--mdc-outlined-text-field-input-text-color: rgba(0, 0, 0, .87);--mdc-outlined-text-field-disabled-input-text-color: rgba(0, 0, 0, .38);--mdc-outlined-text-field-input-text-placeholder-color: rgba(0, 0, 0, .6);--mdc-outlined-text-field-error-caret-color: #f44336;--mdc-outlined-text-field-error-focus-label-text-color: #f44336;--mdc-outlined-text-field-error-label-text-color: #f44336;--mdc-outlined-text-field-error-hover-label-text-color: #f44336;--mdc-outlined-text-field-outline-color: rgba(0, 0, 0, .38);--mdc-outlined-text-field-disabled-outline-color: rgba(0, 0, 0, .06);--mdc-outlined-text-field-hover-outline-color: rgba(0, 0, 0, .87);--mdc-outlined-text-field-error-focus-outline-color: #f44336;--mdc-outlined-text-field-error-hover-outline-color: #f44336;--mdc-outlined-text-field-error-outline-color: #f44336;--mat-form-field-focus-select-arrow-color: rgba(255, 152, 0, .87);--mat-form-field-disabled-input-text-placeholder-color: rgba(0, 0, 0, .38);--mat-form-field-state-layer-color: rgba(0, 0, 0, .87);--mat-form-field-error-text-color: #f44336;--mat-form-field-select-option-text-color: inherit;--mat-form-field-select-disabled-option-text-color: GrayText;--mat-form-field-leading-icon-color: unset;--mat-form-field-disabled-leading-icon-color: unset;--mat-form-field-trailing-icon-color: unset;--mat-form-field-disabled-trailing-icon-color: unset;--mat-form-field-error-focus-trailing-icon-color: unset;--mat-form-field-error-hover-trailing-icon-color: unset;--mat-form-field-error-trailing-icon-color: unset;--mat-form-field-enabled-select-arrow-color: rgba(0, 0, 0, .54);--mat-form-field-disabled-select-arrow-color: rgba(0, 0, 0, .38);--mat-form-field-hover-state-layer-opacity: .04;--mat-form-field-focus-state-layer-opacity: .08}.mat-mdc-form-field.mat-accent{--mdc-filled-text-field-caret-color: #424242;--mdc-filled-text-field-focus-active-indicator-color: #424242;--mdc-filled-text-field-focus-label-text-color: rgba(66, 66, 66, .87);--mdc-outlined-text-field-caret-color: #424242;--mdc-outlined-text-field-focus-outline-color: #424242;--mdc-outlined-text-field-focus-label-text-color: rgba(66, 66, 66, .87);--mat-form-field-focus-select-arrow-color: rgba(66, 66, 66, .87)}.mat-mdc-form-field.mat-warn{--mdc-filled-text-field-caret-color: #f44336;--mdc-filled-text-field-focus-active-indicator-color: #f44336;--mdc-filled-text-field-focus-label-text-color: rgba(244, 67, 54, .87);--mdc-outlined-text-field-caret-color: #f44336;--mdc-outlined-text-field-focus-outline-color: #f44336;--mdc-outlined-text-field-focus-label-text-color: rgba(244, 67, 54, .87);--mat-form-field-focus-select-arrow-color: rgba(244, 67, 54, .87)}html{--mat-form-field-container-height: 56px;--mat-form-field-filled-label-display: block;--mat-form-field-container-vertical-padding: 16px;--mat-form-field-filled-with-label-container-padding-top: 24px;--mat-form-field-filled-with-label-container-padding-bottom: 8px}html{--mdc-filled-text-field-label-text-font: Roboto, sans-serif;--mdc-filled-text-field-label-text-size: 16px;--mdc-filled-text-field-label-text-tracking: .03125em;--mdc-filled-text-field-label-text-weight: 400;--mdc-outlined-text-field-label-text-font: Roboto, sans-serif;--mdc-outlined-text-field-label-text-size: 16px;--mdc-outlined-text-field-label-text-tracking: .03125em;--mdc-outlined-text-field-label-text-weight: 400;--mat-form-field-container-text-font: Roboto, sans-serif;--mat-form-field-container-text-line-height: 24px;--mat-form-field-container-text-size: 16px;--mat-form-field-container-text-tracking: .03125em;--mat-form-field-container-text-weight: 400;--mat-form-field-outlined-label-text-populated-size: 16px;--mat-form-field-subscript-text-font: Roboto, sans-serif;--mat-form-field-subscript-text-line-height: 20px;--mat-form-field-subscript-text-size: 12px;--mat-form-field-subscript-text-tracking: .0333333333em;--mat-form-field-subscript-text-weight: 400}html{--mat-select-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color: rgba(0, 0, 0, .38);--mat-select-placeholder-text-color: rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color: rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color: rgba(0, 0, 0, .38);--mat-select-focused-arrow-color: rgba(255, 152, 0, .87);--mat-select-invalid-arrow-color: rgba(244, 67, 54, .87)}html .mat-mdc-form-field.mat-accent{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color: rgba(0, 0, 0, .38);--mat-select-placeholder-text-color: rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color: rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color: rgba(0, 0, 0, .38);--mat-select-focused-arrow-color: rgba(66, 66, 66, .87);--mat-select-invalid-arrow-color: rgba(244, 67, 54, .87)}html .mat-mdc-form-field.mat-warn{--mat-select-panel-background-color: white;--mat-select-enabled-trigger-text-color: rgba(0, 0, 0, .87);--mat-select-disabled-trigger-text-color: rgba(0, 0, 0, .38);--mat-select-placeholder-text-color: rgba(0, 0, 0, .6);--mat-select-enabled-arrow-color: rgba(0, 0, 0, .54);--mat-select-disabled-arrow-color: rgba(0, 0, 0, .38);--mat-select-focused-arrow-color: rgba(244, 67, 54, .87);--mat-select-invalid-arrow-color: rgba(244, 67, 54, .87)}html{--mat-select-arrow-transform: translateY(-8px)}html{--mat-select-trigger-text-font: Roboto, sans-serif;--mat-select-trigger-text-line-height: 24px;--mat-select-trigger-text-size: 16px;--mat-select-trigger-text-tracking: .03125em;--mat-select-trigger-text-weight: 400}html{--mat-autocomplete-container-shape: 4px;--mat-autocomplete-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12)}html{--mat-autocomplete-background-color: white}html{--mdc-dialog-container-shape: 4px;--mat-dialog-container-elevation-shadow: 0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12);--mat-dialog-container-max-width: 80vw;--mat-dialog-container-small-max-width: 80vw;--mat-dialog-container-min-width: 0;--mat-dialog-actions-alignment: start;--mat-dialog-actions-padding: 8px;--mat-dialog-content-padding: 20px 24px;--mat-dialog-with-actions-content-padding: 20px 24px;--mat-dialog-headline-padding: 0 24px 9px}html{--mdc-dialog-container-color: white;--mdc-dialog-subhead-color: rgba(0, 0, 0, .87);--mdc-dialog-supporting-text-color: rgba(0, 0, 0, .6)}html{--mdc-dialog-subhead-font: Roboto, sans-serif;--mdc-dialog-subhead-line-height: 32px;--mdc-dialog-subhead-size: 20px;--mdc-dialog-subhead-weight: 500;--mdc-dialog-subhead-tracking: .0125em;--mdc-dialog-supporting-text-font: Roboto, sans-serif;--mdc-dialog-supporting-text-line-height: 24px;--mdc-dialog-supporting-text-size: 16px;--mdc-dialog-supporting-text-weight: 400;--mdc-dialog-supporting-text-tracking: .03125em}.mat-mdc-standard-chip{--mdc-chip-container-shape-radius: 16px;--mdc-chip-with-avatar-avatar-shape-radius: 14px;--mdc-chip-with-avatar-avatar-size: 28px;--mdc-chip-with-icon-icon-size: 18px;--mdc-chip-outline-width: 0;--mdc-chip-outline-color: transparent;--mdc-chip-disabled-outline-color: transparent;--mdc-chip-focus-outline-color: transparent;--mdc-chip-hover-state-layer-opacity: .04;--mdc-chip-with-avatar-disabled-avatar-opacity: 1;--mdc-chip-flat-selected-outline-width: 0;--mdc-chip-selected-hover-state-layer-opacity: .04;--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity: 1;--mdc-chip-with-icon-disabled-icon-opacity: 1;--mat-chip-disabled-container-opacity: .4;--mat-chip-trailing-action-opacity: .54;--mat-chip-trailing-action-focus-opacity: 1;--mat-chip-trailing-action-state-layer-color: transparent;--mat-chip-selected-trailing-action-state-layer-color: transparent;--mat-chip-trailing-action-hover-state-layer-opacity: 0;--mat-chip-trailing-action-focus-state-layer-opacity: 0}.mat-mdc-standard-chip{--mdc-chip-disabled-label-text-color: #212121;--mdc-chip-elevated-container-color: #e0e0e0;--mdc-chip-elevated-selected-container-color: #e0e0e0;--mdc-chip-elevated-disabled-container-color: #e0e0e0;--mdc-chip-flat-disabled-selected-container-color: #e0e0e0;--mdc-chip-focus-state-layer-color: black;--mdc-chip-hover-state-layer-color: black;--mdc-chip-selected-hover-state-layer-color: black;--mdc-chip-focus-state-layer-opacity: .12;--mdc-chip-selected-focus-state-layer-color: black;--mdc-chip-selected-focus-state-layer-opacity: .12;--mdc-chip-label-text-color: #212121;--mdc-chip-selected-label-text-color: #212121;--mdc-chip-with-icon-icon-color: #212121;--mdc-chip-with-icon-disabled-icon-color: #212121;--mdc-chip-with-icon-selected-icon-color: #212121;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color: #212121;--mdc-chip-with-trailing-icon-trailing-icon-color: #212121;--mat-chip-selected-disabled-trailing-icon-color: #212121;--mat-chip-selected-trailing-icon-color: #212121}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-primary,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-primary{--mdc-chip-disabled-label-text-color: rgba(0, 0, 0, .87);--mdc-chip-elevated-container-color: #ff9800;--mdc-chip-elevated-selected-container-color: #ff9800;--mdc-chip-elevated-disabled-container-color: #ff9800;--mdc-chip-flat-disabled-selected-container-color: #ff9800;--mdc-chip-focus-state-layer-color: black;--mdc-chip-hover-state-layer-color: black;--mdc-chip-selected-hover-state-layer-color: black;--mdc-chip-focus-state-layer-opacity: .12;--mdc-chip-selected-focus-state-layer-color: black;--mdc-chip-selected-focus-state-layer-opacity: .12;--mdc-chip-label-text-color: rgba(0, 0, 0, .87);--mdc-chip-selected-label-text-color: rgba(0, 0, 0, .87);--mdc-chip-with-icon-icon-color: rgba(0, 0, 0, .87);--mdc-chip-with-icon-disabled-icon-color: rgba(0, 0, 0, .87);--mdc-chip-with-icon-selected-icon-color: rgba(0, 0, 0, .87);--mdc-chip-with-trailing-icon-disabled-trailing-icon-color: rgba(0, 0, 0, .87);--mdc-chip-with-trailing-icon-trailing-icon-color: rgba(0, 0, 0, .87);--mat-chip-selected-disabled-trailing-icon-color: rgba(0, 0, 0, .87);--mat-chip-selected-trailing-icon-color: rgba(0, 0, 0, .87)}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-accent,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-accent{--mdc-chip-disabled-label-text-color: white;--mdc-chip-elevated-container-color: #424242;--mdc-chip-elevated-selected-container-color: #424242;--mdc-chip-elevated-disabled-container-color: #424242;--mdc-chip-flat-disabled-selected-container-color: #424242;--mdc-chip-focus-state-layer-color: black;--mdc-chip-hover-state-layer-color: black;--mdc-chip-selected-hover-state-layer-color: black;--mdc-chip-focus-state-layer-opacity: .12;--mdc-chip-selected-focus-state-layer-color: black;--mdc-chip-selected-focus-state-layer-opacity: .12;--mdc-chip-label-text-color: white;--mdc-chip-selected-label-text-color: white;--mdc-chip-with-icon-icon-color: white;--mdc-chip-with-icon-disabled-icon-color: white;--mdc-chip-with-icon-selected-icon-color: white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color: white;--mdc-chip-with-trailing-icon-trailing-icon-color: white;--mat-chip-selected-disabled-trailing-icon-color: white;--mat-chip-selected-trailing-icon-color: white}.mat-mdc-standard-chip.mat-mdc-chip-selected.mat-warn,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mat-warn{--mdc-chip-disabled-label-text-color: white;--mdc-chip-elevated-container-color: #f44336;--mdc-chip-elevated-selected-container-color: #f44336;--mdc-chip-elevated-disabled-container-color: #f44336;--mdc-chip-flat-disabled-selected-container-color: #f44336;--mdc-chip-focus-state-layer-color: black;--mdc-chip-hover-state-layer-color: black;--mdc-chip-selected-hover-state-layer-color: black;--mdc-chip-focus-state-layer-opacity: .12;--mdc-chip-selected-focus-state-layer-color: black;--mdc-chip-selected-focus-state-layer-opacity: .12;--mdc-chip-label-text-color: white;--mdc-chip-selected-label-text-color: white;--mdc-chip-with-icon-icon-color: white;--mdc-chip-with-icon-disabled-icon-color: white;--mdc-chip-with-icon-selected-icon-color: white;--mdc-chip-with-trailing-icon-disabled-trailing-icon-color: white;--mdc-chip-with-trailing-icon-trailing-icon-color: white;--mat-chip-selected-disabled-trailing-icon-color: white;--mat-chip-selected-trailing-icon-color: white}.mat-mdc-chip.mat-mdc-standard-chip{--mdc-chip-container-height: 32px}.mat-mdc-standard-chip{--mdc-chip-label-text-font: Roboto, sans-serif;--mdc-chip-label-text-line-height: 20px;--mdc-chip-label-text-size: 14px;--mdc-chip-label-text-tracking: .0178571429em;--mdc-chip-label-text-weight: 400}html{--mdc-switch-disabled-selected-icon-opacity: .38;--mdc-switch-disabled-track-opacity: .12;--mdc-switch-disabled-unselected-icon-opacity: .38;--mdc-switch-handle-height: 20px;--mdc-switch-handle-shape: 10px;--mdc-switch-handle-width: 20px;--mdc-switch-selected-icon-size: 18px;--mdc-switch-track-height: 14px;--mdc-switch-track-shape: 7px;--mdc-switch-track-width: 36px;--mdc-switch-unselected-icon-size: 18px;--mdc-switch-selected-focus-state-layer-opacity: .12;--mdc-switch-selected-hover-state-layer-opacity: .04;--mdc-switch-selected-pressed-state-layer-opacity: .1;--mdc-switch-unselected-focus-state-layer-opacity: .12;--mdc-switch-unselected-hover-state-layer-opacity: .04;--mdc-switch-unselected-pressed-state-layer-opacity: .1}html .mat-mdc-slide-toggle{--mat-switch-disabled-selected-handle-opacity: .38;--mat-switch-disabled-unselected-handle-opacity: .38;--mat-switch-unselected-handle-size: 20px;--mat-switch-selected-handle-size: 20px;--mat-switch-pressed-handle-size: 20px;--mat-switch-with-icon-handle-size: 20px;--mat-switch-selected-handle-horizontal-margin: 0;--mat-switch-selected-with-icon-handle-horizontal-margin: 0;--mat-switch-selected-pressed-handle-horizontal-margin: 0;--mat-switch-unselected-handle-horizontal-margin: 0;--mat-switch-unselected-with-icon-handle-horizontal-margin: 0;--mat-switch-unselected-pressed-handle-horizontal-margin: 0;--mat-switch-visible-track-opacity: 1;--mat-switch-hidden-track-opacity: 1;--mat-switch-visible-track-transition: transform 75ms 0ms cubic-bezier(0, 0, .2, 1);--mat-switch-hidden-track-transition: transform 75ms 0ms cubic-bezier(.4, 0, .6, 1);--mat-switch-track-outline-width: 1px;--mat-switch-track-outline-color: transparent;--mat-switch-selected-track-outline-width: 1px;--mat-switch-selected-track-outline-color: transparent;--mat-switch-disabled-unselected-track-outline-width: 1px;--mat-switch-disabled-unselected-track-outline-color: transparent}html{--mdc-switch-selected-focus-state-layer-color: #fb8c00;--mdc-switch-selected-handle-color: #fb8c00;--mdc-switch-selected-hover-state-layer-color: #fb8c00;--mdc-switch-selected-pressed-state-layer-color: #fb8c00;--mdc-switch-selected-focus-handle-color: #e65100;--mdc-switch-selected-hover-handle-color: #e65100;--mdc-switch-selected-pressed-handle-color: #e65100;--mdc-switch-selected-focus-track-color: #ffb74d;--mdc-switch-selected-hover-track-color: #ffb74d;--mdc-switch-selected-pressed-track-color: #ffb74d;--mdc-switch-selected-track-color: #ffb74d;--mdc-switch-disabled-selected-handle-color: #424242;--mdc-switch-disabled-selected-icon-color: #fff;--mdc-switch-disabled-selected-track-color: #424242;--mdc-switch-disabled-unselected-handle-color: #424242;--mdc-switch-disabled-unselected-icon-color: #fff;--mdc-switch-disabled-unselected-track-color: #424242;--mdc-switch-handle-surface-color: #fff;--mdc-switch-selected-icon-color: #fff;--mdc-switch-unselected-focus-handle-color: #212121;--mdc-switch-unselected-focus-state-layer-color: #424242;--mdc-switch-unselected-focus-track-color: #e0e0e0;--mdc-switch-unselected-handle-color: #616161;--mdc-switch-unselected-hover-handle-color: #212121;--mdc-switch-unselected-hover-state-layer-color: #424242;--mdc-switch-unselected-hover-track-color: #e0e0e0;--mdc-switch-unselected-icon-color: #fff;--mdc-switch-unselected-pressed-handle-color: #212121;--mdc-switch-unselected-pressed-state-layer-color: #424242;--mdc-switch-unselected-pressed-track-color: #e0e0e0;--mdc-switch-unselected-track-color: #e0e0e0;--mdc-switch-handle-elevation-shadow: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mdc-switch-disabled-handle-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mdc-switch-disabled-label-text-color: rgba(0, 0, 0, .38)}html .mat-mdc-slide-toggle{--mat-switch-label-text-color: rgba(0, 0, 0, .87)}html .mat-mdc-slide-toggle.mat-accent{--mdc-switch-selected-focus-state-layer-color: #757575;--mdc-switch-selected-handle-color: #757575;--mdc-switch-selected-hover-state-layer-color: #757575;--mdc-switch-selected-pressed-state-layer-color: #757575;--mdc-switch-selected-focus-handle-color: #212121;--mdc-switch-selected-hover-handle-color: #212121;--mdc-switch-selected-pressed-handle-color: #212121;--mdc-switch-selected-focus-track-color: #e0e0e0;--mdc-switch-selected-hover-track-color: #e0e0e0;--mdc-switch-selected-pressed-track-color: #e0e0e0;--mdc-switch-selected-track-color: #e0e0e0}html .mat-mdc-slide-toggle.mat-warn{--mdc-switch-selected-focus-state-layer-color: #e53935;--mdc-switch-selected-handle-color: #e53935;--mdc-switch-selected-hover-state-layer-color: #e53935;--mdc-switch-selected-pressed-state-layer-color: #e53935;--mdc-switch-selected-focus-handle-color: #b71c1c;--mdc-switch-selected-hover-handle-color: #b71c1c;--mdc-switch-selected-pressed-handle-color: #b71c1c;--mdc-switch-selected-focus-track-color: #e57373;--mdc-switch-selected-hover-track-color: #e57373;--mdc-switch-selected-pressed-track-color: #e57373;--mdc-switch-selected-track-color: #e57373}html{--mdc-switch-state-layer-size: 40px}html .mat-mdc-slide-toggle{--mat-switch-label-text-font: Roboto, sans-serif;--mat-switch-label-text-line-height: 20px;--mat-switch-label-text-size: 14px;--mat-switch-label-text-tracking: .0178571429em;--mat-switch-label-text-weight: 400}html{--mdc-radio-disabled-selected-icon-opacity: .38;--mdc-radio-disabled-unselected-icon-opacity: .38;--mdc-radio-state-layer-size: 40px}.mat-mdc-radio-button.mat-primary{--mdc-radio-disabled-selected-icon-color: black;--mdc-radio-disabled-unselected-icon-color: black;--mdc-radio-unselected-hover-icon-color: #212121;--mdc-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54);--mdc-radio-selected-focus-icon-color: #ff9800;--mdc-radio-selected-hover-icon-color: #ff9800;--mdc-radio-selected-icon-color: #ff9800;--mdc-radio-selected-pressed-icon-color: #ff9800;--mat-radio-ripple-color: black;--mat-radio-checked-ripple-color: #ff9800;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-label-text-color: rgba(0, 0, 0, .87)}.mat-mdc-radio-button.mat-accent{--mdc-radio-disabled-selected-icon-color: black;--mdc-radio-disabled-unselected-icon-color: black;--mdc-radio-unselected-hover-icon-color: #212121;--mdc-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54);--mdc-radio-selected-focus-icon-color: #424242;--mdc-radio-selected-hover-icon-color: #424242;--mdc-radio-selected-icon-color: #424242;--mdc-radio-selected-pressed-icon-color: #424242;--mat-radio-ripple-color: black;--mat-radio-checked-ripple-color: #424242;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-label-text-color: rgba(0, 0, 0, .87)}.mat-mdc-radio-button.mat-warn{--mdc-radio-disabled-selected-icon-color: black;--mdc-radio-disabled-unselected-icon-color: black;--mdc-radio-unselected-hover-icon-color: #212121;--mdc-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54);--mdc-radio-selected-focus-icon-color: #f44336;--mdc-radio-selected-hover-icon-color: #f44336;--mdc-radio-selected-icon-color: #f44336;--mdc-radio-selected-pressed-icon-color: #f44336;--mat-radio-ripple-color: black;--mat-radio-checked-ripple-color: #f44336;--mat-radio-disabled-label-color: rgba(0, 0, 0, .38);--mat-radio-label-text-color: rgba(0, 0, 0, .87)}html{--mdc-radio-state-layer-size: 40px;--mat-radio-touch-target-display: block}html{--mat-radio-label-text-font: Roboto, sans-serif;--mat-radio-label-text-line-height: 20px;--mat-radio-label-text-size: 14px;--mat-radio-label-text-tracking: .0178571429em;--mat-radio-label-text-weight: 400}html{--mdc-slider-active-track-height: 6px;--mdc-slider-active-track-shape: 9999px;--mdc-slider-handle-height: 20px;--mdc-slider-handle-shape: 50%;--mdc-slider-handle-width: 20px;--mdc-slider-inactive-track-height: 4px;--mdc-slider-inactive-track-shape: 9999px;--mdc-slider-with-overlap-handle-outline-width: 1px;--mdc-slider-with-tick-marks-active-container-opacity: .6;--mdc-slider-with-tick-marks-container-shape: 50%;--mdc-slider-with-tick-marks-container-size: 2px;--mdc-slider-with-tick-marks-inactive-container-opacity: .6;--mdc-slider-handle-elevation: 0px 2px 1px -1px rgba(0, 0, 0, .2), 0px 1px 1px 0px rgba(0, 0, 0, .14), 0px 1px 3px 0px rgba(0, 0, 0, .12);--mat-slider-value-indicator-width: auto;--mat-slider-value-indicator-height: 32px;--mat-slider-value-indicator-caret-display: block;--mat-slider-value-indicator-border-radius: 4px;--mat-slider-value-indicator-padding: 0 12px;--mat-slider-value-indicator-text-transform: none;--mat-slider-value-indicator-container-transform: translateX(-50%)}html{--mdc-slider-handle-color: #ff9800;--mdc-slider-focus-handle-color: #ff9800;--mdc-slider-hover-handle-color: #ff9800;--mdc-slider-active-track-color: #ff9800;--mdc-slider-inactive-track-color: #ff9800;--mdc-slider-with-tick-marks-inactive-container-color: #ff9800;--mdc-slider-with-tick-marks-active-container-color: rgba(0, 0, 0, .87);--mdc-slider-disabled-active-track-color: #000;--mdc-slider-disabled-handle-color: #000;--mdc-slider-disabled-inactive-track-color: #000;--mdc-slider-label-container-color: #000;--mdc-slider-label-label-text-color: #fff;--mdc-slider-with-overlap-handle-outline-color: #fff;--mdc-slider-with-tick-marks-disabled-container-color: #000;--mat-slider-ripple-color: #ff9800;--mat-slider-hover-state-layer-color: rgba(255, 152, 0, .05);--mat-slider-focus-state-layer-color: rgba(255, 152, 0, .2);--mat-slider-value-indicator-opacity: .6}html .mat-accent{--mdc-slider-handle-color: #424242;--mdc-slider-focus-handle-color: #424242;--mdc-slider-hover-handle-color: #424242;--mdc-slider-active-track-color: #424242;--mdc-slider-inactive-track-color: #424242;--mdc-slider-with-tick-marks-inactive-container-color: #424242;--mdc-slider-with-tick-marks-active-container-color: white;--mat-slider-ripple-color: #424242;--mat-slider-hover-state-layer-color: rgba(66, 66, 66, .05);--mat-slider-focus-state-layer-color: rgba(66, 66, 66, .2)}html .mat-warn{--mdc-slider-handle-color: #f44336;--mdc-slider-focus-handle-color: #f44336;--mdc-slider-hover-handle-color: #f44336;--mdc-slider-active-track-color: #f44336;--mdc-slider-inactive-track-color: #f44336;--mdc-slider-with-tick-marks-inactive-container-color: #f44336;--mdc-slider-with-tick-marks-active-container-color: white;--mat-slider-ripple-color: #f44336;--mat-slider-hover-state-layer-color: rgba(244, 67, 54, .05);--mat-slider-focus-state-layer-color: rgba(244, 67, 54, .2)}html{--mdc-slider-label-label-text-font: Roboto, sans-serif;--mdc-slider-label-label-text-size: 14px;--mdc-slider-label-label-text-line-height: 22px;--mdc-slider-label-label-text-tracking: .0071428571em;--mdc-slider-label-label-text-weight: 500}html{--mat-menu-container-shape: 4px;--mat-menu-divider-bottom-spacing: 0;--mat-menu-divider-top-spacing: 0;--mat-menu-item-spacing: 16px;--mat-menu-item-icon-size: 24px;--mat-menu-item-leading-spacing: 16px;--mat-menu-item-trailing-spacing: 16px;--mat-menu-item-with-icon-leading-spacing: 16px;--mat-menu-item-with-icon-trailing-spacing: 16px;--mat-menu-base-elevation-level: 8}html{--mat-menu-item-label-text-color: rgba(0, 0, 0, .87);--mat-menu-item-icon-color: rgba(0, 0, 0, .87);--mat-menu-item-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-menu-item-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-menu-container-color: white;--mat-menu-divider-color: rgba(0, 0, 0, .12)}html{--mat-menu-item-label-text-font: Roboto, sans-serif;--mat-menu-item-label-text-size: 16px;--mat-menu-item-label-text-tracking: .03125em;--mat-menu-item-label-text-line-height: 24px;--mat-menu-item-label-text-weight: 400}html{--mdc-list-list-item-container-shape: 0;--mdc-list-list-item-leading-avatar-shape: 50%;--mdc-list-list-item-container-color: transparent;--mdc-list-list-item-selected-container-color: transparent;--mdc-list-list-item-leading-avatar-color: transparent;--mdc-list-list-item-leading-icon-size: 24px;--mdc-list-list-item-leading-avatar-size: 40px;--mdc-list-list-item-trailing-icon-size: 24px;--mdc-list-list-item-disabled-state-layer-color: transparent;--mdc-list-list-item-disabled-state-layer-opacity: 0;--mdc-list-list-item-disabled-label-text-opacity: .38;--mdc-list-list-item-disabled-leading-icon-opacity: .38;--mdc-list-list-item-disabled-trailing-icon-opacity: .38;--mat-list-active-indicator-color: transparent;--mat-list-active-indicator-shape: 4px}html{--mdc-list-list-item-label-text-color: rgba(0, 0, 0, .87);--mdc-list-list-item-supporting-text-color: rgba(0, 0, 0, .54);--mdc-list-list-item-leading-icon-color: rgba(0, 0, 0, .38);--mdc-list-list-item-trailing-supporting-text-color: rgba(0, 0, 0, .38);--mdc-list-list-item-trailing-icon-color: rgba(0, 0, 0, .38);--mdc-list-list-item-selected-trailing-icon-color: rgba(0, 0, 0, .38);--mdc-list-list-item-disabled-label-text-color: black;--mdc-list-list-item-disabled-leading-icon-color: black;--mdc-list-list-item-disabled-trailing-icon-color: black;--mdc-list-list-item-hover-label-text-color: rgba(0, 0, 0, .87);--mdc-list-list-item-hover-leading-icon-color: rgba(0, 0, 0, .38);--mdc-list-list-item-hover-trailing-icon-color: rgba(0, 0, 0, .38);--mdc-list-list-item-focus-label-text-color: rgba(0, 0, 0, .87);--mdc-list-list-item-hover-state-layer-color: black;--mdc-list-list-item-hover-state-layer-opacity: .04;--mdc-list-list-item-focus-state-layer-color: black;--mdc-list-list-item-focus-state-layer-opacity: .12}.mdc-list-item__start,.mdc-list-item__end{--mdc-radio-disabled-selected-icon-color: black;--mdc-radio-disabled-unselected-icon-color: black;--mdc-radio-unselected-hover-icon-color: #212121;--mdc-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54);--mdc-radio-selected-focus-icon-color: #ff9800;--mdc-radio-selected-hover-icon-color: #ff9800;--mdc-radio-selected-icon-color: #ff9800;--mdc-radio-selected-pressed-icon-color: #ff9800}.mat-accent .mdc-list-item__start,.mat-accent .mdc-list-item__end{--mdc-radio-disabled-selected-icon-color: black;--mdc-radio-disabled-unselected-icon-color: black;--mdc-radio-unselected-hover-icon-color: #212121;--mdc-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54);--mdc-radio-selected-focus-icon-color: #424242;--mdc-radio-selected-hover-icon-color: #424242;--mdc-radio-selected-icon-color: #424242;--mdc-radio-selected-pressed-icon-color: #424242}.mat-warn .mdc-list-item__start,.mat-warn .mdc-list-item__end{--mdc-radio-disabled-selected-icon-color: black;--mdc-radio-disabled-unselected-icon-color: black;--mdc-radio-unselected-hover-icon-color: #212121;--mdc-radio-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-radio-unselected-pressed-icon-color: rgba(0, 0, 0, .54);--mdc-radio-selected-focus-icon-color: #f44336;--mdc-radio-selected-hover-icon-color: #f44336;--mdc-radio-selected-icon-color: #f44336;--mdc-radio-selected-pressed-icon-color: #f44336}.mat-mdc-list-option{--mdc-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-selected-checkmark-color: black;--mdc-checkbox-selected-focus-icon-color: #ff9800;--mdc-checkbox-selected-hover-icon-color: #ff9800;--mdc-checkbox-selected-icon-color: #ff9800;--mdc-checkbox-selected-pressed-icon-color: #ff9800;--mdc-checkbox-unselected-focus-icon-color: #212121;--mdc-checkbox-unselected-hover-icon-color: #212121;--mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-checkbox-selected-focus-state-layer-color: #ff9800;--mdc-checkbox-selected-hover-state-layer-color: #ff9800;--mdc-checkbox-selected-pressed-state-layer-color: #ff9800;--mdc-checkbox-unselected-focus-state-layer-color: black;--mdc-checkbox-unselected-hover-state-layer-color: black;--mdc-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-list-option.mat-accent{--mdc-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-selected-checkmark-color: white;--mdc-checkbox-selected-focus-icon-color: #424242;--mdc-checkbox-selected-hover-icon-color: #424242;--mdc-checkbox-selected-icon-color: #424242;--mdc-checkbox-selected-pressed-icon-color: #424242;--mdc-checkbox-unselected-focus-icon-color: #212121;--mdc-checkbox-unselected-hover-icon-color: #212121;--mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-checkbox-selected-focus-state-layer-color: #424242;--mdc-checkbox-selected-hover-state-layer-color: #424242;--mdc-checkbox-selected-pressed-state-layer-color: #424242;--mdc-checkbox-unselected-focus-state-layer-color: black;--mdc-checkbox-unselected-hover-state-layer-color: black;--mdc-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-list-option.mat-warn{--mdc-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-selected-checkmark-color: white;--mdc-checkbox-selected-focus-icon-color: #f44336;--mdc-checkbox-selected-hover-icon-color: #f44336;--mdc-checkbox-selected-icon-color: #f44336;--mdc-checkbox-selected-pressed-icon-color: #f44336;--mdc-checkbox-unselected-focus-icon-color: #212121;--mdc-checkbox-unselected-hover-icon-color: #212121;--mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-checkbox-selected-focus-state-layer-color: #f44336;--mdc-checkbox-selected-hover-state-layer-color: #f44336;--mdc-checkbox-selected-pressed-state-layer-color: #f44336;--mdc-checkbox-unselected-focus-state-layer-color: black;--mdc-checkbox-unselected-hover-state-layer-color: black;--mdc-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected .mdc-list-item__primary-text,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--selected .mdc-list-item__start,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated .mdc-list-item__primary-text,.mat-mdc-list-base.mat-mdc-list-base .mdc-list-item--activated .mdc-list-item__start{color:#ff9800}.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__start,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__content,.mat-mdc-list-base .mdc-list-item--disabled .mdc-list-item__end{opacity:1}html{--mdc-list-list-item-one-line-container-height: 48px;--mdc-list-list-item-two-line-container-height: 64px;--mdc-list-list-item-three-line-container-height: 88px;--mat-list-list-item-leading-icon-start-space: 16px;--mat-list-list-item-leading-icon-end-space: 32px}.mdc-list-item__start,.mdc-list-item__end{--mdc-radio-state-layer-size: 40px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-one-line,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-one-line{height:56px}.mat-mdc-list-item.mdc-list-item--with-leading-avatar.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-checkbox.mdc-list-item--with-two-lines,.mat-mdc-list-item.mdc-list-item--with-leading-icon.mdc-list-item--with-two-lines{height:72px}html{--mdc-list-list-item-label-text-font: Roboto, sans-serif;--mdc-list-list-item-label-text-line-height: 24px;--mdc-list-list-item-label-text-size: 16px;--mdc-list-list-item-label-text-tracking: .03125em;--mdc-list-list-item-label-text-weight: 400;--mdc-list-list-item-supporting-text-font: Roboto, sans-serif;--mdc-list-list-item-supporting-text-line-height: 20px;--mdc-list-list-item-supporting-text-size: 14px;--mdc-list-list-item-supporting-text-tracking: .0178571429em;--mdc-list-list-item-supporting-text-weight: 400;--mdc-list-list-item-trailing-supporting-text-font: Roboto, sans-serif;--mdc-list-list-item-trailing-supporting-text-line-height: 20px;--mdc-list-list-item-trailing-supporting-text-size: 12px;--mdc-list-list-item-trailing-supporting-text-tracking: .0333333333em;--mdc-list-list-item-trailing-supporting-text-weight: 400}.mdc-list-group__subheader{font:400 16px/28px Roboto,sans-serif;letter-spacing:.009375em}html{--mat-paginator-container-text-color: rgba(0, 0, 0, .87);--mat-paginator-container-background-color: white;--mat-paginator-enabled-icon-color: rgba(0, 0, 0, .54);--mat-paginator-disabled-icon-color: rgba(0, 0, 0, .12)}html{--mat-paginator-container-size: 56px;--mat-paginator-form-field-container-height: 40px;--mat-paginator-form-field-container-vertical-padding: 8px;--mat-paginator-touch-target-display: block}html{--mat-paginator-container-text-font: Roboto, sans-serif;--mat-paginator-container-text-line-height: 20px;--mat-paginator-container-text-size: 12px;--mat-paginator-container-text-tracking: .0333333333em;--mat-paginator-container-text-weight: 400;--mat-paginator-select-trigger-text-size: 12px}html{--mdc-secondary-navigation-tab-container-height: 48px;--mdc-tab-indicator-active-indicator-height: 2px;--mdc-tab-indicator-active-indicator-shape: 0;--mat-tab-header-divider-color: transparent;--mat-tab-header-divider-height: 0}.mat-mdc-tab-group,.mat-mdc-tab-nav-bar{--mdc-tab-indicator-active-indicator-color: #ff9800;--mat-tab-header-disabled-ripple-color: rgba(0, 0, 0, .38);--mat-tab-header-pagination-icon-color: black;--mat-tab-header-inactive-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-active-label-text-color: #ff9800;--mat-tab-header-active-ripple-color: #ff9800;--mat-tab-header-inactive-ripple-color: #ff9800;--mat-tab-header-inactive-focus-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-inactive-hover-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-active-focus-label-text-color: #ff9800;--mat-tab-header-active-hover-label-text-color: #ff9800;--mat-tab-header-active-focus-indicator-color: #ff9800;--mat-tab-header-active-hover-indicator-color: #ff9800}.mat-mdc-tab-group.mat-accent,.mat-mdc-tab-nav-bar.mat-accent{--mdc-tab-indicator-active-indicator-color: #424242;--mat-tab-header-disabled-ripple-color: rgba(0, 0, 0, .38);--mat-tab-header-pagination-icon-color: black;--mat-tab-header-inactive-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-active-label-text-color: #424242;--mat-tab-header-active-ripple-color: #424242;--mat-tab-header-inactive-ripple-color: #424242;--mat-tab-header-inactive-focus-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-inactive-hover-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-active-focus-label-text-color: #424242;--mat-tab-header-active-hover-label-text-color: #424242;--mat-tab-header-active-focus-indicator-color: #424242;--mat-tab-header-active-hover-indicator-color: #424242}.mat-mdc-tab-group.mat-warn,.mat-mdc-tab-nav-bar.mat-warn{--mdc-tab-indicator-active-indicator-color: #f44336;--mat-tab-header-disabled-ripple-color: rgba(0, 0, 0, .38);--mat-tab-header-pagination-icon-color: black;--mat-tab-header-inactive-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-active-label-text-color: #f44336;--mat-tab-header-active-ripple-color: #f44336;--mat-tab-header-inactive-ripple-color: #f44336;--mat-tab-header-inactive-focus-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-inactive-hover-label-text-color: rgba(0, 0, 0, .6);--mat-tab-header-active-focus-label-text-color: #f44336;--mat-tab-header-active-hover-label-text-color: #f44336;--mat-tab-header-active-focus-indicator-color: #f44336;--mat-tab-header-active-hover-indicator-color: #f44336}.mat-mdc-tab-group.mat-background-primary,.mat-mdc-tab-nav-bar.mat-background-primary{--mat-tab-header-with-background-background-color: #ff9800;--mat-tab-header-with-background-foreground-color: rgba(0, 0, 0, .87)}.mat-mdc-tab-group.mat-background-accent,.mat-mdc-tab-nav-bar.mat-background-accent{--mat-tab-header-with-background-background-color: #424242;--mat-tab-header-with-background-foreground-color: white}.mat-mdc-tab-group.mat-background-warn,.mat-mdc-tab-nav-bar.mat-background-warn{--mat-tab-header-with-background-background-color: #f44336;--mat-tab-header-with-background-foreground-color: white}.mat-mdc-tab-header{--mdc-secondary-navigation-tab-container-height: 48px}.mat-mdc-tab-header{--mat-tab-header-label-text-font: Roboto, sans-serif;--mat-tab-header-label-text-size: 14px;--mat-tab-header-label-text-tracking: .0892857143em;--mat-tab-header-label-text-line-height: 36px;--mat-tab-header-label-text-weight: 500}html{--mdc-checkbox-disabled-selected-checkmark-color: #fff;--mdc-checkbox-selected-focus-state-layer-opacity: .16;--mdc-checkbox-selected-hover-state-layer-opacity: .04;--mdc-checkbox-selected-pressed-state-layer-opacity: .16;--mdc-checkbox-unselected-focus-state-layer-opacity: .16;--mdc-checkbox-unselected-hover-state-layer-opacity: .04;--mdc-checkbox-unselected-pressed-state-layer-opacity: .16}html{--mdc-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-selected-checkmark-color: white;--mdc-checkbox-selected-focus-icon-color: #424242;--mdc-checkbox-selected-hover-icon-color: #424242;--mdc-checkbox-selected-icon-color: #424242;--mdc-checkbox-selected-pressed-icon-color: #424242;--mdc-checkbox-unselected-focus-icon-color: #212121;--mdc-checkbox-unselected-hover-icon-color: #212121;--mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-checkbox-selected-focus-state-layer-color: #424242;--mdc-checkbox-selected-hover-state-layer-color: #424242;--mdc-checkbox-selected-pressed-state-layer-color: #424242;--mdc-checkbox-unselected-focus-state-layer-color: black;--mdc-checkbox-unselected-hover-state-layer-color: black;--mdc-checkbox-unselected-pressed-state-layer-color: black;--mat-checkbox-disabled-label-color: rgba(0, 0, 0, .38);--mat-checkbox-label-text-color: rgba(0, 0, 0, .87)}.mat-mdc-checkbox.mat-primary{--mdc-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-selected-checkmark-color: black;--mdc-checkbox-selected-focus-icon-color: #ff9800;--mdc-checkbox-selected-hover-icon-color: #ff9800;--mdc-checkbox-selected-icon-color: #ff9800;--mdc-checkbox-selected-pressed-icon-color: #ff9800;--mdc-checkbox-unselected-focus-icon-color: #212121;--mdc-checkbox-unselected-hover-icon-color: #212121;--mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-checkbox-selected-focus-state-layer-color: #ff9800;--mdc-checkbox-selected-hover-state-layer-color: #ff9800;--mdc-checkbox-selected-pressed-state-layer-color: #ff9800;--mdc-checkbox-unselected-focus-state-layer-color: black;--mdc-checkbox-unselected-hover-state-layer-color: black;--mdc-checkbox-unselected-pressed-state-layer-color: black}.mat-mdc-checkbox.mat-warn{--mdc-checkbox-disabled-selected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, .38);--mdc-checkbox-selected-checkmark-color: white;--mdc-checkbox-selected-focus-icon-color: #f44336;--mdc-checkbox-selected-hover-icon-color: #f44336;--mdc-checkbox-selected-icon-color: #f44336;--mdc-checkbox-selected-pressed-icon-color: #f44336;--mdc-checkbox-unselected-focus-icon-color: #212121;--mdc-checkbox-unselected-hover-icon-color: #212121;--mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, .54);--mdc-checkbox-selected-focus-state-layer-color: #f44336;--mdc-checkbox-selected-hover-state-layer-color: #f44336;--mdc-checkbox-selected-pressed-state-layer-color: #f44336;--mdc-checkbox-unselected-focus-state-layer-color: black;--mdc-checkbox-unselected-hover-state-layer-color: black;--mdc-checkbox-unselected-pressed-state-layer-color: black}html{--mdc-checkbox-state-layer-size: 40px;--mat-checkbox-touch-target-display: block}html{--mat-checkbox-label-text-font: Roboto, sans-serif;--mat-checkbox-label-text-line-height: 20px;--mat-checkbox-label-text-size: 14px;--mat-checkbox-label-text-tracking: .0178571429em;--mat-checkbox-label-text-weight: 400}html{--mdc-text-button-container-shape: 4px;--mdc-text-button-keep-touch-target: false;--mdc-filled-button-container-shape: 4px;--mdc-filled-button-keep-touch-target: false;--mdc-protected-button-container-shape: 4px;--mdc-protected-button-container-elevation-shadow: 0px 3px 1px -2px rgba(0, 0, 0, .2), 0px 2px 2px 0px rgba(0, 0, 0, .14), 0px 1px 5px 0px rgba(0, 0, 0, .12);--mdc-protected-button-disabled-container-elevation-shadow: 0px 0px 0px 0px rgba(0, 0, 0, .2), 0px 0px 0px 0px rgba(0, 0, 0, .14), 0px 0px 0px 0px rgba(0, 0, 0, .12);--mdc-protected-button-focus-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mdc-protected-button-hover-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mdc-protected-button-pressed-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-outlined-button-keep-touch-target: false;--mdc-outlined-button-outline-width: 1px;--mdc-outlined-button-container-shape: 4px;--mat-text-button-horizontal-padding: 8px;--mat-text-button-with-icon-horizontal-padding: 8px;--mat-text-button-icon-spacing: 8px;--mat-text-button-icon-offset: 0;--mat-filled-button-horizontal-padding: 16px;--mat-filled-button-icon-spacing: 8px;--mat-filled-button-icon-offset: -4px;--mat-protected-button-horizontal-padding: 16px;--mat-protected-button-icon-spacing: 8px;--mat-protected-button-icon-offset: -4px;--mat-outlined-button-horizontal-padding: 15px;--mat-outlined-button-icon-spacing: 8px;--mat-outlined-button-icon-offset: -4px}html{--mdc-text-button-label-text-color: black;--mdc-text-button-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-text-button-state-layer-color: black;--mat-text-button-disabled-state-layer-color: black;--mat-text-button-ripple-color: rgba(0, 0, 0, .1);--mat-text-button-hover-state-layer-opacity: .04;--mat-text-button-focus-state-layer-opacity: .12;--mat-text-button-pressed-state-layer-opacity: .12;--mdc-filled-button-container-color: white;--mdc-filled-button-label-text-color: black;--mdc-filled-button-disabled-container-color: rgba(0, 0, 0, .12);--mdc-filled-button-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-filled-button-state-layer-color: black;--mat-filled-button-disabled-state-layer-color: black;--mat-filled-button-ripple-color: rgba(0, 0, 0, .1);--mat-filled-button-hover-state-layer-opacity: .04;--mat-filled-button-focus-state-layer-opacity: .12;--mat-filled-button-pressed-state-layer-opacity: .12;--mdc-protected-button-container-color: white;--mdc-protected-button-label-text-color: black;--mdc-protected-button-disabled-container-color: rgba(0, 0, 0, .12);--mdc-protected-button-disabled-label-text-color: rgba(0, 0, 0, .38);--mat-protected-button-state-layer-color: black;--mat-protected-button-disabled-state-layer-color: black;--mat-protected-button-ripple-color: rgba(0, 0, 0, .1);--mat-protected-button-hover-state-layer-opacity: .04;--mat-protected-button-focus-state-layer-opacity: .12;--mat-protected-button-pressed-state-layer-opacity: .12;--mdc-outlined-button-disabled-outline-color: rgba(0, 0, 0, .12);--mdc-outlined-button-disabled-label-text-color: rgba(0, 0, 0, .38);--mdc-outlined-button-label-text-color: black;--mdc-outlined-button-outline-color: rgba(0, 0, 0, .12);--mat-outlined-button-state-layer-color: black;--mat-outlined-button-disabled-state-layer-color: black;--mat-outlined-button-ripple-color: rgba(0, 0, 0, .1);--mat-outlined-button-hover-state-layer-opacity: .04;--mat-outlined-button-focus-state-layer-opacity: .12;--mat-outlined-button-pressed-state-layer-opacity: .12}.mat-mdc-button.mat-primary{--mdc-text-button-label-text-color: #ff9800;--mat-text-button-state-layer-color: #ff9800;--mat-text-button-ripple-color: rgba(255, 152, 0, .1)}.mat-mdc-button.mat-accent{--mdc-text-button-label-text-color: #424242;--mat-text-button-state-layer-color: #424242;--mat-text-button-ripple-color: rgba(66, 66, 66, .1)}.mat-mdc-button.mat-warn{--mdc-text-button-label-text-color: #f44336;--mat-text-button-state-layer-color: #f44336;--mat-text-button-ripple-color: rgba(244, 67, 54, .1)}.mat-mdc-unelevated-button.mat-primary{--mdc-filled-button-container-color: #ff9800;--mdc-filled-button-label-text-color: black;--mat-filled-button-state-layer-color: black;--mat-filled-button-ripple-color: rgba(0, 0, 0, .1)}.mat-mdc-unelevated-button.mat-accent{--mdc-filled-button-container-color: #424242;--mdc-filled-button-label-text-color: white;--mat-filled-button-state-layer-color: white;--mat-filled-button-ripple-color: rgba(255, 255, 255, .1)}.mat-mdc-unelevated-button.mat-warn{--mdc-filled-button-container-color: #f44336;--mdc-filled-button-label-text-color: white;--mat-filled-button-state-layer-color: white;--mat-filled-button-ripple-color: rgba(255, 255, 255, .1)}.mat-mdc-raised-button.mat-primary{--mdc-protected-button-container-color: #ff9800;--mdc-protected-button-label-text-color: black;--mat-protected-button-state-layer-color: black;--mat-protected-button-ripple-color: rgba(0, 0, 0, .1)}.mat-mdc-raised-button.mat-accent{--mdc-protected-button-container-color: #424242;--mdc-protected-button-label-text-color: white;--mat-protected-button-state-layer-color: white;--mat-protected-button-ripple-color: rgba(255, 255, 255, .1)}.mat-mdc-raised-button.mat-warn{--mdc-protected-button-container-color: #f44336;--mdc-protected-button-label-text-color: white;--mat-protected-button-state-layer-color: white;--mat-protected-button-ripple-color: rgba(255, 255, 255, .1)}.mat-mdc-outlined-button.mat-primary{--mdc-outlined-button-label-text-color: #ff9800;--mdc-outlined-button-outline-color: rgba(0, 0, 0, .12);--mat-outlined-button-state-layer-color: #ff9800;--mat-outlined-button-ripple-color: rgba(255, 152, 0, .1)}.mat-mdc-outlined-button.mat-accent{--mdc-outlined-button-label-text-color: #424242;--mdc-outlined-button-outline-color: rgba(0, 0, 0, .12);--mat-outlined-button-state-layer-color: #424242;--mat-outlined-button-ripple-color: rgba(66, 66, 66, .1)}.mat-mdc-outlined-button.mat-warn{--mdc-outlined-button-label-text-color: #f44336;--mdc-outlined-button-outline-color: rgba(0, 0, 0, .12);--mat-outlined-button-state-layer-color: #f44336;--mat-outlined-button-ripple-color: rgba(244, 67, 54, .1)}html{--mdc-text-button-container-height: 36px;--mdc-filled-button-container-height: 36px;--mdc-protected-button-container-height: 36px;--mdc-outlined-button-container-height: 36px;--mat-text-button-touch-target-display: block;--mat-filled-button-touch-target-display: block;--mat-protected-button-touch-target-display: block;--mat-outlined-button-touch-target-display: block}html{--mdc-text-button-label-text-font: Roboto, sans-serif;--mdc-text-button-label-text-size: 14px;--mdc-text-button-label-text-tracking: .0892857143em;--mdc-text-button-label-text-weight: 500;--mdc-text-button-label-text-transform: none;--mdc-filled-button-label-text-font: Roboto, sans-serif;--mdc-filled-button-label-text-size: 14px;--mdc-filled-button-label-text-tracking: .0892857143em;--mdc-filled-button-label-text-weight: 500;--mdc-filled-button-label-text-transform: none;--mdc-protected-button-label-text-font: Roboto, sans-serif;--mdc-protected-button-label-text-size: 14px;--mdc-protected-button-label-text-tracking: .0892857143em;--mdc-protected-button-label-text-weight: 500;--mdc-protected-button-label-text-transform: none;--mdc-outlined-button-label-text-font: Roboto, sans-serif;--mdc-outlined-button-label-text-size: 14px;--mdc-outlined-button-label-text-tracking: .0892857143em;--mdc-outlined-button-label-text-weight: 500;--mdc-outlined-button-label-text-transform: none}html{--mdc-icon-button-icon-size: 24px}html{--mdc-icon-button-icon-color: inherit;--mdc-icon-button-disabled-icon-color: rgba(0, 0, 0, .38);--mat-icon-button-state-layer-color: black;--mat-icon-button-disabled-state-layer-color: black;--mat-icon-button-ripple-color: rgba(0, 0, 0, .1);--mat-icon-button-hover-state-layer-opacity: .04;--mat-icon-button-focus-state-layer-opacity: .12;--mat-icon-button-pressed-state-layer-opacity: .12}html .mat-mdc-icon-button.mat-primary{--mdc-icon-button-icon-color: #ff9800;--mat-icon-button-state-layer-color: #ff9800;--mat-icon-button-ripple-color: rgba(255, 152, 0, .1)}html .mat-mdc-icon-button.mat-accent{--mdc-icon-button-icon-color: #424242;--mat-icon-button-state-layer-color: #424242;--mat-icon-button-ripple-color: rgba(66, 66, 66, .1)}html .mat-mdc-icon-button.mat-warn{--mdc-icon-button-icon-color: #f44336;--mat-icon-button-state-layer-color: #f44336;--mat-icon-button-ripple-color: rgba(244, 67, 54, .1)}html{--mat-icon-button-touch-target-display: block}.mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size: 48px;width:var(--mdc-icon-button-state-layer-size);height:var(--mdc-icon-button-state-layer-size);padding:12px}html{--mdc-fab-container-shape: 50%;--mdc-fab-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mdc-fab-small-container-shape: 50%;--mdc-fab-small-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-fab-small-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-small-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-fab-small-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12);--mdc-extended-fab-container-height: 48px;--mdc-extended-fab-container-shape: 24px;--mdc-extended-fab-container-elevation-shadow: 0px 3px 5px -1px rgba(0, 0, 0, .2), 0px 6px 10px 0px rgba(0, 0, 0, .14), 0px 1px 18px 0px rgba(0, 0, 0, .12);--mdc-extended-fab-focus-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-extended-fab-hover-container-elevation-shadow: 0px 5px 5px -3px rgba(0, 0, 0, .2), 0px 8px 10px 1px rgba(0, 0, 0, .14), 0px 3px 14px 2px rgba(0, 0, 0, .12);--mdc-extended-fab-pressed-container-elevation-shadow: 0px 7px 8px -4px rgba(0, 0, 0, .2), 0px 12px 17px 2px rgba(0, 0, 0, .14), 0px 5px 22px 4px rgba(0, 0, 0, .12)}html{--mdc-fab-container-color: white;--mat-fab-foreground-color: black;--mat-fab-state-layer-color: black;--mat-fab-disabled-state-layer-color: black;--mat-fab-ripple-color: rgba(0, 0, 0, .1);--mat-fab-hover-state-layer-opacity: .04;--mat-fab-focus-state-layer-opacity: .12;--mat-fab-pressed-state-layer-opacity: .12;--mat-fab-disabled-state-container-color: rgba(0, 0, 0, .12);--mat-fab-disabled-state-foreground-color: rgba(0, 0, 0, .38);--mdc-fab-small-container-color: white;--mat-fab-small-foreground-color: black;--mat-fab-small-state-layer-color: black;--mat-fab-small-disabled-state-layer-color: black;--mat-fab-small-ripple-color: rgba(0, 0, 0, .1);--mat-fab-small-hover-state-layer-opacity: .04;--mat-fab-small-focus-state-layer-opacity: .12;--mat-fab-small-pressed-state-layer-opacity: .12;--mat-fab-small-disabled-state-container-color: rgba(0, 0, 0, .12);--mat-fab-small-disabled-state-foreground-color: rgba(0, 0, 0, .38)}html .mat-mdc-fab.mat-primary{--mdc-fab-container-color: #ff9800;--mat-fab-foreground-color: black;--mat-fab-state-layer-color: black;--mat-fab-ripple-color: rgba(0, 0, 0, .1)}html .mat-mdc-fab.mat-accent{--mdc-fab-container-color: #424242;--mat-fab-foreground-color: white;--mat-fab-state-layer-color: white;--mat-fab-ripple-color: rgba(255, 255, 255, .1)}html .mat-mdc-fab.mat-warn{--mdc-fab-container-color: #f44336;--mat-fab-foreground-color: white;--mat-fab-state-layer-color: white;--mat-fab-ripple-color: rgba(255, 255, 255, .1)}html .mat-mdc-mini-fab.mat-primary{--mdc-fab-small-container-color: #ff9800;--mat-fab-small-foreground-color: black;--mat-fab-small-state-layer-color: black;--mat-fab-small-ripple-color: rgba(0, 0, 0, .1)}html .mat-mdc-mini-fab.mat-accent{--mdc-fab-small-container-color: #424242;--mat-fab-small-foreground-color: white;--mat-fab-small-state-layer-color: white;--mat-fab-small-ripple-color: rgba(255, 255, 255, .1)}html .mat-mdc-mini-fab.mat-warn{--mdc-fab-small-container-color: #f44336;--mat-fab-small-foreground-color: white;--mat-fab-small-state-layer-color: white;--mat-fab-small-ripple-color: rgba(255, 255, 255, .1)}html{--mat-fab-touch-target-display: block;--mat-fab-small-touch-target-display: block}html{--mdc-extended-fab-label-text-font: Roboto, sans-serif;--mdc-extended-fab-label-text-size: 14px;--mdc-extended-fab-label-text-tracking: .0892857143em;--mdc-extended-fab-label-text-weight: 500}html{--mdc-snackbar-container-shape: 4px}html{--mdc-snackbar-container-color: #333333;--mdc-snackbar-supporting-text-color: rgba(255, 255, 255, .87);--mat-snack-bar-button-color: #424242}html{--mdc-snackbar-supporting-text-font: Roboto, sans-serif;--mdc-snackbar-supporting-text-line-height: 20px;--mdc-snackbar-supporting-text-size: 14px;--mdc-snackbar-supporting-text-weight: 400}html{--mat-table-row-item-outline-width: 1px}html{--mat-table-background-color: white;--mat-table-header-headline-color: rgba(0, 0, 0, .87);--mat-table-row-item-label-text-color: rgba(0, 0, 0, .87);--mat-table-row-item-outline-color: rgba(0, 0, 0, .12)}html{--mat-table-header-container-height: 56px;--mat-table-footer-container-height: 52px;--mat-table-row-item-container-height: 52px}html{--mat-table-header-headline-font: Roboto, sans-serif;--mat-table-header-headline-line-height: 22px;--mat-table-header-headline-size: 14px;--mat-table-header-headline-weight: 500;--mat-table-header-headline-tracking: .0071428571em;--mat-table-row-item-label-text-font: Roboto, sans-serif;--mat-table-row-item-label-text-line-height: 20px;--mat-table-row-item-label-text-size: 14px;--mat-table-row-item-label-text-weight: 400;--mat-table-row-item-label-text-tracking: .0178571429em;--mat-table-footer-supporting-text-font: Roboto, sans-serif;--mat-table-footer-supporting-text-line-height: 20px;--mat-table-footer-supporting-text-size: 14px;--mat-table-footer-supporting-text-weight: 400;--mat-table-footer-supporting-text-tracking: .0178571429em}html{--mdc-circular-progress-active-indicator-width: 4px;--mdc-circular-progress-size: 48px}html{--mdc-circular-progress-active-indicator-color: #ff9800}html .mat-accent{--mdc-circular-progress-active-indicator-color: #424242}html .mat-warn{--mdc-circular-progress-active-indicator-color: #f44336}html{--mat-badge-container-shape: 50%;--mat-badge-container-size: unset;--mat-badge-small-size-container-size: unset;--mat-badge-large-size-container-size: unset;--mat-badge-legacy-container-size: 22px;--mat-badge-legacy-small-size-container-size: 16px;--mat-badge-legacy-large-size-container-size: 28px;--mat-badge-container-offset: -11px 0;--mat-badge-small-size-container-offset: -8px 0;--mat-badge-large-size-container-offset: -14px 0;--mat-badge-container-overlap-offset: -11px;--mat-badge-small-size-container-overlap-offset: -8px;--mat-badge-large-size-container-overlap-offset: -14px;--mat-badge-container-padding: 0;--mat-badge-small-size-container-padding: 0;--mat-badge-large-size-container-padding: 0}html{--mat-badge-background-color: #ff9800;--mat-badge-text-color: rgba(0, 0, 0, .87);--mat-badge-disabled-state-background-color: #b9b9b9;--mat-badge-disabled-state-text-color: rgba(0, 0, 0, .38)}.mat-badge-accent{--mat-badge-background-color: #424242;--mat-badge-text-color: white}.mat-badge-warn{--mat-badge-background-color: #f44336;--mat-badge-text-color: white}html{--mat-badge-text-font: Roboto, sans-serif;--mat-badge-text-size: 12px;--mat-badge-text-weight: 600;--mat-badge-small-size-text-size: 9px;--mat-badge-large-size-text-size: 24px}html{--mat-bottom-sheet-container-shape: 4px}html{--mat-bottom-sheet-container-text-color: rgba(0, 0, 0, .87);--mat-bottom-sheet-container-background-color: white}html{--mat-bottom-sheet-container-text-font: Roboto, sans-serif;--mat-bottom-sheet-container-text-line-height: 20px;--mat-bottom-sheet-container-text-size: 14px;--mat-bottom-sheet-container-text-tracking: .0178571429em;--mat-bottom-sheet-container-text-weight: 400}html{--mat-legacy-button-toggle-height: 36px;--mat-legacy-button-toggle-shape: 2px;--mat-legacy-button-toggle-focus-state-layer-opacity: 1;--mat-standard-button-toggle-shape: 4px;--mat-standard-button-toggle-hover-state-layer-opacity: .04;--mat-standard-button-toggle-focus-state-layer-opacity: .12}html{--mat-legacy-button-toggle-text-color: rgba(0, 0, 0, .38);--mat-legacy-button-toggle-state-layer-color: rgba(0, 0, 0, .12);--mat-legacy-button-toggle-selected-state-text-color: rgba(0, 0, 0, .54);--mat-legacy-button-toggle-selected-state-background-color: #e0e0e0;--mat-legacy-button-toggle-disabled-state-text-color: rgba(0, 0, 0, .26);--mat-legacy-button-toggle-disabled-state-background-color: #eeeeee;--mat-legacy-button-toggle-disabled-selected-state-background-color: #bdbdbd;--mat-standard-button-toggle-text-color: rgba(0, 0, 0, .87);--mat-standard-button-toggle-background-color: white;--mat-standard-button-toggle-state-layer-color: black;--mat-standard-button-toggle-selected-state-background-color: #e0e0e0;--mat-standard-button-toggle-selected-state-text-color: rgba(0, 0, 0, .87);--mat-standard-button-toggle-disabled-state-text-color: rgba(0, 0, 0, .26);--mat-standard-button-toggle-disabled-state-background-color: white;--mat-standard-button-toggle-disabled-selected-state-text-color: rgba(0, 0, 0, .87);--mat-standard-button-toggle-disabled-selected-state-background-color: #bdbdbd;--mat-standard-button-toggle-divider-color: #e0e0e0}html{--mat-standard-button-toggle-height: 48px}html{--mat-legacy-button-toggle-label-text-font: Roboto, sans-serif;--mat-legacy-button-toggle-label-text-line-height: 24px;--mat-legacy-button-toggle-label-text-size: 16px;--mat-legacy-button-toggle-label-text-tracking: .03125em;--mat-legacy-button-toggle-label-text-weight: 400;--mat-standard-button-toggle-label-text-font: Roboto, sans-serif;--mat-standard-button-toggle-label-text-line-height: 24px;--mat-standard-button-toggle-label-text-size: 16px;--mat-standard-button-toggle-label-text-tracking: .03125em;--mat-standard-button-toggle-label-text-weight: 400}html{--mat-datepicker-calendar-container-shape: 4px;--mat-datepicker-calendar-container-touch-shape: 4px;--mat-datepicker-calendar-container-elevation-shadow: 0px 2px 4px -1px rgba(0, 0, 0, .2), 0px 4px 5px 0px rgba(0, 0, 0, .14), 0px 1px 10px 0px rgba(0, 0, 0, .12);--mat-datepicker-calendar-container-touch-elevation-shadow: 0px 11px 15px -7px rgba(0, 0, 0, .2), 0px 24px 38px 3px rgba(0, 0, 0, .14), 0px 9px 46px 8px rgba(0, 0, 0, .12)}html{--mat-datepicker-calendar-date-selected-state-text-color: rgba(0, 0, 0, .87);--mat-datepicker-calendar-date-selected-state-background-color: #ff9800;--mat-datepicker-calendar-date-selected-disabled-state-background-color: rgba(255, 152, 0, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color: rgba(0, 0, 0, .87);--mat-datepicker-calendar-date-focus-state-background-color: rgba(255, 152, 0, .3);--mat-datepicker-calendar-date-hover-state-background-color: rgba(255, 152, 0, .3);--mat-datepicker-toggle-active-state-icon-color: #ff9800;--mat-datepicker-calendar-date-in-range-state-background-color: rgba(255, 152, 0, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: #46a35e;--mat-datepicker-toggle-icon-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-body-label-text-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-period-button-text-color: black;--mat-datepicker-calendar-period-button-icon-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-navigation-button-icon-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-header-divider-color: rgba(0, 0, 0, .12);--mat-datepicker-calendar-header-text-color: rgba(0, 0, 0, .54);--mat-datepicker-calendar-date-today-outline-color: rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-today-disabled-state-outline-color: rgba(0, 0, 0, .18);--mat-datepicker-calendar-date-text-color: rgba(0, 0, 0, .87);--mat-datepicker-calendar-date-outline-color: transparent;--mat-datepicker-calendar-date-disabled-state-text-color: rgba(0, 0, 0, .38);--mat-datepicker-calendar-date-preview-state-outline-color: rgba(0, 0, 0, .24);--mat-datepicker-range-input-separator-color: rgba(0, 0, 0, .87);--mat-datepicker-range-input-disabled-state-separator-color: rgba(0, 0, 0, .38);--mat-datepicker-range-input-disabled-state-text-color: rgba(0, 0, 0, .38);--mat-datepicker-calendar-container-background-color: white;--mat-datepicker-calendar-container-text-color: rgba(0, 0, 0, .87)}.mat-datepicker-content.mat-accent{--mat-datepicker-calendar-date-selected-state-text-color: white;--mat-datepicker-calendar-date-selected-state-background-color: #424242;--mat-datepicker-calendar-date-selected-disabled-state-background-color: rgba(66, 66, 66, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color: white;--mat-datepicker-calendar-date-focus-state-background-color: rgba(66, 66, 66, .3);--mat-datepicker-calendar-date-hover-state-background-color: rgba(66, 66, 66, .3);--mat-datepicker-calendar-date-in-range-state-background-color: rgba(66, 66, 66, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: #46a35e}.mat-datepicker-content.mat-warn{--mat-datepicker-calendar-date-selected-state-text-color: white;--mat-datepicker-calendar-date-selected-state-background-color: #f44336;--mat-datepicker-calendar-date-selected-disabled-state-background-color: rgba(244, 67, 54, .4);--mat-datepicker-calendar-date-today-selected-state-outline-color: white;--mat-datepicker-calendar-date-focus-state-background-color: rgba(244, 67, 54, .3);--mat-datepicker-calendar-date-hover-state-background-color: rgba(244, 67, 54, .3);--mat-datepicker-calendar-date-in-range-state-background-color: rgba(244, 67, 54, .2);--mat-datepicker-calendar-date-in-comparison-range-state-background-color: rgba(249, 171, 0, .2);--mat-datepicker-calendar-date-in-overlap-range-state-background-color: #a8dab5;--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color: #46a35e}.mat-datepicker-toggle-active.mat-accent{--mat-datepicker-toggle-active-state-icon-color: #424242}.mat-datepicker-toggle-active.mat-warn{--mat-datepicker-toggle-active-state-icon-color: #f44336}.mat-calendar-controls{--mat-icon-button-touch-target-display: none}.mat-calendar-controls .mat-mdc-icon-button.mat-mdc-button-base{--mdc-icon-button-state-layer-size: 40px;width:var(--mdc-icon-button-state-layer-size);height:var(--mdc-icon-button-state-layer-size);padding:8px}html{--mat-datepicker-calendar-text-font: Roboto, sans-serif;--mat-datepicker-calendar-text-size: 13px;--mat-datepicker-calendar-body-label-text-size: 14px;--mat-datepicker-calendar-body-label-text-weight: 500;--mat-datepicker-calendar-period-button-text-size: 14px;--mat-datepicker-calendar-period-button-text-weight: 500;--mat-datepicker-calendar-header-text-size: 11px;--mat-datepicker-calendar-header-text-weight: 400}html{--mat-divider-width: 1px}html{--mat-divider-color: rgba(0, 0, 0, .12)}html{--mat-expansion-container-shape: 4px;--mat-expansion-legacy-header-indicator-display: inline-block;--mat-expansion-header-indicator-display: none}html{--mat-expansion-container-background-color: white;--mat-expansion-container-text-color: rgba(0, 0, 0, .87);--mat-expansion-actions-divider-color: rgba(0, 0, 0, .12);--mat-expansion-header-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-expansion-header-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-expansion-header-disabled-state-text-color: rgba(0, 0, 0, .26);--mat-expansion-header-text-color: rgba(0, 0, 0, .87);--mat-expansion-header-description-color: rgba(0, 0, 0, .54);--mat-expansion-header-indicator-color: rgba(0, 0, 0, .54)}html{--mat-expansion-header-collapsed-state-height: 48px;--mat-expansion-header-expanded-state-height: 64px}html{--mat-expansion-header-text-font: Roboto, sans-serif;--mat-expansion-header-text-size: 14px;--mat-expansion-header-text-weight: 500;--mat-expansion-header-text-line-height: inherit;--mat-expansion-header-text-tracking: inherit;--mat-expansion-container-text-font: Roboto, sans-serif;--mat-expansion-container-text-line-height: 20px;--mat-expansion-container-text-size: 14px;--mat-expansion-container-text-tracking: .0178571429em;--mat-expansion-container-text-weight: 400}html{--mat-grid-list-tile-header-primary-text-size: 14px;--mat-grid-list-tile-header-secondary-text-size: 12px;--mat-grid-list-tile-footer-primary-text-size: 14px;--mat-grid-list-tile-footer-secondary-text-size: 12px}html{--mat-icon-color: inherit}.mat-icon.mat-primary{--mat-icon-color: #ff9800}.mat-icon.mat-accent{--mat-icon-color: #424242}.mat-icon.mat-warn{--mat-icon-color: #f44336}html{--mat-sidenav-container-shape: 0;--mat-sidenav-container-elevation-shadow: 0px 8px 10px -5px rgba(0, 0, 0, .2), 0px 16px 24px 2px rgba(0, 0, 0, .14), 0px 6px 30px 5px rgba(0, 0, 0, .12);--mat-sidenav-container-width: auto}html{--mat-sidenav-container-divider-color: rgba(0, 0, 0, .12);--mat-sidenav-container-background-color: white;--mat-sidenav-container-text-color: rgba(0, 0, 0, .87);--mat-sidenav-content-background-color: #fafafa;--mat-sidenav-content-text-color: rgba(0, 0, 0, .87);--mat-sidenav-scrim-color: rgba(0, 0, 0, .6)}html{--mat-stepper-header-icon-foreground-color: rgba(0, 0, 0, .87);--mat-stepper-header-selected-state-icon-background-color: #ff9800;--mat-stepper-header-selected-state-icon-foreground-color: rgba(0, 0, 0, .87);--mat-stepper-header-done-state-icon-background-color: #ff9800;--mat-stepper-header-done-state-icon-foreground-color: rgba(0, 0, 0, .87);--mat-stepper-header-edit-state-icon-background-color: #ff9800;--mat-stepper-header-edit-state-icon-foreground-color: rgba(0, 0, 0, .87);--mat-stepper-container-color: white;--mat-stepper-line-color: rgba(0, 0, 0, .12);--mat-stepper-header-hover-state-layer-color: rgba(0, 0, 0, .04);--mat-stepper-header-focus-state-layer-color: rgba(0, 0, 0, .04);--mat-stepper-header-label-text-color: rgba(0, 0, 0, .54);--mat-stepper-header-optional-label-text-color: rgba(0, 0, 0, .54);--mat-stepper-header-selected-state-label-text-color: rgba(0, 0, 0, .87);--mat-stepper-header-error-state-label-text-color: #f44336;--mat-stepper-header-icon-background-color: rgba(0, 0, 0, .54);--mat-stepper-header-error-state-icon-foreground-color: #f44336;--mat-stepper-header-error-state-icon-background-color: transparent}html .mat-step-header.mat-accent{--mat-stepper-header-icon-foreground-color: white;--mat-stepper-header-selected-state-icon-background-color: #424242;--mat-stepper-header-selected-state-icon-foreground-color: white;--mat-stepper-header-done-state-icon-background-color: #424242;--mat-stepper-header-done-state-icon-foreground-color: white;--mat-stepper-header-edit-state-icon-background-color: #424242;--mat-stepper-header-edit-state-icon-foreground-color: white}html .mat-step-header.mat-warn{--mat-stepper-header-icon-foreground-color: white;--mat-stepper-header-selected-state-icon-background-color: #f44336;--mat-stepper-header-selected-state-icon-foreground-color: white;--mat-stepper-header-done-state-icon-background-color: #f44336;--mat-stepper-header-done-state-icon-foreground-color: white;--mat-stepper-header-edit-state-icon-background-color: #f44336;--mat-stepper-header-edit-state-icon-foreground-color: white}html{--mat-stepper-header-height: 72px}html{--mat-stepper-container-text-font: Roboto, sans-serif;--mat-stepper-header-label-text-font: Roboto, sans-serif;--mat-stepper-header-label-text-size: 14px;--mat-stepper-header-label-text-weight: 400;--mat-stepper-header-error-state-label-text-size: 16px;--mat-stepper-header-selected-state-label-text-size: 16px;--mat-stepper-header-selected-state-label-text-weight: 400}html{--mat-sort-arrow-color: #757575}html{--mat-toolbar-container-background-color: whitesmoke;--mat-toolbar-container-text-color: rgba(0, 0, 0, .87)}.mat-toolbar.mat-primary{--mat-toolbar-container-background-color: #ff9800;--mat-toolbar-container-text-color: rgba(0, 0, 0, .87)}.mat-toolbar.mat-accent{--mat-toolbar-container-background-color: #424242;--mat-toolbar-container-text-color: white}.mat-toolbar.mat-warn{--mat-toolbar-container-background-color: #f44336;--mat-toolbar-container-text-color: white}html{--mat-toolbar-standard-height: 64px;--mat-toolbar-mobile-height: 56px}html{--mat-toolbar-title-text-font: Roboto, sans-serif;--mat-toolbar-title-text-line-height: 32px;--mat-toolbar-title-text-size: 20px;--mat-toolbar-title-text-tracking: .0125em;--mat-toolbar-title-text-weight: 500}html{--mat-tree-container-background-color: white;--mat-tree-node-text-color: rgba(0, 0, 0, .87)}html{--mat-tree-node-min-height: 48px}html{--mat-tree-node-text-font: Roboto, sans-serif;--mat-tree-node-text-size: 14px;--mat-tree-node-text-weight: 400}:root{--ion-color-primary: #0054e9;--ion-color-primary-rgb: 0, 84, 233;--ion-color-primary-contrast: #fff;--ion-color-primary-contrast-rgb: 255, 255, 255;--ion-color-primary-shade: #004acd;--ion-color-primary-tint: #1a65eb;--ion-color-secondary: #0163aa;--ion-color-secondary-rgb: 1, 99, 170;--ion-color-secondary-contrast: #fff;--ion-color-secondary-contrast-rgb: 255, 255, 255;--ion-color-secondary-shade: #015796;--ion-color-secondary-tint: #1a73b3;--ion-color-tertiary: #6030ff;--ion-color-tertiary-rgb: 96, 48, 255;--ion-color-tertiary-contrast: #fff;--ion-color-tertiary-contrast-rgb: 255, 255, 255;--ion-color-tertiary-shade: #542ae0;--ion-color-tertiary-tint: #7045ff;--ion-color-success: #2dd55b;--ion-color-success-rgb: 45, 213, 91;--ion-color-success-contrast: #000;--ion-color-success-contrast-rgb: 0, 0, 0;--ion-color-success-shade: #28bb50;--ion-color-success-tint: #42d96b;--ion-color-warning: #ffc409;--ion-color-warning-rgb: 255, 196, 9;--ion-color-warning-contrast: #000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0ac08;--ion-color-warning-tint: #ffca22;--ion-color-danger: #c5000f;--ion-color-danger-rgb: 197, 0, 15;--ion-color-danger-contrast: #fff;--ion-color-danger-contrast-rgb: 255, 255, 255;--ion-color-danger-shade: #ad000d;--ion-color-danger-tint: #cb1a27;--ion-color-light: #f4f5f8;--ion-color-light-rgb: 244, 245, 248;--ion-color-light-contrast: #000;--ion-color-light-contrast-rgb: 0, 0, 0;--ion-color-light-shade: #d7d8da;--ion-color-light-tint: #f5f6f9;--ion-color-medium: #636469;--ion-color-medium-rgb: 99, 100, 105;--ion-color-medium-contrast: #fff;--ion-color-medium-contrast-rgb: 255, 255, 255;--ion-color-medium-shade: #57585c;--ion-color-medium-tint: #737478;--ion-color-dark: #222428;--ion-color-dark-rgb: 34, 36, 40;--ion-color-dark-contrast: #fff;--ion-color-dark-contrast-rgb: 255, 255, 255;--ion-color-dark-shade: #1e2023;--ion-color-dark-tint: #383a3e}html.ios{--ion-default-font: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Roboto", sans-serif}html.md{--ion-default-font: "Roboto", "Helvetica Neue", sans-serif}html{--ion-dynamic-font: -apple-system-body;--ion-font-family: var(--ion-default-font)}body{background:var(--ion-background-color);color:var(--ion-text-color)}body.backdrop-no-scroll{overflow:hidden}html.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,html.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,html.ios ion-modal ion-footer ion-toolbar:first-of-type{padding-top:6px}html.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,html.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type{padding-bottom:6px}html.ios ion-modal ion-toolbar{padding-right:calc(var(--ion-safe-area-right) + 8px);padding-left:calc(var(--ion-safe-area-left) + 8px)}@media screen and (min-width: 768px){html.ios ion-modal.modal-card:first-of-type{--backdrop-opacity: .18}}ion-modal.modal-default.show-modal~ion-modal.modal-default{--backdrop-opacity: 0;--box-shadow: none}html.ios ion-modal.modal-card .ion-page{border-top-left-radius:var(--border-radius)}.ion-color-primary{--ion-color-base: var(--ion-color-primary, #0054e9) !important;--ion-color-base-rgb: var(--ion-color-primary-rgb, 0, 84, 233) !important;--ion-color-contrast: var(--ion-color-primary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-primary-shade, #004acd) !important;--ion-color-tint: var(--ion-color-primary-tint, #1a65eb) !important}.ion-color-secondary{--ion-color-base: var(--ion-color-secondary, #0163aa) !important;--ion-color-base-rgb: var(--ion-color-secondary-rgb, 1, 99, 170) !important;--ion-color-contrast: var(--ion-color-secondary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-secondary-shade, #015796) !important;--ion-color-tint: var(--ion-color-secondary-tint, #1a73b3) !important}.ion-color-tertiary{--ion-color-base: var(--ion-color-tertiary, #6030ff) !important;--ion-color-base-rgb: var(--ion-color-tertiary-rgb, 96, 48, 255) !important;--ion-color-contrast: var(--ion-color-tertiary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-tertiary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-tertiary-shade, #542ae0) !important;--ion-color-tint: var(--ion-color-tertiary-tint, #7045ff) !important}.ion-color-success{--ion-color-base: var(--ion-color-success, #2dd55b) !important;--ion-color-base-rgb: var(--ion-color-success-rgb, 45, 213, 91) !important;--ion-color-contrast: var(--ion-color-success-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-success-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-success-shade, #28bb50) !important;--ion-color-tint: var(--ion-color-success-tint, #42d96b) !important}.ion-color-warning{--ion-color-base: var(--ion-color-warning, #ffc409) !important;--ion-color-base-rgb: var(--ion-color-warning-rgb, 255, 196, 9) !important;--ion-color-contrast: var(--ion-color-warning-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-warning-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-warning-shade, #e0ac08) !important;--ion-color-tint: var(--ion-color-warning-tint, #ffca22) !important}.ion-color-danger{--ion-color-base: var(--ion-color-danger, #c5000f) !important;--ion-color-base-rgb: var(--ion-color-danger-rgb, 197, 0, 15) !important;--ion-color-contrast: var(--ion-color-danger-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-danger-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-danger-shade, #ad000d) !important;--ion-color-tint: var(--ion-color-danger-tint, #cb1a27) !important}.ion-color-light{--ion-color-base: var(--ion-color-light, #f4f5f8) !important;--ion-color-base-rgb: var(--ion-color-light-rgb, 244, 245, 248) !important;--ion-color-contrast: var(--ion-color-light-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-light-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-light-shade, #d7d8da) !important;--ion-color-tint: var(--ion-color-light-tint, #f5f6f9) !important}.ion-color-medium{--ion-color-base: var(--ion-color-medium, #636469) !important;--ion-color-base-rgb: var(--ion-color-medium-rgb, 99, 100, 105) !important;--ion-color-contrast: var(--ion-color-medium-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-medium-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-medium-shade, #57585c) !important;--ion-color-tint: var(--ion-color-medium-tint, #737478) !important}.ion-color-dark{--ion-color-base: var(--ion-color-dark, #222428) !important;--ion-color-base-rgb: var(--ion-color-dark-rgb, 34, 36, 40) !important;--ion-color-contrast: var(--ion-color-dark-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-dark-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-dark-shade, #1e2023) !important;--ion-color-tint: var(--ion-color-dark-tint, #383a3e) !important}.ion-page{inset:0;display:flex;position:absolute;flex-direction:column;justify-content:space-between;contain:layout size style;z-index:0}ion-modal>.ion-page{position:relative;contain:layout style;height:100%}.split-pane-visible>.ion-page.split-pane-main{position:relative}ion-route,ion-route-redirect,ion-router,ion-select-option,ion-nav-controller,ion-menu-controller,ion-action-sheet-controller,ion-alert-controller,ion-loading-controller,ion-modal-controller,ion-picker-controller,ion-popover-controller,ion-toast-controller,.ion-page-hidden{display:none!important}.ion-page-invisible{opacity:0}.can-go-back>ion-header ion-back-button{display:block}html.plt-ios.plt-hybrid,html.plt-ios.plt-pwa{--ion-statusbar-padding: 20px}@supports (padding-top: 20px){html{--ion-safe-area-top: var(--ion-statusbar-padding)}}@supports (padding-top: env(safe-area-inset-top)){html{--ion-safe-area-top: env(safe-area-inset-top);--ion-safe-area-bottom: env(safe-area-inset-bottom);--ion-safe-area-left: env(safe-area-inset-left);--ion-safe-area-right: env(safe-area-inset-right)}}ion-card.ion-color .ion-inherit-color,ion-card-header.ion-color .ion-inherit-color{color:inherit}.menu-content{transform:translateZ(0)}.menu-content-open{cursor:pointer;touch-action:manipulation;pointer-events:none;overflow-y:hidden}.menu-content-open ion-content{--overflow: hidden}.menu-content-open .ion-content-scroll-host{overflow:hidden}.ios .menu-content-reveal{box-shadow:-8px 0 42px #00000014}[dir=rtl].ios .menu-content-reveal{box-shadow:8px 0 42px #00000014}.md .menu-content-reveal,.md .menu-content-push{box-shadow:4px 0 16px #0000002e}ion-accordion-group.accordion-group-expand-inset>ion-accordion:first-of-type{border-top-left-radius:8px;border-top-right-radius:8px}ion-accordion-group.accordion-group-expand-inset>ion-accordion:last-of-type{border-bottom-left-radius:8px;border-bottom-right-radius:8px}ion-accordion-group>ion-accordion:last-of-type ion-item[slot=header]{--border-width: 0px}ion-accordion.accordion-animated>[slot=header] .ion-accordion-toggle-icon{transition:.3s transform cubic-bezier(.25,.8,.5,1)}@media (prefers-reduced-motion: reduce){ion-accordion .ion-accordion-toggle-icon{transition:none!important}}ion-accordion.accordion-expanding>[slot=header] .ion-accordion-toggle-icon,ion-accordion.accordion-expanded>[slot=header] .ion-accordion-toggle-icon{transform:rotate(180deg)}ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-previous ion-item[slot=header]{--border-width: 0px;--inner-border-width: 0px}ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-expanding:first-of-type,ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-expanded:first-of-type{margin-top:0}ion-input input::-webkit-date-and-time-value{text-align:start}.ion-datetime-button-overlay{--width: fit-content;--height: fit-content}.ion-datetime-button-overlay ion-datetime.datetime-grid{width:320px;min-height:320px}[ion-last-focus],header[tabindex="-1"]:focus,[role=banner][tabindex="-1"]:focus,main[tabindex="-1"]:focus,[role=main][tabindex="-1"]:focus,h1[tabindex="-1"]:focus,[role=heading][aria-level="1"][tabindex="-1"]:focus{outline:none}.popover-viewport:has(>ion-content){overflow:hidden}@supports not selector(:has(> ion-content)){.popover-viewport{overflow:hidden}}audio,canvas,progress,video{vertical-align:baseline}audio:not([controls]){display:none;height:0}b,strong{font-weight:700}img{max-width:100%}hr{height:1px;border-width:0;box-sizing:content-box}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}label,input,select,textarea{font-family:inherit;line-height:normal}textarea{overflow:auto;height:auto;font:inherit;color:inherit}textarea::placeholder{padding-left:2px}form,input,optgroup,select{margin:0;font:inherit;color:inherit}html input[type=button],input[type=reset],input[type=submit]{cursor:pointer;-webkit-appearance:button}a,a div,a span,a ion-icon,a ion-label,button,button div,button span,button ion-icon,button ion-label,.ion-tappable,[tappable],[tappable] div,[tappable] span,[tappable] ion-icon,[tappable] ion-label,input,textarea{touch-action:manipulation}a ion-label,button ion-label{pointer-events:none}button{padding:0;border:0;border-radius:0;font-family:inherit;font-style:inherit;font-variant:inherit;line-height:1;text-transform:none;cursor:pointer;-webkit-appearance:button}[tappable]{cursor:pointer}a[disabled],button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}*{box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none}html{width:100%;height:100%;-webkit-text-size-adjust:100%;text-size-adjust:100%}html:not(.hydrated) body{display:none}html.ion-ce body{display:block}html.plt-pwa{height:100vh}body{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin:0;padding:0;position:fixed;width:100%;max-width:100%;height:100%;max-height:100%;transform:translateZ(0);text-rendering:optimizeLegibility;overflow:hidden;touch-action:manipulation;-webkit-user-drag:none;-ms-content-zooming:none;word-wrap:break-word;overscroll-behavior-y:none;-webkit-text-size-adjust:none;text-size-adjust:none}html{font-family:var(--ion-font-family)}@supports (-webkit-touch-callout: none){html{font:var(--ion-dynamic-font, 16px var(--ion-font-family))}}a{background-color:transparent;color:var(--ion-color-primary, #0054e9)}h1,h2,h3,h4,h5,h6{margin-top:16px;margin-bottom:10px;font-weight:500;line-height:1.2}h1{margin-top:20px;font-size:1.625rem}h2{margin-top:18px;font-size:1.5rem}h3{font-size:1.375rem}h4{font-size:1.25rem}h5{font-size:1.125rem}h6{font-size:1rem}small{font-size:75%}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}.ion-hide,.ion-hide-up,.ion-hide-down{display:none!important}@media (min-width: 576px){.ion-hide-sm-up{display:none!important}}@media (max-width: 575.98px){.ion-hide-sm-down{display:none!important}}@media (min-width: 768px){.ion-hide-md-up{display:none!important}}@media (max-width: 767.98px){.ion-hide-md-down{display:none!important}}@media (min-width: 992px){.ion-hide-lg-up{display:none!important}}@media (max-width: 991.98px){.ion-hide-lg-down{display:none!important}}@media (min-width: 1200px){.ion-hide-xl-up{display:none!important}}@media (max-width: 1199.98px){.ion-hide-xl-down{display:none!important}}.ion-no-padding{--padding-start: 0;--padding-end: 0;--padding-top: 0;--padding-bottom: 0;padding:0}.ion-padding{--padding-start: var(--ion-padding, 16px);--padding-end: var(--ion-padding, 16px);--padding-top: var(--ion-padding, 16px);--padding-bottom: var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-top{--padding-top: var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px)}.ion-padding-start{--padding-start: var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px)}.ion-padding-end{--padding-end: var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px)}.ion-padding-bottom{--padding-bottom: var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-vertical{--padding-top: var(--ion-padding, 16px);--padding-bottom: var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-horizontal{--padding-start: var(--ion-padding, 16px);--padding-end: var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px)}.ion-no-margin{--margin-start: 0;--margin-end: 0;--margin-top: 0;--margin-bottom: 0;margin:0}.ion-margin{--margin-start: var(--ion-margin, 16px);--margin-end: var(--ion-margin, 16px);--margin-top: var(--ion-margin, 16px);--margin-bottom: var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-top{--margin-top: var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px)}.ion-margin-start{--margin-start: var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px)}.ion-margin-end{--margin-end: var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px)}.ion-margin-bottom{--margin-bottom: var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-vertical{--margin-top: var(--ion-margin, 16px);--margin-bottom: var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-horizontal{--margin-start: var(--ion-margin, 16px);--margin-end: var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px)}.ion-float-left{float:left!important}.ion-float-right{float:right!important}.ion-float-start{float:left!important}:host-context([dir=rtl]) .ion-float-start{float:right!important}[dir=rtl] .ion-float-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-start:dir(rtl){float:right!important}}.ion-float-end{float:right!important}:host-context([dir=rtl]) .ion-float-end{float:left!important}[dir=rtl] .ion-float-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-end:dir(rtl){float:left!important}}@media (min-width: 576px){.ion-float-sm-left{float:left!important}.ion-float-sm-right{float:right!important}.ion-float-sm-start{float:left!important}:host-context([dir=rtl]) .ion-float-sm-start{float:right!important}[dir=rtl] .ion-float-sm-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-sm-start:dir(rtl){float:right!important}}.ion-float-sm-end{float:right!important}:host-context([dir=rtl]) .ion-float-sm-end{float:left!important}[dir=rtl] .ion-float-sm-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-sm-end:dir(rtl){float:left!important}}}@media (min-width: 768px){.ion-float-md-left{float:left!important}.ion-float-md-right{float:right!important}.ion-float-md-start{float:left!important}:host-context([dir=rtl]) .ion-float-md-start{float:right!important}[dir=rtl] .ion-float-md-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-md-start:dir(rtl){float:right!important}}.ion-float-md-end{float:right!important}:host-context([dir=rtl]) .ion-float-md-end{float:left!important}[dir=rtl] .ion-float-md-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-md-end:dir(rtl){float:left!important}}}@media (min-width: 992px){.ion-float-lg-left{float:left!important}.ion-float-lg-right{float:right!important}.ion-float-lg-start{float:left!important}:host-context([dir=rtl]) .ion-float-lg-start{float:right!important}[dir=rtl] .ion-float-lg-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-lg-start:dir(rtl){float:right!important}}.ion-float-lg-end{float:right!important}:host-context([dir=rtl]) .ion-float-lg-end{float:left!important}[dir=rtl] .ion-float-lg-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-lg-end:dir(rtl){float:left!important}}}@media (min-width: 1200px){.ion-float-xl-left{float:left!important}.ion-float-xl-right{float:right!important}.ion-float-xl-start{float:left!important}:host-context([dir=rtl]) .ion-float-xl-start{float:right!important}[dir=rtl] .ion-float-xl-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-xl-start:dir(rtl){float:right!important}}.ion-float-xl-end{float:right!important}:host-context([dir=rtl]) .ion-float-xl-end{float:left!important}[dir=rtl] .ion-float-xl-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-xl-end:dir(rtl){float:left!important}}}.ion-text-center{text-align:center!important}.ion-text-justify{text-align:justify!important}.ion-text-start{text-align:start!important}.ion-text-end{text-align:end!important}.ion-text-left{text-align:left!important}.ion-text-right{text-align:right!important}.ion-text-nowrap{white-space:nowrap!important}.ion-text-wrap{white-space:normal!important}@media (min-width: 576px){.ion-text-sm-center{text-align:center!important}.ion-text-sm-justify{text-align:justify!important}.ion-text-sm-start{text-align:start!important}.ion-text-sm-end{text-align:end!important}.ion-text-sm-left{text-align:left!important}.ion-text-sm-right{text-align:right!important}.ion-text-sm-nowrap{white-space:nowrap!important}.ion-text-sm-wrap{white-space:normal!important}}@media (min-width: 768px){.ion-text-md-center{text-align:center!important}.ion-text-md-justify{text-align:justify!important}.ion-text-md-start{text-align:start!important}.ion-text-md-end{text-align:end!important}.ion-text-md-left{text-align:left!important}.ion-text-md-right{text-align:right!important}.ion-text-md-nowrap{white-space:nowrap!important}.ion-text-md-wrap{white-space:normal!important}}@media (min-width: 992px){.ion-text-lg-center{text-align:center!important}.ion-text-lg-justify{text-align:justify!important}.ion-text-lg-start{text-align:start!important}.ion-text-lg-end{text-align:end!important}.ion-text-lg-left{text-align:left!important}.ion-text-lg-right{text-align:right!important}.ion-text-lg-nowrap{white-space:nowrap!important}.ion-text-lg-wrap{white-space:normal!important}}@media (min-width: 1200px){.ion-text-xl-center{text-align:center!important}.ion-text-xl-justify{text-align:justify!important}.ion-text-xl-start{text-align:start!important}.ion-text-xl-end{text-align:end!important}.ion-text-xl-left{text-align:left!important}.ion-text-xl-right{text-align:right!important}.ion-text-xl-nowrap{white-space:nowrap!important}.ion-text-xl-wrap{white-space:normal!important}}.ion-text-uppercase{text-transform:uppercase!important}.ion-text-lowercase{text-transform:lowercase!important}.ion-text-capitalize{text-transform:capitalize!important}@media (min-width: 576px){.ion-text-sm-uppercase{text-transform:uppercase!important}.ion-text-sm-lowercase{text-transform:lowercase!important}.ion-text-sm-capitalize{text-transform:capitalize!important}}@media (min-width: 768px){.ion-text-md-uppercase{text-transform:uppercase!important}.ion-text-md-lowercase{text-transform:lowercase!important}.ion-text-md-capitalize{text-transform:capitalize!important}}@media (min-width: 992px){.ion-text-lg-uppercase{text-transform:uppercase!important}.ion-text-lg-lowercase{text-transform:lowercase!important}.ion-text-lg-capitalize{text-transform:capitalize!important}}@media (min-width: 1200px){.ion-text-xl-uppercase{text-transform:uppercase!important}.ion-text-xl-lowercase{text-transform:lowercase!important}.ion-text-xl-capitalize{text-transform:capitalize!important}}.ion-align-self-start{align-self:flex-start!important}.ion-align-self-end{align-self:flex-end!important}.ion-align-self-center{align-self:center!important}.ion-align-self-stretch{align-self:stretch!important}.ion-align-self-baseline{align-self:baseline!important}.ion-align-self-auto{align-self:auto!important}.ion-wrap{flex-wrap:wrap!important}.ion-nowrap{flex-wrap:nowrap!important}.ion-wrap-reverse{flex-wrap:wrap-reverse!important}.ion-justify-content-start{justify-content:flex-start!important}.ion-justify-content-center{justify-content:center!important}.ion-justify-content-end{justify-content:flex-end!important}.ion-justify-content-around{justify-content:space-around!important}.ion-justify-content-between{justify-content:space-between!important}.ion-justify-content-evenly{justify-content:space-evenly!important}.ion-align-items-start{align-items:flex-start!important}.ion-align-items-center{align-items:center!important}.ion-align-items-end{align-items:flex-end!important}.ion-align-items-stretch{align-items:stretch!important}.ion-align-items-baseline{align-items:baseline!important}:root{--color-primary: #ee9907;--page-margin-left: 3.5vw;--page-margin-right: 2vw;--page-margin-left-player: 3.5vw;--page-margin-right-player: 2vw;--page-margin-left-header: 15px;--page-margin-right-header: 15px;--page-margin-left-player-header: 15px;--page-margin-right-player-header: 2vw;--general-line-height: 1.1}.cc-theme-edgeless.cc-window{z-index:9999999}.center-question p{margin:16px 0}.sub-heading p{margin:3px 0 9px}.ion-color.sc-ion-searchbar-md-h{color:var(--ion-color-contrast);padding-left:0}h1{margin:0}body{background-color:#222428;color:#000000de}ion-grid{margin:0;padding:0;--ion-background-color: transparent}ion-grid ion-row{margin:0;padding:0;--ion-background-color: transparent}ion-grid ion-row ion-col{margin:0;padding:0;--ion-background-color: transparent}ion-modal .ion-page{background-color:#222428!important}.alert-wrapper{background-color:#222428!important}.alert-wrapper .alert-title{color:#fff}.html-alert .alert-message{white-space:pre-line}ion-select-popover{background-color:#181818!important}ion-select-popover ion-list{padding-top:0!important;padding-bottom:0!important;background-color:#181818!important}ion-select-popover ion-item::part(native){background-color:#181818!important;color:#fff}ion-checkbox::part(container){--background: none;--background-checked: var(--ion-color-primary);--border-color: #ffffff;--border-color-checked: var(--ion-color-primary)}ion-content{--ion-background-color: transparent;margin:0;padding:0}.ion-page{background-color:transparent}ion-router-outlet{--ion-background-color: transparent}::-webkit-scrollbar-thumb:hover{background:#000006}::-webkit-scrollbar{width:10px;height:10px;background:#17171700;border-radius:10px}::-webkit-scrollbar-thumb{background:#454545;border-radius:8px;width:8px}::-webkit-scrollbar-thumb:hover{background:#3c3c3c}.disabled{opacity:.5!important}.hidden{display:none}.font-nunito{font-family:Nunito}.font-play{font-family:Play}.font-exo2{font-family:"Exo 2"}.instance-title-default{font-family:"Exo 2";font-weight:800;letter-spacing:0,1em;line-height:1.1;color:#fff;margin-left:3.5vw}.instance-title-sticky,.row-title{font-family:"Exo 2";font-weight:800;letter-spacing:0,1em;line-height:1.1;color:#fff}.description{font-family:Roboto;font-weight:400;letter-spacing:0,3em;line-height:1.3;font-size:1.125em;color:#ccc}.instance-label{font-family:Roboto;font-weight:400;letter-spacing:0,3em;line-height:1.4;font-size:1.125em;color:#aaa}.ql-editor.ql-blank:before{color:#ffffffb2!important}.ql-container.ql-snow{width:100%}li{padding-left:10px!important}ol li:before{color:#fff}.mat-mdc-dialog-container{box-shadow:0 11px 15px -7px #0003,0 24px 38px 3px #00000024,0 9px 46px 8px #0000001f;background:#181818;color:#fff}.popover-content{width:500px}.tag-popover{--background: #1e1e1e;--width: 20%;--max-height: 30%}.tag-popover ion-content .back-nav{margin-left:0!important}.chip-option-select-popover{--width: 20%;--max-height: 30%}.chip-option-select-popover ion-list{padding-top:0!important;padding-bottom:0!important}.chip-option-select-popover ion-list ion-item:hover{cursor:pointer;--background: rgba(39, 39, 39)}.chip-option-select-popover .cancel-col{display:flex;justify-content:flex-start;background-color:#1e1e1e;padding-left:10px}.chip-option-select-popover .save-col{display:flex;justify-content:flex-end;background-color:#1e1e1e;padding-right:10px}.settings-popover{--background: #222428}.row-add-popover{--background: black;--offset-y: 40px;--width: 62%}.product-add-popover{--background: black;--width: 30%;--height: 40%}.criteria-add-popover{--height: auto;--max-height: 30%;--background: rgb(35, 35, 35);--width: 25%}.amp-content-title{display:none}.my-custom-interface .select-interface-option{--color: #fff;--color-hover: #db8b00;--background: #181818}.row-add-custom-popover{--offset-x: 10px;--offset-y: 10px}.add-organization-modal{align-items:center;justify-content:center}.add-organization-modal::part(content){width:50%;height:40%;background:transparent}.add-organization-modal .add-media-popover::part(content){width:10%;background:#000;padding:.5em;align-items:center;justify-content:center}.repo-dashboard-dialog{position:absolute;align-items:center;justify-content:center}.repo-dashboard-dialog::part(content){width:25%;height:40%}.persona-dialog{position:absolute;align-items:center;justify-content:center}.persona-dialog::part(content){width:350px;height:fit-content;border-radius:5px;border:1px solid black;background-color:#333}@media screen and (max-width: 1000px){.user-persona-dialog::part(content){width:85vw}}@media screen and (min-width: 1000px){.user-persona-dialog::part(content){width:525px}}.user-persona-dialog{box-shadow:0 11px 15px -7px #0003,0 24px 38px 3px #00000024,0 9px 46px 8px #0000001f;background:#000000bf;position:absolute;align-items:center;justify-content:center;z-index:1000}.user-persona-dialog::part(content){min-height:calc(100vh - 95px);max-height:calc(100vh - 95px);border-radius:15px;background-color:#000;border:2px solid #333}.user-organizations-modal{box-shadow:0 11px 15px -7px #0003,0 24px 38px 3px #00000024,0 9px 46px 8px #0000001f;background:#000000bf;position:absolute;align-items:center;justify-content:center}.user-organizations-modal::part(content){height:50vh;width:50vw;border-radius:15px;background-color:#000;border:2px solid #333}.join-dialog{position:absolute;align-items:center;justify-content:center}.join-dialog::part(content){width:380px;height:720px;border-radius:5px;border:1px solid black;background-color:#333}.join-dialog-no-orgs{position:absolute;align-items:center;justify-content:center;z-index:500}.join-dialog-no-orgs::part(content){width:380px;height:220px;border-radius:5px;border:1px solid black;background-color:#333}ion-modal.full-screen .ion-page{background-color:transparent!important}.full-screen{position:absolute;align-items:center;justify-content:center}.full-screen::part(content){width:100vw;height:100vh;background-color:#000c!important}.confirm-dialog{--width: 465px;--height: fit-content !important}.select-user-role-modal{position:absolute;align-items:center;justify-content:center;z-index:500}.select-user-role-modal::part(content){width:380px;height:300px;border-radius:5px;border:1px solid black;background-color:#333}.update-user-role-modal{position:absolute;align-items:center;justify-content:center}.update-user-role-modal::part(content){width:420px;height:60px;border-radius:5px;border:1px solid black;background-color:#333}.product-history-modal{position:absolute;align-items:center;justify-content:center}.product-history-modal::part(content){width:420px;height:fit-content;min-height:330px;border-radius:10px;border:1px solid black;background-color:#333}.add-search-modal{--offset-y: -20px;--offset-x: 24px}.add-search-modal::part(content){max-width:350px;width:100%;border-radius:11px;border:1px solid black;padding:0}.create-entity-modal::part(content){width:400px;height:auto;border-radius:5px;border:1px solid black;background-color:#333}.add-product-org-modal,.my-instances-dialog{position:absolute;align-items:center;justify-content:center}.add-product-org-modal::part(content),.my-instances-dialog::part(content){width:400px;height:fit-content;max-height:fit-content;border-radius:11px;border:1px solid black;background-color:#333}.no-backdrop-popover::part(backdrop){background:transparent}.completion-modal{position:absolute;align-items:center;justify-content:center}.completion-modal::part(content){width:650px;height:auto;border-radius:10px;border:2px solid rgb(64,64,64);background-color:#333}ion-icon[name=information-circle-outline]{display:none!important}.mat-mdc-tooltip{font-size:20px!important;background-color:#181818;max-width:unset!important}ion-toast.success-toast{--background: #232428;--box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, .2);color:#fff}ion-toast.success-toast::part(container){border:2px solid green;border-radius:5px}ion-toast.success-toast::part(icon){color:green}ion-toast.success-toast::part(button){color:#f99e00}ion-toast.completed-row-progress-toast{display:flex;justify-content:center;--width: fit-content;--background: transparent;--box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, .2);color:#fff;left:8vw!important;right:19vw!important;width:auto!important;bottom:10px;background-image:linear-gradient(to top,#181818 0%,transparent 100px);border-bottom:10px solid #181818}@media screen and (min-width: 1500px){ion-toast.completed-row-progress-toast{left:8vw!important;right:20vw!important}}@media screen and (min-width: 1700px){ion-toast.completed-row-progress-toast{left:7vw!important;right:20vw!important}}@media screen and (max-width: 1200px){ion-toast.completed-row-progress-toast{left:10vw!important;right:20vw!important}}@media screen and (max-width: 1000px){ion-toast.completed-row-progress-toast{left:10vw!important;right:18vw!important}}@media screen and (max-width: 960px){ion-toast.completed-row-progress-toast{left:0!important;right:0!important;background:none}}ion-toast.completed-row-progress-toast::part(message){font-size:1.125em;font-weight:500;padding-inline:12px;padding-inline-end:0px;letter-spacing:.03em;display:flex;justify-content:center;align-items:center;gap:5px}ion-toast.completed-row-progress-toast::part(container){border:green solid 2px;border-radius:7px;background:#1f281d}ion-toast.completed-row-progress-toast::part(icon){color:green}ion-toast.completed-row-progress-toast::part(button){color:#000;background-color:#f99e00;border-radius:5px;font-weight:600}@media screen and (max-width: 960px){ion-toast.completed-row-progress-toast::part(container){margin-bottom:80px}}ion-toast.started-row-progress-toast{display:flex;justify-content:center;--width: fit-content;--background: transparent;color:#e0e0e0;--background-hover: none;--box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, .2);left:8vw!important;right:19vw!important;width:auto!important;bottom:10px;background-image:linear-gradient(to top,#181818 0%,transparent 100px);border-bottom:10px solid #181818}@media screen and (min-width: 1500px){ion-toast.started-row-progress-toast{left:8vw!important;right:20vw!important}}@media screen and (min-width: 1700px){ion-toast.started-row-progress-toast{left:7vw!important;right:20vw!important}}@media screen and (max-width: 1200px){ion-toast.started-row-progress-toast{left:10vw!important;right:20vw!important}}@media screen and (max-width: 1000px){ion-toast.started-row-progress-toast{left:10vw!important;right:18vw!important}}@media screen and (max-width: 960px){ion-toast.started-row-progress-toast{left:0!important;right:0!important;background:none}}ion-toast.started-row-progress-toast::part(message){font-size:1.125em;padding-inline:12px;padding-inline-end:0px;font-weight:500;letter-spacing:.03em}ion-toast.started-row-progress-toast::part(container){width:fit-content;border:#bb7000 solid 2px;border-radius:7px}@media screen and (max-width: 960px){ion-toast.started-row-progress-toast::part(container){margin-bottom:80px}}ion-toast.not-started-row-progress-toast{display:flex;justify-content:center;--width: fit-content;--background: transparent;color:#e0e0e0;--background-hover: none;--box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, .2);left:8vw!important;right:19vw!important;width:auto!important;bottom:10px;background-image:linear-gradient(to top,#181818 0%,transparent 100px);border-bottom:10px solid #181818}@media screen and (min-width: 1500px){ion-toast.not-started-row-progress-toast{left:8vw!important;right:20vw!important}}@media screen and (min-width: 1700px){ion-toast.not-started-row-progress-toast{left:7vw!important;right:20vw!important}}@media screen and (max-width: 1200px){ion-toast.not-started-row-progress-toast{left:10vw!important;right:20vw!important}}@media screen and (max-width: 1000px){ion-toast.not-started-row-progress-toast{left:10vw!important;right:18vw!important}}@media screen and (max-width: 960px){ion-toast.not-started-row-progress-toast{left:0!important;right:0!important;background:none}}ion-toast.not-started-row-progress-toast::part(message){font-size:1.125em;padding-inline:12px;padding-inline-end:0px;font-weight:500;letter-spacing:.03em}ion-toast.not-started-row-progress-toast::part(container){width:fit-content;border:rgb(121,121,121) solid 2px;border-radius:7px}@media screen and (max-width: 960px){ion-toast.not-started-row-progress-toast::part(container){margin-bottom:80px}}ion-toast.class-assignment-toast{--background: #232428;--box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, .2);color:#fff;--background-hover: none;transform:translateY(-50px)!important}ion-toast.class-assignment-toast::part(container){border:1px solid #f99e00;border-radius:5px}ion-toast.class-assignment-toast::part(icon){color:#f99e00}ion-toast.class-assignment-toast::part(button){color:#f99e00}ion-toast.error-toast{--background: #232428;--box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, .2);--color: #fff}ion-toast.error-toast::part(container){border:1px solid red;border-radius:5px}ion-toast.error-toast::part(icon){color:red}ion-toast.error-toast::part(button){color:red}.badge-link{cursor:pointer!important}.mat-mdc-slide-toggle{margin-left:20px;margin-top:5px;margin-bottom:5px}.mat-mdc-slide-toggle .mdc-label{color:#fff!important}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:#222!important}.mat-mdc-standard-chip{background-color:#181818!important}.mdc-evolution-chip__text-label{color:#fff!important}.mdc-evolution-chip__icon--trailing{color:#747474!important}.riasec-score .ngx-charts .gridline-path{stroke:#181818!important}.riasec-score .polar-chart .polar-chart-background{fill:#242424!important}.riasec-score .ngx-charts text{fill:#fff!important}#hubspot-messages-iframe-container{min-height:96px;min-width:100px;width:96px;height:100px;color-scheme:light;margin-right:7px}.status-foot-container{position:absolute;left:25px;z-index:999;bottom:-8px;width:100px;height:26px}.status-foot-container .inner{padding:2px 5px 1px;font-size:10px;border-radius:3px;color:#fff;letter-spacing:.1em;line-height:normal;width:max-content;display:inline-block;font-weight:700}.status-inprogress{outline:1px solid orange!important;border-radius:7px;margin-bottom:6px}.status-complete{outline:1px solid green!important;height:100%;border-radius:7px}access-widget-ui::part(acsb-trigger){width:60px!important;height:60px!important}.cc-window.cc-banner{align-items:center;justify-content:center}.cc-btn{margin:9px 30px 7px!important;border-radius:4px;padding:9px 15px 7px!important}.conveythis-widget-main,#conveythis-wrapper{width:90px!important}@media screen and (max-width: 960px){access-widget-ui::part(acsb-trigger){display:none}.instance-label{font-size:.75em}#conveythis-wrapper{display:none;visibility:hidden;bottom:90px!important}}.conveythis-widget-language{padding-left:5px!important}.full-parent-height{height:100%}.feature-type-select-popover{--width: 400px}.custom-popover-center::part(content){top:50%!important;left:50%!important;height:fit-content;max-height:fit-content;transform:translate(-50%,-50%)!important}.cc-window{opacity:1;transition:opacity 1s ease}.cc-window.cc-invisible{opacity:0}.cc-animate.cc-revoke{transition:transform 1s ease}.cc-animate.cc-revoke.cc-top{transform:translateY(-2em)}.cc-animate.cc-revoke.cc-bottom{transform:translateY(2em)}.cc-animate.cc-revoke.cc-active.cc-top,.cc-animate.cc-revoke.cc-active.cc-bottom,.cc-revoke:hover{transform:translateY(0)}.cc-grower{max-height:0;overflow:hidden;transition:max-height 1s}.cc-revoke,.cc-window{position:fixed;overflow:hidden;box-sizing:border-box;font-family:Helvetica,Calibri,Arial,sans-serif;font-size:16px;line-height:1.5em;display:flex;flex-wrap:nowrap;z-index:9999}.cc-window.cc-static{position:static}.cc-window.cc-floating{padding:2em;max-width:24em;flex-direction:column}.cc-window.cc-banner{padding:1em 1.8em;width:100%;flex-direction:row}.cc-revoke{padding:.5em}.cc-revoke:hover{text-decoration:underline}.cc-header{font-size:18px;font-weight:700}.cc-btn,.cc-close,.cc-link,.cc-revoke{cursor:pointer}.cc-link{opacity:.8;display:inline-block;padding:.2em;text-decoration:underline}.cc-link:hover{opacity:1}.cc-link:active,.cc-link:visited{color:initial}.cc-btn{display:block;padding:.4em .8em;font-size:.9em;font-weight:700;border-width:2px;border-style:solid;text-align:center;white-space:nowrap}.cc-highlight .cc-btn:first-child{background-color:transparent;border-color:transparent}.cc-highlight .cc-btn:first-child:focus,.cc-highlight .cc-btn:first-child:hover{background-color:transparent;text-decoration:underline}.cc-close{display:block;position:absolute;top:.5em;right:.5em;font-size:1.6em;opacity:.9;line-height:.75}.cc-close:focus,.cc-close:hover{opacity:1}.cc-revoke.cc-top{top:0;left:3em;border-bottom-left-radius:.5em;border-bottom-right-radius:.5em}.cc-revoke.cc-bottom{bottom:0;left:3em;border-top-left-radius:.5em;border-top-right-radius:.5em}.cc-revoke.cc-left{left:3em;right:unset}.cc-revoke.cc-right{right:3em;left:unset}.cc-top{top:1em}.cc-left{left:1em}.cc-right{right:1em}.cc-bottom{bottom:1em}.cc-floating>.cc-link{margin-bottom:1em}.cc-floating .cc-message{display:block;margin-bottom:1em}.cc-window.cc-floating .cc-compliance{flex:1 0 auto}.cc-window.cc-banner{align-items:center}.cc-banner.cc-top{left:0;right:0;top:0}.cc-banner.cc-bottom{left:0;right:0;bottom:0}.cc-banner .cc-message{display:block;flex:1 1 auto;max-width:100%;margin-right:1em}.cc-compliance{display:flex;align-items:center;align-content:space-between}.cc-floating .cc-compliance>.cc-btn{flex:1}.cc-btn+.cc-btn{margin-left:.5em}@media print{.cc-revoke,.cc-window{display:none}}@media screen and (max-width:900px){.cc-btn{white-space:normal}}@media screen and (max-width:414px) and (orientation:portrait),screen and (max-width:736px) and (orientation:landscape){.cc-window.cc-top{top:0}.cc-window.cc-bottom{bottom:0}.cc-window.cc-banner,.cc-window.cc-floating,.cc-window.cc-left,.cc-window.cc-right{left:0;right:0}.cc-window.cc-banner{flex-direction:column}.cc-window.cc-banner .cc-compliance{flex:1 1 auto}.cc-window.cc-floating{max-width:none}.cc-window .cc-message{margin-bottom:1em}.cc-window.cc-banner{align-items:unset}.cc-window.cc-banner .cc-message{margin-right:0}}.cc-floating.cc-theme-classic{padding:1.2em;border-radius:5px}.cc-floating.cc-type-info.cc-theme-classic .cc-compliance{text-align:center;display:inline;flex:none}.cc-theme-classic .cc-btn{border-radius:5px}.cc-theme-classic .cc-btn:last-child{min-width:140px}.cc-floating.cc-type-info.cc-theme-classic .cc-btn{display:inline-block}.cc-theme-edgeless.cc-window{padding:0}.cc-floating.cc-theme-edgeless .cc-message{margin:2em 2em 1.5em}.cc-banner.cc-theme-edgeless .cc-btn{margin:0;padding:.8em 1.8em;height:100%}.cc-banner.cc-theme-edgeless .cc-message{margin-left:1em}.cc-floating.cc-theme-edgeless .cc-btn+.cc-btn{margin-left:0}/*!
 * Quill Editor v2.0.2
 * https://quilljs.com
 * Copyright (c) 2017-2024, Slab
 * Copyright (c) 2014, Jason Chen
 * Copyright (c) 2013, salesforce.com
 */.ql-container{box-sizing:border-box;font-family:Helvetica,Arial,sans-serif;font-size:13px;height:100%;margin:0;position:relative}.ql-container.ql-disabled .ql-tooltip{visibility:hidden}.ql-container:not(.ql-disabled) li[data-list=checked]>.ql-ui,.ql-container:not(.ql-disabled) li[data-list=unchecked]>.ql-ui{cursor:pointer}.ql-clipboard{left:-100000px;height:1px;overflow-y:hidden;position:absolute;top:50%}.ql-clipboard p{margin:0;padding:0}.ql-editor{box-sizing:border-box;counter-reset:list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;line-height:1.42;height:100%;outline:none;overflow-y:auto;padding:12px 15px;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap;word-wrap:break-word}.ql-editor>*{cursor:text}.ql-editor p,.ql-editor ol,.ql-editor pre,.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6{margin:0;padding:0}@supports (counter-set:none){.ql-editor p,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6{counter-set:list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor p,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6{counter-reset:list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}}.ql-editor table{border-collapse:collapse}.ql-editor td{border:1px solid #000;padding:2px 5px}.ql-editor ol{padding-left:1.5em}.ql-editor li{list-style-type:none;padding-left:1.5em;position:relative}.ql-editor li>.ql-ui:before{display:inline-block;margin-left:-1.5em;margin-right:.3em;text-align:right;white-space:nowrap;width:1.2em}.ql-editor li[data-list=checked]>.ql-ui,.ql-editor li[data-list=unchecked]>.ql-ui{color:#777}.ql-editor li[data-list=bullet]>.ql-ui:before{content:"\2022"}.ql-editor li[data-list=checked]>.ql-ui:before{content:"\2611"}.ql-editor li[data-list=unchecked]>.ql-ui:before{content:"\2610"}@supports (counter-set:none){.ql-editor li[data-list]{counter-set:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list]{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}}.ql-editor li[data-list=ordered]{counter-increment:list-0}.ql-editor li[data-list=ordered]>.ql-ui:before{content:counter(list-0,decimal) ". "}.ql-editor li[data-list=ordered].ql-indent-1{counter-increment:list-1}.ql-editor li[data-list=ordered].ql-indent-1>.ql-ui:before{content:counter(list-1,lower-alpha) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-1{counter-set:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}}.ql-editor li[data-list=ordered].ql-indent-2{counter-increment:list-2}.ql-editor li[data-list=ordered].ql-indent-2>.ql-ui:before{content:counter(list-2,lower-roman) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-2{counter-set:list-3 list-4 list-5 list-6 list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}}.ql-editor li[data-list=ordered].ql-indent-3{counter-increment:list-3}.ql-editor li[data-list=ordered].ql-indent-3>.ql-ui:before{content:counter(list-3,decimal) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-3{counter-set:list-4 list-5 list-6 list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}}.ql-editor li[data-list=ordered].ql-indent-4{counter-increment:list-4}.ql-editor li[data-list=ordered].ql-indent-4>.ql-ui:before{content:counter(list-4,lower-alpha) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-4{counter-set:list-5 list-6 list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}}.ql-editor li[data-list=ordered].ql-indent-5{counter-increment:list-5}.ql-editor li[data-list=ordered].ql-indent-5>.ql-ui:before{content:counter(list-5,lower-roman) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-5{counter-set:list-6 list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}}.ql-editor li[data-list=ordered].ql-indent-6{counter-increment:list-6}.ql-editor li[data-list=ordered].ql-indent-6>.ql-ui:before{content:counter(list-6,decimal) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-6{counter-set:list-7 list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-6{counter-reset:list-7 list-8 list-9}}.ql-editor li[data-list=ordered].ql-indent-7{counter-increment:list-7}.ql-editor li[data-list=ordered].ql-indent-7>.ql-ui:before{content:counter(list-7,lower-alpha) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-7{counter-set:list-8 list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-7{counter-reset:list-8 list-9}}.ql-editor li[data-list=ordered].ql-indent-8{counter-increment:list-8}.ql-editor li[data-list=ordered].ql-indent-8>.ql-ui:before{content:counter(list-8,lower-roman) ". "}@supports (counter-set:none){.ql-editor li[data-list].ql-indent-8{counter-set:list-9}}@supports not (counter-set:none){.ql-editor li[data-list].ql-indent-8{counter-reset:list-9}}.ql-editor li[data-list=ordered].ql-indent-9{counter-increment:list-9}.ql-editor li[data-list=ordered].ql-indent-9>.ql-ui:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:3em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:4.5em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:3em}.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:4.5em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:7.5em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:7.5em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:9em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:10.5em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:9em}.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:10.5em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:13.5em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:13.5em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:15em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:16.5em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:15em}.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:16.5em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:19.5em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:19.5em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:21em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:22.5em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:21em}.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:22.5em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:24em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:25.5em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:24em}.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:25.5em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:27em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:28.5em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:27em}.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:28.5em}.ql-editor li.ql-direction-rtl{padding-right:1.5em}.ql-editor li.ql-direction-rtl>.ql-ui:before{margin-left:.3em;margin-right:-1.5em;text-align:left}.ql-editor table{table-layout:fixed;width:100%}.ql-editor table td{outline:none}.ql-editor .ql-code-block-container{font-family:monospace}.ql-editor .ql-video{display:block;max-width:100%}.ql-editor .ql-video.ql-align-center{margin:0 auto}.ql-editor .ql-video.ql-align-right{margin:0 0 0 auto}.ql-editor .ql-bg-black{background-color:#000}.ql-editor .ql-bg-red{background-color:#e60000}.ql-editor .ql-bg-orange{background-color:#f90}.ql-editor .ql-bg-yellow{background-color:#ff0}.ql-editor .ql-bg-green{background-color:#008a00}.ql-editor .ql-bg-blue{background-color:#06c}.ql-editor .ql-bg-purple{background-color:#93f}.ql-editor .ql-color-white{color:#fff}.ql-editor .ql-color-red{color:#e60000}.ql-editor .ql-color-orange{color:#f90}.ql-editor .ql-color-yellow{color:#ff0}.ql-editor .ql-color-green{color:#008a00}.ql-editor .ql-color-blue{color:#06c}.ql-editor .ql-color-purple{color:#93f}.ql-editor .ql-font-serif{font-family:Georgia,Times New Roman,serif}.ql-editor .ql-font-monospace{font-family:Monaco,Courier New,monospace}.ql-editor .ql-size-small{font-size:.75em}.ql-editor .ql-size-large{font-size:1.5em}.ql-editor .ql-size-huge{font-size:2.5em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor .ql-ui{position:absolute}.ql-editor.ql-blank:before{color:#0009;content:attr(data-placeholder);font-style:italic;left:15px;pointer-events:none;position:absolute;right:15px}.ql-snow.ql-toolbar:after,.ql-snow .ql-toolbar:after{clear:both;content:"";display:table}.ql-snow.ql-toolbar button,.ql-snow .ql-toolbar button{background:none;border:none;cursor:pointer;display:inline-block;float:left;height:24px;padding:3px 5px;width:28px}.ql-snow.ql-toolbar button svg,.ql-snow .ql-toolbar button svg{float:left;height:100%}.ql-snow.ql-toolbar button:active:hover,.ql-snow .ql-toolbar button:active:hover{outline:none}.ql-snow.ql-toolbar input.ql-image[type=file],.ql-snow .ql-toolbar input.ql-image[type=file]{display:none}.ql-snow.ql-toolbar button:hover,.ql-snow .ql-toolbar button:hover,.ql-snow.ql-toolbar button:focus,.ql-snow .ql-toolbar button:focus,.ql-snow.ql-toolbar button.ql-active,.ql-snow .ql-toolbar button.ql-active,.ql-snow.ql-toolbar .ql-picker-label:hover,.ql-snow .ql-toolbar .ql-picker-label:hover,.ql-snow.ql-toolbar .ql-picker-label.ql-active,.ql-snow .ql-toolbar .ql-picker-label.ql-active,.ql-snow.ql-toolbar .ql-picker-item:hover,.ql-snow .ql-toolbar .ql-picker-item:hover,.ql-snow.ql-toolbar .ql-picker-item.ql-selected,.ql-snow .ql-toolbar .ql-picker-item.ql-selected{color:#06c}.ql-snow.ql-toolbar button:hover .ql-fill,.ql-snow .ql-toolbar button:hover .ql-fill,.ql-snow.ql-toolbar button:focus .ql-fill,.ql-snow .ql-toolbar button:focus .ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill{fill:#06c}.ql-snow.ql-toolbar button:hover .ql-stroke,.ql-snow .ql-toolbar button:hover .ql-stroke,.ql-snow.ql-toolbar button:focus .ql-stroke,.ql-snow .ql-toolbar button:focus .ql-stroke,.ql-snow.ql-toolbar button.ql-active .ql-stroke,.ql-snow .ql-toolbar button.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,.ql-snow.ql-toolbar button:hover .ql-stroke-miter,.ql-snow .ql-toolbar button:hover .ql-stroke-miter,.ql-snow.ql-toolbar button:focus .ql-stroke-miter,.ql-snow .ql-toolbar button:focus .ql-stroke-miter,.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar button.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter{stroke:#06c}@media (pointer:coarse){.ql-snow.ql-toolbar button:hover:not(.ql-active),.ql-snow .ql-toolbar button:hover:not(.ql-active){color:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill{fill:#444}.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,.ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,.ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter{stroke:#444}}.ql-snow,.ql-snow *{box-sizing:border-box}.ql-snow .ql-hidden{display:none}.ql-snow .ql-out-bottom,.ql-snow .ql-out-top{visibility:hidden}.ql-snow .ql-tooltip{position:absolute;transform:translateY(10px)}.ql-snow .ql-tooltip a{cursor:pointer;text-decoration:none}.ql-snow .ql-tooltip.ql-flip{transform:translateY(-10px)}.ql-snow .ql-formats{display:inline-block;vertical-align:middle}.ql-snow .ql-formats:after{clear:both;content:"";display:table}.ql-snow .ql-stroke{fill:none;stroke:#444;stroke-linecap:round;stroke-linejoin:round;stroke-width:2}.ql-snow .ql-stroke-miter{fill:none;stroke:#444;stroke-miterlimit:10;stroke-width:2}.ql-snow .ql-fill,.ql-snow .ql-stroke.ql-fill{fill:#444}.ql-snow .ql-empty{fill:none}.ql-snow .ql-even{fill-rule:evenodd}.ql-snow .ql-thin,.ql-snow .ql-stroke.ql-thin{stroke-width:1}.ql-snow .ql-transparent{opacity:.4}.ql-snow .ql-direction svg:last-child{display:none}.ql-snow .ql-direction.ql-active svg:last-child{display:inline}.ql-snow .ql-direction.ql-active svg:first-child{display:none}.ql-snow .ql-editor h1{font-size:2em}.ql-snow .ql-editor h2{font-size:1.5em}.ql-snow .ql-editor h3{font-size:1.17em}.ql-snow .ql-editor h4{font-size:1em}.ql-snow .ql-editor h5{font-size:.83em}.ql-snow .ql-editor h6{font-size:.67em}.ql-snow .ql-editor a{text-decoration:underline}.ql-snow .ql-editor blockquote{border-left:4px solid #ccc;margin-bottom:5px;margin-top:5px;padding-left:16px}.ql-snow .ql-editor code,.ql-snow .ql-editor .ql-code-block-container{background-color:#f0f0f0;border-radius:3px}.ql-snow .ql-editor .ql-code-block-container{margin-bottom:5px;margin-top:5px;padding:5px 10px}.ql-snow .ql-editor code{font-size:85%;padding:2px 4px}.ql-snow .ql-editor .ql-code-block-container{background-color:#23241f;color:#f8f8f2;overflow:visible}.ql-snow .ql-editor img{max-width:100%}.ql-snow .ql-picker{color:#444;display:inline-block;float:left;font-size:14px;font-weight:500;height:24px;position:relative;vertical-align:middle}.ql-snow .ql-picker-label{cursor:pointer;display:inline-block;height:100%;padding-left:8px;padding-right:2px;position:relative;width:100%}.ql-snow .ql-picker-label:before{display:inline-block;line-height:22px}.ql-snow .ql-picker-options{background-color:#fff;display:none;min-width:100%;padding:4px 8px;position:absolute;white-space:nowrap}.ql-snow .ql-picker-options .ql-picker-item{cursor:pointer;display:block;padding-bottom:5px;padding-top:5px}.ql-snow .ql-picker.ql-expanded .ql-picker-label{color:#ccc;z-index:2}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill{fill:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke{stroke:#ccc}.ql-snow .ql-picker.ql-expanded .ql-picker-options{display:block;margin-top:-1px;top:100%;z-index:1}.ql-snow .ql-color-picker,.ql-snow .ql-icon-picker{width:28px}.ql-snow .ql-color-picker .ql-picker-label,.ql-snow .ql-icon-picker .ql-picker-label{padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-label svg,.ql-snow .ql-icon-picker .ql-picker-label svg{right:4px}.ql-snow .ql-icon-picker .ql-picker-options{padding:4px 0}.ql-snow .ql-icon-picker .ql-picker-item{height:24px;width:24px;padding:2px 4px}.ql-snow .ql-color-picker .ql-picker-options{padding:3px 5px;width:152px}.ql-snow .ql-color-picker .ql-picker-item{border:1px solid transparent;float:left;height:16px;margin:2px;padding:0;width:16px}.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg{position:absolute;margin-top:-9px;right:0;top:50%;width:18px}.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=""]):before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=""]):before{content:attr(data-label)}.ql-snow .ql-picker.ql-header{width:98px}.ql-snow .ql-picker.ql-header .ql-picker-label:before,.ql-snow .ql-picker.ql-header .ql-picker-item:before{content:"Normal"}.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{content:"Heading 1"}.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{content:"Heading 2"}.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{content:"Heading 3"}.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{content:"Heading 4"}.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{content:"Heading 5"}.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]:before,.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{content:"Heading 6"}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]:before{font-size:2em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]:before{font-size:1.5em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]:before{font-size:1.17em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]:before{font-size:1em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]:before{font-size:.83em}.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]:before{font-size:.67em}.ql-snow .ql-picker.ql-font{width:108px}.ql-snow .ql-picker.ql-font .ql-picker-label:before,.ql-snow .ql-picker.ql-font .ql-picker-item:before{content:"Sans Serif"}.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]:before,.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{content:"Serif"}.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]:before,.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{content:"Monospace"}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]:before{font-family:Georgia,Times New Roman,serif}.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]:before{font-family:Monaco,Courier New,monospace}.ql-snow .ql-picker.ql-size{width:98px}.ql-snow .ql-picker.ql-size .ql-picker-label:before,.ql-snow .ql-picker.ql-size .ql-picker-item:before{content:"Normal"}.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]:before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{content:"Small"}.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]:before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{content:"Large"}.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]:before,.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{content:"Huge"}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]:before{font-size:10px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]:before{font-size:18px}.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]:before{font-size:32px}.ql-snow .ql-color-picker.ql-background .ql-picker-item{background-color:#fff}.ql-snow .ql-color-picker.ql-color .ql-picker-item{background-color:#000}.ql-code-block-container{position:relative}.ql-code-block-container .ql-ui{right:5px;top:5px}.ql-toolbar.ql-snow{border:1px solid #ccc;box-sizing:border-box;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;padding:8px}.ql-toolbar.ql-snow .ql-formats{margin-right:15px}.ql-toolbar.ql-snow .ql-picker-label{border:1px solid transparent}.ql-toolbar.ql-snow .ql-picker-options{border:1px solid transparent;box-shadow:#0003 0 2px 8px}.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label,.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options{border-color:#ccc}.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover{border-color:#000}.ql-toolbar.ql-snow+.ql-container.ql-snow{border-top:0}.ql-snow .ql-tooltip{background-color:#fff;border:1px solid #ccc;box-shadow:0 0 5px #ddd;color:#444;padding:5px 12px;white-space:nowrap}.ql-snow .ql-tooltip:before{content:"Visit URL:";line-height:26px;margin-right:8px}.ql-snow .ql-tooltip input[type=text]{display:none;border:1px solid #ccc;font-size:13px;height:26px;margin:0;padding:3px 5px;width:170px}.ql-snow .ql-tooltip a.ql-preview{display:inline-block;max-width:200px;overflow-x:hidden;text-overflow:ellipsis;vertical-align:top}.ql-snow .ql-tooltip a.ql-action:after{border-right:1px solid #ccc;content:"Edit";margin-left:16px;padding-right:8px}.ql-snow .ql-tooltip a.ql-remove:before{content:"Remove";margin-left:8px}.ql-snow .ql-tooltip a{line-height:26px}.ql-snow .ql-tooltip.ql-editing a.ql-preview,.ql-snow .ql-tooltip.ql-editing a.ql-remove{display:none}.ql-snow .ql-tooltip.ql-editing input[type=text]{display:inline-block}.ql-snow .ql-tooltip.ql-editing a.ql-action:after{border-right:0;content:"Save";padding-right:0}.ql-snow .ql-tooltip[data-mode=link]:before{content:"Enter link:"}.ql-snow .ql-tooltip[data-mode=formula]:before{content:"Enter formula:"}.ql-snow .ql-tooltip[data-mode=video]:before{content:"Enter video:"}.ql-snow a{color:#06c}.ql-container.ql-snow{border:1px solid #ccc}
