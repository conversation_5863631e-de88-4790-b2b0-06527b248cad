namespace EdgeFactor.Common.Models.Asset
{
    public class FileModel
    {
        public FileModel(byte[] fileContent, string contentType, string fileName)
        {
            FileContent = fileContent;
            ContentType = contentType;
            FileName = fileName;
        }

        public byte[] FileContent { get; set; } = null!;
        public string ContentType { get; set; } = null!;
        public string FileName { get; set; } = null!;
    }
}
