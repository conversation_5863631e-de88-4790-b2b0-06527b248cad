
namespace EdgeFactor.Common.Models.Row
{
    public class CustomContentDetail
    {
        public Guid RowId { get; set; }
        public Guid? ContentId { get; set; }
        public bool? IsDeleted { get; set; }
        public string? Title { get; set; }
        public string? Description { get; set; }
        public string? BackgroundImageAssetId { get; set; }
        public string? ButtonText { get; set; }
        public string? ButtonUrl { get; set; }
        public int ContentOrder { get; set; }
        public bool SameUrlNavigation { get; set; }
    }
}
