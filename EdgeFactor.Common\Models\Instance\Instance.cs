using EdgeFactor.Common.DB.Models;

namespace EdgeFactor.Common.Models.Instance
{
    public class Instance : InstanceBase
    {
        public Instance()
        {
        }
        public Instance(Instances instance, Guid? userId = null) : this(
            instance.Id,
            instance.Title,
            instance.Description,
            instance.IconAssetId,
            instance?.CoverMediaAssetId,
            instance.ActionUrl,
            instance.Status,
            instance.IsDefault,
            instance.Feature != null ? new Feature.Feature(instance.Feature) : null,
            instance?.OrganizationId,
            instance?.CoverMediaAsset != null ? new Asset.Asset(instance.CoverMediaAsset) : null,
            instance?.JoinCode,
            instance?.IsJoinCodeInstance,
            instance.UserId,
            instance.UserId != null ? instance.UserId == userId : false,
            instance.Organization != null && instance.Organization?.ProductOrganizations != null ? instance.Organization.ProductOrganizations.Any(x => x.Product.Name.ToLower() == "scorm") : false,
            instance.Slug
        )
        { }

        public Instance(string title, Guid featureId, Guid organizationId)
        {
            Title = title;
            FeatureId = featureId;
            OrganizationId = organizationId;
        }

        public Instance(Guid id, Guid featureId, string title, string? status, Guid? organizationId, Guid? gradeId, string? section, Guid? subject, Guid? term, DateTime createdDate, string? joinCode, bool? isJoinCodeInstance)
        {
            Id = id;
            FeatureId = featureId;
            Title = title;
            Status = status;
            OrganizationId = organizationId;
            GradeId = gradeId;
            Section = section;
            Subject = subject;
            Term = term;
            CreatedDate = createdDate;
            JoinCode = joinCode;
            IsJoinCodeInstance = isJoinCodeInstance;
        }

        public Instance(InstanceBase instanceBase)
        {
            FeatureId = instanceBase.FeatureId;
            Title = instanceBase.Title;
            Description = instanceBase.Description;
            IconAssetId = instanceBase.IconAssetId;
            ActionUrl = instanceBase.ActionUrl;
            Status = instanceBase.Status;
            IsDefault = instanceBase.IsDefault;
            OrganizationId = instanceBase.OrganizationId;
            ProductId = instanceBase.ProductId;
            CoverMediaAssetId = instanceBase.CoverMediaAssetId;
            IsOwner = instanceBase.IsOwner;
            HasScormAccess = instanceBase.HasScormAccess;
            Slug = instanceBase.Slug;
        }

        public Instance(Instance instance)
        {
            Id = instance.Id;
            FeatureId = instance.FeatureId;
            Title = instance.Title;
            Description = instance.Description;
            IconAssetId = instance.IconAssetId;
            ActionUrl = instance.ActionUrl;
            Status = instance.Status;
            IsDefault = instance.IsDefault;
            OrganizationId = instance.OrganizationId;
            ProductId = instance.ProductId;
            CoverMediaAssetId = instance.CoverMediaAssetId;
            IsOwner = instance.IsOwner;
            HasScormAccess = instance.HasScormAccess;
            Grade = instance.Grade;
            Section = instance.Section;
            Subject = instance.Subject;
            Term = instance.Term;
            CreatedDate = instance.CreatedDate;
            JoinCode = instance.JoinCode;
            IsJoinCodeInstance = instance.IsJoinCodeInstance;
            EducatorId = instance.EducatorId;
            DueDate = instance.DueDate;
        }

        public Instance(Guid id, string title, string? description, Guid? iconAssetId, Guid? coverMediaAssetId, string? actionUrl, string? status, bool? isDefault, Feature.Feature feature, Guid? organizationId, Asset.Asset? asset, string? joinCode, bool? isJoinCodeInstance, Guid? userId, bool? isOwner, bool? hasScormAccess, string slug)
        {
            Id = id;
            Title = title;
            Description = description;
            IconAssetId = iconAssetId;
            ActionUrl = actionUrl;
            Status = status;
            IsDefault = isDefault;
            Feature = feature;
            OrganizationId = organizationId;
            CoverMediaAsset = asset;
            CoverMediaAssetId = coverMediaAssetId;
            JoinCode = joinCode;
            IsJoinCodeInstance = isJoinCodeInstance;
            AuthorId = userId;
            IsOwner = isOwner;
            HasScormAccess = hasScormAccess;
            Slug = slug;
        }

        public Instance(Instances instance, EarningCriteria? earningCriteria, bool isEnrolled, Guid? userId = null, UserProgress? userInstanceProgress = null)
        {
            Id = instance.Id;
            Title = instance.Title;
            Description = instance.Description;
            IconAssetId = instance.IconAssetId;
            CoverMediaAssetId = instance?.CoverMediaAssetId;
            ActionUrl = instance.ActionUrl;
            Status = instance.Status;
            IsDefault = instance.IsDefault;
            Feature = instance.Feature != null ? new Feature.Feature(instance.Feature) : null;
            OrganizationId = instance?.OrganizationId;
            CoverMediaAsset = instance?.CoverMediaAsset != null ? new Asset.Asset(instance.CoverMediaAsset) : null;
            JoinCode = instance?.JoinCode;
            UserId = instance.UserId;
            IsJoinCodeInstance = (instance.InstanceUsers.Any(x => x.UserId == userId || (x.Role.Name == "Instructor" || x.Role.Name == "Administrator")) && instance.IsJoinCodeInstance == true);
            HasScormAccess = instance.Organization != null && instance.Organization?.ProductOrganizations != null
                ? instance.Organization.ProductOrganizations.Any(x => x.Product.Name.ToLower() == "scorm")
                : false;
            EarningCriteria = earningCriteria;
            IsEnrolled = isEnrolled;
            IsRetake = instance.IsRetake;
            IsSubmitted = userInstanceProgress?.IsSubmitted;
            IsGraded = userInstanceProgress?.IsGraded;
            Feedback = userInstanceProgress?.Feedback;
            Grade = userInstanceProgress?.Grade;
            TotalMarks = userInstanceProgress?.TotalMarks;
            Slug = instance.Slug;
            IsComplete = userInstanceProgress?.Progress >= 100;
            EducatorId = instance.EducatorId;
            DueDate = instance.DueDate != null ? instance.DueDate.ToEpoch() : null;
        }

        public Guid? UserId { get; set; }
        public Guid Id { get; set; }
        public Feature.Feature Feature { get; set; }
        public Asset.Asset? CoverMediaAsset { get; set; }
        public string? JoinCode { get; set; }
        public bool? IsJoinCodeInstance { get; set; }
        public Guid? AuthorId { get; set; }
        public EarningCriteria? EarningCriteria { get; set; }
        public bool? IsEnrolled { get; set; }
        public bool? IsRetake { get; set; }
        public bool? IsSubmitted { get; set; }
        public bool? IsGraded { get; set; }
        public string? Feedback { get; set; }
        public decimal? Grade { get; set; }
        public Guid? GradeId { get; set; }
        public int? TotalMarks { get; set; }
        public DateTime CreatedDate { get; set; }
        public Guid? Term { get; set; }
        public Guid? Subject { get; set; }
        public string? Section { get; set; }
        public bool? IsComplete { get; set; }
        public Guid? EducatorId { get; set; }
        public int? InstanceInterest { get; set; }
    }

    public record InstanceResult(Instance Instance, string Status, decimal Grade, bool IsGraded, bool ContainsGrading)
    {
        public InstanceResult(Instance instance, VwInstanceProgressGrade progressGrade, bool containsGrading) : this(instance, progressGrade?.Status, (decimal)(progressGrade?.Grade ?? 0), progressGrade?.IsGraded ?? false, containsGrading) { }
    }
}
