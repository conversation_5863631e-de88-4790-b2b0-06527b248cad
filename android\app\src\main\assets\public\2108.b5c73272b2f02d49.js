"use strict";(self.webpackChunkpublic=self.webpackChunkpublic||[]).push([[2108],{72108:(so,ce,l)=>{l.r(ce),l.d(ce,{FeatureInstanceModule:()=>io});var I=l(74710),E=l(61017),L=l(1875),Y=l(26110),Ve=l(2863),je=l(33863),e=l(93953),S=l(83084),N=l(6081),O=l(29783),B=l(45312),y=l(21413),Q=l(27468),u=l(56977),w=l(94821),v=l(45243),M=l(18544),H=l(7838),C=l(60177),d=l(73465),V=l(90882),j=l(64985),Xe=l(6255),$e=l(58135),W=l(68512),T=l(89524);function Ge(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",1)(1,"ion-button",3),e.bIt("click",function(){e.eBV(n);const i=e.XpG();return e.Njj(i.setupStepsOnEdit())}),e.nrm(2,"ion-icon",4),e.EFF(3," Edit "),e.k0s()()}}function Ue(o,r){if(1&o&&(e.j41(0,"ion-label",5),e.EFF(1),e.k0s()),2&o){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",n.section.title.length>0?n.section.title:"Section"," ")}}function De(o,r){if(1&o&&(e.j41(0,"ion-label",5),e.EFF(1),e.k0s()),2&o){let n;const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",null!==(n=null==t.instanceSectionComponents||null==t.instanceSectionComponents[0]||null==t.instanceSectionComponents[0].component||null==t.instanceSectionComponents[0].component.templateField?null:t.instanceSectionComponents[0].component.templateField.label)&&void 0!==n?n:null==t.instanceSectionComponents||null==t.instanceSectionComponents[0]||null==t.instanceSectionComponents[0].component||null==t.instanceSectionComponents[0].component.componentType?null:t.instanceSectionComponents[0].component.componentType.name," ")}}function Ee(o,r){if(1&o&&(e.j41(0,"ion-row"),e.nrm(1,"app-heading-value",10),e.k0s()),2&o){let n;const t=e.XpG(2).$implicit;e.R7$(),e.Y8G("inheritedPropertyValue",null!=t&&t.title||null!=t.section&&t.section.title?null!==(n=null==t?null:t.title)&&void 0!==n?n:t.section.title:"Section")("fontSize",22)}}function Ne(o,r){if(1&o&&(e.j41(0,"ion-row"),e.nrm(1,"app-text-value",11),e.k0s()),2&o){const n=e.XpG(2).$implicit;e.R7$(),e.Y8G("defaultValue",null!=n.section&&n.section.description?n.section.description:"Description")}}function Ae(o,r){if(1&o&&e.nrm(0,"div",14),2&o){const n=e.XpG(5);e.Aen("--background-image:url("+n.assetUrl+");")}}function ze(o,r){if(1&o&&(e.j41(0,"ion-label",18),e.EFF(1),e.k0s()),2&o){let n;const t=e.XpG(3).$implicit;e.R7$(),e.SpI(" ",null!==(n=null==t||null==t.component||null==t.component.templateField?null:t.component.templateField.label)&&void 0!==n?n:null==t||null==t.component||null==t.component.componentType?null:t.component.componentType.name," ")}}function Le(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",17),e.bIt("click",function(i){e.eBV(n);const a=e.XpG(2).$implicit,s=e.XpG(4);return e.Njj(s.editComponent(a.component,i))}),e.DNE(1,ze,2,1,"ion-label",18),e.k0s()}if(2&o){const n=e.XpG(2).$implicit,t=e.XpG(4);e.Y8G("ngClass",(null==t.editPanelBuilderIn?null:t.editPanelBuilderIn.id)!==n.component.id||t.disabled?"component-overlay":"component-overlay selected"),e.R7$(),e.vxM((null==t.editPanelBuilderIn?null:t.editPanelBuilderIn.id)!==n.component.id||t.disabled?-1:1)}}function Ye(o,r){if(1&o&&e.nrm(0,"app-component-row-selector",16),2&o){let n;const t=e.XpG(2).$implicit,i=e.XpG(3).$implicit,a=e.XpG();e.Y8G("instance",a.instance)("instanceSectionComponent",t)("instanceSection",i)("routeParams",a.routeParams)("sidePanelPadding",!0)("searchFilter",a.searchFilter)("continuousFeedback",null!==(n=null==i||null==i.section?null:i.section.isContinuousFeedback)&&void 0!==n&&n)}}function Qe(o,r){if(1&o&&(e.j41(0,"ion-col",13),e.DNE(1,Le,2,2,"div",15)(2,Ye,1,7,"app-component-row-selector",16),e.k0s()),2&o){const n=e.XpG().$implicit;e.Y8G("size",0!==(null==n.component||null==n.component.templateField?null:n.component.templateField.colspan)?null==n.component||null==n.component.templateField?null:n.component.templateField.colspan:null)("id",n.component.id),e.R7$(),e.vxM("Text"===(null==n||null==n.component||null==n.component.componentType?null:n.component.componentType.name)&&"Campaign.CampaignCode"===(null==n||null==n.component||null==n.component.templateField||null==n.component.templateField.systemProperty?null:n.component.templateField.systemProperty.property)||"User RIASEC Score Chart"===(null==n||null==n.component||null==n.component.componentType?null:n.component.componentType.name)?-1:1),e.R7$(),e.vxM(null!=n.component&&null!=n.component.templateField&&n.component.templateField.isFilter||null==n.component||null==n.component.templateField||!n.component.templateField.isPreviewField?-1:2)}}function He(o,r){if(1&o&&e.DNE(0,Ae,1,2,"div",12)(1,Qe,3,4,"ion-col",13),2&o){const n=r.$implicit,t=e.XpG().$index,i=e.XpG(2).$index;e.vxM("Listing Details"===n.component.componentType.name&&0===i&&0===t&&!0!==(null==n.component||null==n.component.templateField?null:n.component.templateField.moveToBack)?0:-1),e.R7$(),e.vxM("banner"===n.component.componentType.name||null!=n.component&&null!=n.component.templateField&&n.component.templateField.isFilter||null==n.component||null==n.component.templateField||!n.component.templateField.isPreviewField?-1:1)}}function We(o,r){if(1&o&&(e.j41(0,"ion-row"),e.Z7z(1,He,2,2,null,null,e.BUC().trackByJsonObject,!0),e.nI1(3,"orderBy"),e.k0s()),2&o){const n=r.$implicit;e.R7$(),e.Dyx(e.i5U(3,0,n,"component.templateField.colNumber"))}}function Je(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",9)(1,"div",19)(2,"div",20)(3,"div",21)(4,"ion-icon",22),e.bIt("click",function(i){e.eBV(n);const a=e.XpG(2).$implicit,s=e.XpG();return e.Njj(s.addComponent(a,i))}),e.k0s()(),e.j41(5,"div",23),e.EFF(6,"Add a component"),e.k0s()()()()}}function Ze(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-card",8),e.bIt("click",function(i){e.eBV(n);const a=e.XpG().$implicit,s=e.XpG();return e.Njj(s.editSection(a.section,i))}),e.j41(1,"ion-grid"),e.DNE(2,Ee,2,2,"ion-row")(3,Ne,2,1,"ion-row"),e.Z7z(4,We,4,3,"ion-row",null,e.BUC().trackByJsonObject,!0),e.nI1(6,"groupBy"),e.nI1(7,"values"),e.k0s(),e.DNE(8,Je,7,0,"div",9),e.k0s()}if(2&o){const n=e.XpG().$implicit,t=e.XpG();e.Aen(!0!==(null==n||null==n.section?null:n.section.hideBackground)&&(null!=n&&null!=n.section&&n.section.backgroundColor||n.backgroundColor)?"background:"+((null==n||null==n.backgroundColor?null:n.backgroundColor.length)>0?null==n?null:n.backgroundColor:null==n||null==n.section?null:n.section.backgroundColor):"background-color:none"),e.Y8G("ngClass",(null==t.editPanelBuilderIn?null:t.editPanelBuilderIn.id)!==n.section.id&&(null==t.editPanelBuilderIn?null:t.editPanelBuilderIn.id)!==n.id||t.disabled?"preview-parent-container":"preview-parent-container selected")("id",n.section.id),e.R7$(2),e.vxM(!0===n.section.showTitleOnPlayer?2:-1),e.R7$(),e.vxM(!0===n.section.showDescOnPlayer?3:-1),e.R7$(),e.Dyx(e.bMT(7,10,e.i5U(6,7,n.instanceSectionComponents,"component.builderRowNumber"))),e.R7$(4),e.vxM((null==n||null==n.section?null:n.section.typeBw)!==t.sectionTypes.Dynamic||t.disabled?-1:8)}}function Ke(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",7)(1,"div",21),e.nrm(2,"hr",24),e.j41(3,"ion-icon",22),e.bIt("click",function(){e.eBV(n);const i=e.XpG().$index,a=e.XpG();return e.Njj(a.addSection(i))}),e.k0s(),e.nrm(4,"hr",25),e.k0s()()}}function qe(o,r){if(1&o&&(e.j41(0,"div",2),e.DNE(1,Ue,2,1,"ion-label",5)(2,De,2,1,"ion-label",5)(3,Ze,9,12,"ion-card",6)(4,Ke,5,0,"div",7),e.k0s()),2&o){const n=r.$implicit,t=r.$index,i=e.XpG();e.R7$(),e.vxM((null==i.editPanelBuilderIn?null:i.editPanelBuilderIn.id)!==n.section.id&&(null==i.editPanelBuilderIn?null:i.editPanelBuilderIn.id)!==n.id||i.disabled||null==n.section||!n.section.title||n.section.typeBw===i.sectionTypes.Dynamic&&null!=n.section&&n.section.templateId?-1:1),e.R7$(),e.vxM((null==i.editPanelBuilderIn?null:i.editPanelBuilderIn.id)!==(null==n.instanceSectionComponents||null==n.instanceSectionComponents[0]||null==n.instanceSectionComponents[0].component?null:n.instanceSectionComponents[0].component.id)||i.disabled?-1:2),e.R7$(),e.vxM(n.section.typeBw===i.sectionTypes.Dynamic&&null!=n.section&&n.section.templateId?-1:3),e.R7$(),e.vxM((null==n||null==n.section?null:n.section.typeBw)!==i.sectionTypes.Dynamic||i.disabled||(null==i.template.instanceSections[t+1]||null==i.template.instanceSections[t+1].section?null:i.template.instanceSections[t+1].section.typeBw)===i.sectionTypes.Dynamic&&null!=i.template&&null!=i.template.instanceSections[t+1]&&null!=i.template.instanceSections[t+1].section&&i.template.instanceSections[t+1].section.templateId?-1:4)}}let en=(()=>{class o{constructor(n,t){this.alertService=n,this.instanceService=t,this.editInstanceSections=[],this.editAddClicked=new e.bkB,this.emitEditInstanceSections=new e.bkB,this.setupStepsOnEditClick=new e.bkB,this.componentDestroyed$=new y.B,this.builderType=N.V,this.instanceSectionsIn=[],this.sectionTypes=O.F}ngOnInit(){this.setGradientImageOverlay()}ngOnChanges(n){n.editPanelBuilderIn&&this.scrollToElement()}scrollToElement(){const n=document.getElementById(this.editPanelBuilderIn.id);n&&(n.style.scrollMarginTop="50px",n.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"}))}setGradientImageOverlay(){this.assetUrl=this.instance?.coverMediaAssetId?`${B.c.contentUrl}asset/${this.instance.coverMediaAssetId}/content`:"assets/images/defaultbackgroundgradient.png"}trackByJsonObject(n,t){return JSON.stringify(t)}editComponent(n,t){t?.stopPropagation(),!0!==this.disabled?(this.editPanelBuilderIn.id=n.id,this.editPanelBuilderIn.builderType=this.builderType.EditComponent,this.editPanelBuilderIn.heading="Edit Component",this.editAddClicked.next(null)):this.alertService.presentAlert("PUBLISHED INSTANCE","Instance must be unpublished before edit!")}editSection(n,t){t?.stopPropagation(),!n.templateId&&(this.editPanelBuilderIn.id=n.id,this.editPanelBuilderIn.builderType=this.builderType.EditSection,this.editPanelBuilderIn.heading="Edit Section",this.editAddClicked.next(null))}addSection(n){this.editPanelBuilderIn.id="",this.editPanelBuilderIn.previousIndex=n,this.editPanelBuilderIn.builderType=this.builderType.AddSection,this.editPanelBuilderIn.heading="Add Section",this.editAddClicked.emit(null)}addComponent(n,t){t?.stopPropagation(),this.editPanelBuilderIn.id=n.id,this.editPanelBuilderIn.builderType=this.builderType.AddComponent,this.editPanelBuilderIn.heading="Add Component",this.editAddClicked.emit(null)}hasEditAccess(){return this.instance?.feature?.featureTabs?.flatMap(t=>t.featureTabActions)?.some(t=>t.actionBw===S.Q.Manage||t.actionBw===S.Q.Publish)}setupStepsOnEdit(){!0!==this.disabled?this.setupStepsOnEditClick.emit(!0):this.alertService.presentAlert("PUBLISHED INSTANCE","Instance must be unpublished before edit!")}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(j.u),e.rXU(M.b))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-builder-instance-preview"]],inputs:{template:"template",instance:"instance",routeParams:"routeParams",editPanelBuilderIn:"editPanelBuilderIn",editInstanceSections:"editInstanceSections",disabled:"disabled",id:"id",panelState:"panelState"},outputs:{editAddClicked:"editAddClicked",emitEditInstanceSections:"emitEditInstanceSections",setupStepsOnEditClick:"setupStepsOnEditClick"},features:[e.OA$],decls:4,vars:2,consts:[[1,"preview-parent-content-container",3,"scrollEvents"],[1,"edit-button"],[1,"section-container"],["fill","clear",1,"inner-container",3,"click"],["name","pencil"],[1,"header","section-title"],[3,"ngClass","style","id"],[1,"section-add-line"],[3,"click","ngClass","id"],[1,"add-component-container"],[3,"inheritedPropertyValue","fontSize"],[3,"defaultValue"],[1,"image-header-gradient",3,"style"],["col-12","","col-md-6","","col-lg-4","","col-xl-3","",3,"size","id"],[1,"image-header-gradient"],[3,"ngClass"],[3,"instance","instanceSectionComponent","instanceSection","routeParams","sidePanelPadding","searchFilter","continuousFeedback"],[3,"click","ngClass"],[1,"header","title"],[1,"inner-content"],[1,"add-button-container"],[1,"icon-container"],["name","add-circle-outline",3,"click"],[1,"button-heading"],[1,"start"],[1,"end"]],template:function(t,i){1&t&&(e.j41(0,"ion-content",0),e.DNE(1,Ge,4,0,"div",1),e.Z7z(2,qe,5,4,"div",2,i.trackByJsonObject,!0),e.k0s()),2&t&&(e.Y8G("scrollEvents",!0),e.R7$(),e.vxM(i.hasEditAccess()&&!i.panelState?1:-1),e.R7$(),e.Dyx(i.template.instanceSections))},dependencies:[Xe.HeadingValueComponent,$e.O,C.YU,d.Jm,d.b_,d.hU,d.W9,d.lO,d.iq,d.he,d.ln,W.A,T.lU,T.Qe,T.Op],styles:["[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]{height:100%;border-radius:11px;background-color:#232323;border:3px solid rgb(51,51,51)}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]{position:absolute;right:25px;top:20px;z-index:9999}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{margin:0;color:#000;text-transform:none;border-radius:3px;font-weight:500;font-size:18px;background-color:#f99e00}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:18px;height:18px;padding-left:10px;margin-right:10px;color:#000}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   ion-button.inner-container[_ngcontent-%COMP%]::part(native){line-height:normal;--padding-start: 0 !important;--padding-end: 10px !important}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]{width:100%;background-image:linear-gradient(to bottom,#1e1e1e59 25%,#232323 75%),var(--background-image);background-size:cover;border-radius:11px;height:180px;top:0;left:0;position:absolute}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{position:absolute;width:fit-content;background:#f99e00;color:#000;border-color:#f99e00;border-width:1px;border-style:solid;border-radius:3px 3px 0 0;font-family:Roboto;font-weight:700;line-height:1.6;letter-spacing:.3px;text-align:left;margin-top:-24px;height:24px;display:block;z-index:1000}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{left:0;margin-bottom:-1px;padding:2px 11px 16px;font-size:11px;z-index:1000}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]{box-shadow:none;--background: none;margin:0}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-headings-col[_ngcontent-%COMP%]{margin-bottom:10px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-headings-col[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-headings-col[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%]{font-family:Roboto;color:#9f9f9f;font-size:18px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-headings-col[_ngcontent-%COMP%]   .section-index[_ngcontent-%COMP%]{width:38px;height:36px;background:#292929;color:#aaa;border-color:#393939;border-width:1px;border-style:solid;border-radius:26px;font-family:Roboto;font-weight:700;font-size:16px;line-height:1.3;text-align:center;z-index:999;display:flex;flex-direction:row;align-items:center;justify-content:center;align-content:center}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-headings-col[_ngcontent-%COMP%]   .section-title-text[_ngcontent-%COMP%]{min-width:128px;width:fit-content;height:24px;padding:7px 12px 4px 43px;background:#333;color:#aaa;border-color:#454545;border-width:1px;border-style:solid;border-radius:11px;font-family:Roboto;font-weight:400;font-size:12px;line-height:1.1;letter-spacing:.2px;text-align:left;z-index:998;margin-left:-20px;text-overflow:ellipsis}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-manage-options-col[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;padding-top:7px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-manage-options-col[_ngcontent-%COMP%]   .section-settings[_ngcontent-%COMP%]{min-width:128px;width:fit-content;height:24px;background:none;color:#aaa;font-family:Roboto;font-weight:400;font-size:12px;line-height:1.1;letter-spacing:.2px;text-align:left;z-index:998;margin-left:-20px;text-overflow:ellipsis;display:flex;flex-direction:row;justify-content:flex-end;align-items:center;align-content:center}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-manage-options-col[_ngcontent-%COMP%]   ion-reorder[_ngcontent-%COMP%]{z-index:900}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-manage-options-col[_ngcontent-%COMP%]   ion-reorder[_ngcontent-%COMP%]   .move-icon[_ngcontent-%COMP%]{cursor:pointer;height:16px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .section-manage-options-col[_ngcontent-%COMP%]   ion-reorder[_ngcontent-%COMP%]   .move-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;height:16px;width:16px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .add-component-container[_ngcontent-%COMP%]{margin:10px;border-radius:10px;border:2px solid rgb(130,130,130);background-color:#222;border-style:dashed}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .add-component-container[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:10px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .add-component-container[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]   .add-button-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-bottom:5px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .add-component-container[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]   .add-button-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ba7a13!important;font-size:35px;cursor:pointer}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   ion-card[_ngcontent-%COMP%]   .add-component-container[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]   .add-button-container[_ngcontent-%COMP%]   .button-heading[_ngcontent-%COMP%]{color:#fff}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .section-add-line[_ngcontent-%COMP%]{margin-bottom:5px}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .section-add-line[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .section-add-line[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   .start[_ngcontent-%COMP%]{border-top:3px solid rgb(50,50,50);border-style:dashed;width:50%}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .section-add-line[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:35px;color:#ba7a13!important;cursor:pointer}[_nghost-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .section-add-line[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   .end[_ngcontent-%COMP%]{border-top:3px solid rgb(50,50,50);border-style:dashed;width:50%}[_nghost-%COMP%]   .component-overlay[_ngcontent-%COMP%]{z-index:1001;height:100%;width:100%;border-radius:5px;position:absolute;cursor:pointer}[_nghost-%COMP%]   .selected[_ngcontent-%COMP%]{border-color:#f99e00;border-width:1px;border-style:solid;border-radius:3px 3px 0 0}[_nghost-%COMP%]   .selected[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{position:absolute;width:fit-content;background:#f99e00;color:#000;border-color:#f99e00;border-width:1px;border-style:solid;border-radius:3px 3px 0 0;font-family:Roboto;font-weight:700;line-height:1.6;letter-spacing:.3px;text-align:left;margin-top:-24px;height:24px;display:block;z-index:1000}[_nghost-%COMP%]   .selected[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{left:0;margin-left:-1px;padding:2px 11px 16px;font-size:11px;z-index:1000}[_nghost-%COMP%]   .preview-parent-container[_ngcontent-%COMP%]{position:unset}"]})}}return o})();var nn=l(97768),_=l(89417),k=function(o){return o[o.AuthToolSection=1]="AuthToolSection",o[o.TemplateSection=2]="TemplateSection",o[o.Component=3]="Component",o}(k||{}),P=l(93438),J=l(70152),le=l(6165),G=l(14310),X=l(31549),x=l(34833),de=l(25164),tn=l(24595),on=l(99551),pe=l(52497),an=l(50453),U=l(36013),ue=l(99213),sn=l(98823),rn=l(60618);const cn=o=>({"background-color":o});function ln(o,r){if(1&o&&e.nrm(0,"div",18),2&o){const n=e.XpG().$implicit,t=e.XpG(2);e.Aen("background-image:url("+t.setImage(n.componentType.assetId)+")")}}function dn(o,r){if(1&o&&(e.j41(0,"div",17)(1,"div",19)(2,"div",20),e.EFF(3),e.k0s()()()),2&o){const n=e.XpG().$implicit;e.R7$(3),e.JRh(null==n.componentType?null:n.componentType.name)}}function pn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div")(1,"div",12)(2,"div",13)(3,"div",14),e.EFF(4),e.k0s()(),e.j41(5,"div",15),e.bIt("click",function(){const i=e.eBV(n).$implicit,a=e.XpG(2);return e.Njj(a.checkboxChanged(i.id))}),e.DNE(6,ln,1,2,"div",16)(7,dn,4,1,"div",17),e.k0s()()()}if(2&o){const n=r.$implicit,t=e.XpG(2);e.R7$(4),e.JRh(null==n.componentType?null:n.componentType.name),e.R7$(),e.Y8G("ngClass",t.isSelected(n.id)?"component selected":"component")("ngStyle",e.eq3(5,cn,n.componentType.assetId?null:"#111111")),e.R7$(),e.vxM(n.componentType.assetId?6:-1),e.R7$(),e.vxM(n.componentType.assetId?-1:7)}}function un(o,r){if(1&o&&(e.j41(0,"div",11),e.Z7z(1,pn,8,7,"div",null,e.fX1),e.k0s()),2&o){const n=e.XpG();e.R7$(),e.Dyx(n.getDynamicComponentListFiltered())}}let _n=(()=>{class o{constructor(n,t){this.dataService=n,this.builderService=t,this.sectionComponentsChanged=new e.bkB,this.sectionTypes=O.F,this.selectedComponentIds=[],this.componentDestroyed$=new y.B}ngOnInit(){this.getDynamicComponentList(this.section.id)}ngOnChanges(n){n.section&&this.getDynamicComponentList(this.section.id)}getDynamicComponentList(n){this.dataService.getDynamicSectionComponentTypes(n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{t&&(this.dynamicComponentList=t)})}setImage(n){return`${B.c.contentUrl}asset/${n}/content`}isSelected(n){return!!this.selectedComponentIds&&-1!==this.selectedComponentIds.findIndex(t=>t===n)}checkboxChanged(n){const t=this.selectedComponentIds.findIndex(i=>i===n);-1===t?this.selectedComponentIds.push(n):this.selectedComponentIds.splice(t,1)}back(){this.builderService.selectedSection$.next(null)}getDynamicComponentListFiltered(){return this.query&&this.query.length>0?this.dynamicComponentList.filter(n=>n.componentType.name.toLowerCase().includes(this.query.toLowerCase())):this.dynamicComponentList}save(){this.dataService.addInstanceSectionComponents(this.panelBuilder.id,this.selectedComponentIds).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{n&&this.sectionComponentsChanged.emit(n)})}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(G.V))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-dynamic-section-components"]],inputs:{section:"section",panelBuilder:"panelBuilder"},outputs:{sectionComponentsChanged:"sectionComponentsChanged"},features:[e.OA$],decls:19,vars:2,consts:[[1,"parent-container"],["size","12",1,"back-col"],["fill","clear","size","small","color","warning",3,"click"],["slot","start","name","chevron-back-outline"],["fill","solid","size","small","color","warning",3,"click"],[1,"inner-search-container"],[1,"top-heading-container"],["position","stacked",1,"heading"],[1,"grey-text"],["placeholder","Find a Component",3,"ngModelChange","ngModel"],["name","search-outline",1,"search-icon"],[1,"dynamic-list-container"],[1,"component-container"],[1,"image-heading-container"],[1,"image-heading"],[3,"click","ngClass","ngStyle"],[1,"image-container",3,"style"],[1,"component-left"],[1,"image-container"],[1,"inner-container"],[1,"heading"]],template:function(t,i){1&t&&(e.j41(0,"div",0)(1,"ion-row")(2,"ion-col",1)(3,"ion-button",2),e.bIt("click",function(){return i.back()}),e.nrm(4,"ion-icon",3),e.EFF(5,"Back"),e.k0s(),e.j41(6,"ion-button",4),e.bIt("click",function(){return i.save()}),e.EFF(7,"Add"),e.k0s()()(),e.qex(8),e.j41(9,"div",5)(10,"div",6)(11,"ion-label",7),e.EFF(12," Components "),e.k0s(),e.nrm(13,"br"),e.j41(14,"ion-label",8),e.EFF(15,"Select components to add"),e.k0s(),e.j41(16,"ion-input",9),e.mxI("ngModelChange",function(s){return e.DH7(i.query,s)||(i.query=s),s}),e.nrm(17,"ion-icon",10),e.k0s()(),e.DNE(18,un,3,0,"div",11),e.k0s(),e.bVm(),e.k0s()),2&t&&(e.R7$(16),e.R50("ngModel",i.query),e.R7$(2),e.vxM(i.dynamicComponentList&&i.section.typeBw===i.sectionTypes.Dynamic?18:-1))},dependencies:[C.YU,C.B3,d.Jm,d.hU,d.iq,d.$w,d.he,d.ln,d.Gw,_.BC,_.vS],styles:["[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .back-col[_ngcontent-%COMP%]{display:flex;justify-content:space-between}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .back-col[_ngcontent-%COMP%]   ion-button.button[_ngcontent-%COMP%]::part(native){line-height:0px}@media (min-width: 1500px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:11vw}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:15vw;height:11.3vw}}@media (min-width: 992px) and (max-width: 1500px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:15vw}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:20vw;height:15.3vw}}@media (min-width: 769px) and (max-width: 992px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:180px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:250px;height:181px}}@media (min-width: 480px) and (max-width: 768px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:180px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:250px;height:183px}}@media (max-width: 479px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:180px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:252px;height:181px}}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{background-size:contain;width:100%;object-fit:contain;background-position:left;background-repeat:no-repeat;border-radius:5px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]{color:#fff;position:relative;height:100%}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-size:16px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   .grey-text[_ngcontent-%COMP%]{color:gray;font-size:15px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{margin-bottom:5px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]{padding-left:10px;cursor:pointer}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{background-color:#292929;margin-top:5px;border-radius:4px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]{background-color:#292929;padding:10px;border-radius:5px;width:100%;height:calc(100% - 135px);overflow-y:auto}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .image-heading-container[_ngcontent-%COMP%]{margin-bottom:5px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .image-heading-container[_ngcontent-%COMP%]   .image-heading[_ngcontent-%COMP%]{font-weight:700;font-size:14px;color:#aaa;white-space:nowrap;overflow:hidden;text-overflow:ellipsis!important;padding:0 10px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]{margin-bottom:20px;width:100%;display:flex;flex-direction:column}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]:hover{border:1px solid #f99e00}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{border-radius:5px;display:flex;flex-direction:row;align-items:center;align-content:center;cursor:pointer}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]   .component-left[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;align-content:center}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]   .component-left[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]   .component-left[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-weight:700;font-size:14px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis!important;padding:0 10px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]{border:1px solid #f99e00}"]})}}return o})();var _e=l(27233);const mn=o=>({"no-margin":o});function gn(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-col",1)(1,"ion-button",3),e.bIt("click",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.back())}),e.nrm(2,"ion-icon",4),e.EFF(3,"Back"),e.k0s()()}}function hn(o,r){if(1&o&&(e.j41(0,"form",0)(1,"ion-grid")(2,"ion-row"),e.DNE(3,gn,4,0,"ion-col",1),e.k0s(),e.j41(4,"ion-row")(5,"ion-col"),e.nrm(6,"app-text-input-control",2),e.k0s()()()()),2&o){const n=e.XpG();e.Y8G("formGroup",n.sectionForm)("ngClass",e.eq3(6,mn,n.panelBuilder)),e.R7$(3),e.vxM(n.panelBuilder?-1:3),e.R7$(3),e.Y8G("noPadding",!0)("backgroundColor","#292929")("label","Section title")}}let Cn=(()=>{class o{constructor(n,t){this.dataService=n,this.builderService=t,this.instanceSectionUpdated=new e.bkB,this.builderType=N.V,this.componentDestroyed$=new y.B}ngOnInit(){this.createSectionForm()}ngOnChanges(n){n.section&&(this.formValueChanges$&&this.formValueChanges$.unsubscribe(),this.setFormValues())}createSectionForm(){this.sectionForm=new _.J3({title:new _.hs(this.instanceSection?.title,[_.k0.required])}),this.subscribeToFormChanges()}setFormValues(){this.sectionForm&&(this.sectionForm.controls.title.setValue(this.instanceSection?.title),this.subscribeToFormChanges())}subscribeToFormChanges(){this.formValueChanges$=this.sectionForm.valueChanges.pipe((0,J.B)(1e3),(0,u.Q)(this.componentDestroyed$)).subscribe(()=>{const n={...this.instanceSection,title:this.sectionForm.controls.title.value,instanceId:this.instanceId,templateId:this.templateId,masterSectionId:null};this.sectionForm.valid&&this.updateInstanceSection(n)})}updateInstanceSection(n){this.dataService.updateInstanceSections([n]).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.instanceSectionUpdated.next(n.title)})}back(){this.builderService.selectedSection$.next(null)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(G.V))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-instance-section-edit"]],inputs:{panelBuilder:"panelBuilder",instanceId:"instanceId",templateId:"templateId",instanceSection:"instanceSection"},outputs:{instanceSectionUpdated:"instanceSectionUpdated"},features:[e.OA$],decls:1,vars:1,consts:[[1,"parent-form-container",3,"formGroup","ngClass"],[1,"back-col"],["formControlName","title",3,"noPadding","backgroundColor","label"],["fill","clear","size","small","color","warning",3,"click"],["slot","start","name","chevron-back-outline"]],template:function(t,i){1&t&&e.DNE(0,hn,7,8,"form",0),2&t&&e.vxM(i.sectionForm?0:-1)},dependencies:[_e.U,C.YU,d.Jm,d.hU,d.lO,d.iq,d.ln,_.qT,_.BC,_.cb,_.j4,_.JD],styles:[".parent-form-container[_ngcontent-%COMP%]{margin:10px 16px}.parent-form-container[_ngcontent-%COMP%]   .back-col[_ngcontent-%COMP%]{display:flex;justify-content:flex-start}.parent-form-container[_ngcontent-%COMP%]   .back-col[_ngcontent-%COMP%]   ion-button.button[_ngcontent-%COMP%]::part(native){line-height:0px}.parent-form-container[_ngcontent-%COMP%]   .top-header-col[_ngcontent-%COMP%]{margin-top:10px}.parent-form-container[_ngcontent-%COMP%]   .top-header-col[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#fff;font-family:Roboto;font-weight:700;font-size:25px}.parent-form-container[_ngcontent-%COMP%]   .middle-line[_ngcontent-%COMP%]{margin-top:20px}.parent-form-container[_ngcontent-%COMP%]   .middle-line[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{border-top:3px solid rgb(40,40,40)}.parent-form-container[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]{padding-top:20px}.parent-form-container[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]   .toggle-col[_ngcontent-%COMP%]{display:flex;align-items:center}.parent-form-container[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]   .toggle-col[_ngcontent-%COMP%]   .mat-mdc-slide-toggle[_ngcontent-%COMP%]{vertical-align:middle;margin-right:15px}.parent-form-container[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]   .toggle-col[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:18px;color:#fff;font-family:Roboto;font-weight:700}.no-margin[_ngcontent-%COMP%]{margin:0!important}"]})}}return o})();const fn=()=>({"background-color":"#111111"});function Pn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div")(1,"div",10)(2,"div",11),e.bIt("click",function(){const i=e.eBV(n).$implicit,a=e.XpG(2);return e.Njj(a.checkboxChanged(i))}),e.j41(3,"div",12)(4,"div",13)(5,"div",14),e.EFF(6),e.k0s()()()()()()}if(2&o){const n=r.$implicit,t=e.XpG(2);e.R7$(2),e.Y8G("ngClass",t.isSelected(n.id)?"component selected":"component")("ngStyle",e.lJ4(3,fn)),e.R7$(4),e.JRh(n.title)}}function bn(o,r){if(1&o&&(e.j41(0,"div",9),e.Z7z(1,Pn,7,4,"div",null,e.fX1),e.k0s()),2&o){const n=e.XpG();e.R7$(),e.Dyx(n.sections)}}let Mn=(()=>{class o{constructor(n){this.builderService=n,this.sectionSelected=new e.bkB,this.sectionTypes=O.F}ngOnInit(){this.sections=this.template.instanceSections.filter(n=>n.section.typeBw===this.sectionTypes.Dynamic&&n.section.templateId).map(n=>n.section)}isSelected(n){return this.selectedSection?.id===n}checkboxChanged(n){this.selectedSection=n}back(){this.builderService.selectedSection$.next(null)}save(){this.sectionSelected.emit(this.selectedSection)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(G.V))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-dynamic-section-selector"]],inputs:{panelBuilder:"panelBuilder",template:"template"},outputs:{sectionSelected:"sectionSelected"},decls:17,vars:1,consts:[[1,"parent-container"],["size","12",1,"back-col"],["fill","clear","size","small","color","warning",3,"click"],["slot","start","name","chevron-back-outline"],["fill","solid","size","small","color","warning",3,"click"],[1,"inner-search-container"],[1,"top-heading-container"],["position","stacked",1,"heading"],[1,"grey-text"],[1,"dynamic-list-container"],[1,"component-container"],[3,"click","ngClass","ngStyle"],[1,"component-left"],[1,"inner-container"],[1,"heading"]],template:function(t,i){1&t&&(e.j41(0,"div",0)(1,"ion-row")(2,"ion-col",1)(3,"ion-button",2),e.bIt("click",function(){return i.back()}),e.nrm(4,"ion-icon",3),e.EFF(5,"Back"),e.k0s(),e.j41(6,"ion-button",4),e.bIt("click",function(){return i.save()}),e.EFF(7,"Add"),e.k0s()()(),e.qex(8),e.j41(9,"div",5)(10,"div",6)(11,"ion-label",7),e.EFF(12," Sections "),e.k0s(),e.nrm(13,"br"),e.j41(14,"ion-label",8),e.EFF(15,"Select sections to add"),e.k0s()(),e.DNE(16,bn,3,0,"div",9),e.k0s(),e.bVm(),e.k0s()),2&t&&(e.R7$(16),e.vxM(i.sections?16:-1))},dependencies:[C.YU,C.B3,d.Jm,d.hU,d.iq,d.he,d.ln],styles:["[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .back-col[_ngcontent-%COMP%]{display:flex;justify-content:space-between}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .back-col[_ngcontent-%COMP%]   ion-button.button[_ngcontent-%COMP%]::part(native){line-height:0px}@media (min-width: 1500px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:11vw}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:15vw;height:11.3vw}}@media (min-width: 992px) and (max-width: 1500px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:15vw}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:20vw;height:15.3vw}}@media (min-width: 769px) and (max-width: 992px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:180px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:250px;height:181px}}@media (min-width: 480px) and (max-width: 768px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:180px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:250px;height:183px}}@media (max-width: 479px){[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{height:180px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{width:252px;height:181px}}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{background-size:contain;width:100%;object-fit:contain;background-position:left;background-repeat:no-repeat;border-radius:5px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]{color:#fff;position:relative;height:100%}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-size:16px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   .grey-text[_ngcontent-%COMP%]{color:gray;font-size:15px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{margin-bottom:5px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .top-heading-container[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]{padding-left:10px;cursor:pointer}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{background-color:#292929;margin-top:5px;border-radius:4px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]{background-color:#292929;padding:10px;border-radius:5px;width:100%;height:calc(100% - 135px);overflow-y:auto}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .image-heading-container[_ngcontent-%COMP%]{margin-bottom:5px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .image-heading-container[_ngcontent-%COMP%]   .image-heading[_ngcontent-%COMP%]{font-weight:700;font-size:14px;color:#aaa;white-space:nowrap;overflow:hidden;text-overflow:ellipsis!important;padding:0 10px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]{margin-bottom:20px;width:100%;display:flex;flex-direction:column}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]:hover{border:1px solid #f99e00}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]{border-radius:5px;display:flex;flex-direction:row;align-items:center;align-content:center;cursor:pointer}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]   .component-left[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;align-content:center}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]   .component-left[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .component[_ngcontent-%COMP%]   .component-left[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-weight:700;font-size:14px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis!important;padding:0 10px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .inner-search-container[_ngcontent-%COMP%]   .dynamic-list-container[_ngcontent-%COMP%]   .component-container[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]{border:1px solid #f99e00}"]})}}return o})();const On=["stepper"],yn=["content"],wn=o=>({section:o});function vn(o,r){1&o&&(e.j41(0,"mat-icon"),e.EFF(1,"create"),e.k0s())}function xn(o,r){1&o&&e.DNE(0,vn,2,0,"ng-template",13)}function Sn(o,r){if(1&o&&(e.j41(0,"div",28),e.EFF(1),e.k0s()),2&o){const n=e.XpG(2).$implicit;e.R7$(),e.SpI("STEP ",n.componentIndex,"")}}function In(o,r){if(1&o&&(e.j41(0,"div",28),e.EFF(1),e.k0s()),2&o){const n=e.XpG(2).$implicit;e.R7$(),e.JRh(null==n.section?null:n.section.title)}}function Tn(o,r){if(1&o&&e.nrm(0,"app-builder-side-panel-heading",30),2&o){let n;const t=e.XpG(2).$implicit;e.Y8G("text",null!==(n=null==t||null==t.component||null==t.component.templateField?null:t.component.templateField.helpTitle)&&void 0!==n?n:"")}}function Fn(o,r){if(1&o&&e.nrm(0,"app-builder-side-panel-description",33),2&o){const n=e.XpG(7);e.Y8G("text",null==n.section?null:n.section.description)("noPadding",!0)}}function Bn(o,r){if(1&o&&e.DNE(0,Fn,1,2,"app-builder-side-panel-description",33),2&o){let n;const t=e.XpG(6);e.vxM((null!==(n=null==t.section?null:t.section.description)&&void 0!==n?n:(null==t.section||null==t.section.description?null:t.section.description.length)>0)?0:-1)}}function kn(o,r){if(1&o&&(e.nrm(0,"app-builder-side-panel-heading",32),e.DNE(1,Bn,1,1)),2&o){let n;const t=e.XpG(2).$implicit,i=e.XpG(3);e.Y8G("isSectionHeading",!0)("text",null!==(n=t.section.caption)&&void 0!==n?n:"Section"),e.R7$(),e.vxM(t.typeBw===i.stepTypes.TemplateSection?1:-1)}}function Rn(o,r){1&o&&e.nrm(0,"ion-icon",34)}function Vn(o,r){1&o&&e.nrm(0,"ion-icon",35)}function jn(o,r){if(1&o&&(e.j41(0,"div",31),e.DNE(1,Rn,1,0,"ion-icon",34)(2,Vn,1,0,"ion-icon",35),e.k0s()),2&o){const n=e.XpG(2).$index,t=e.XpG(3);e.R7$(),e.vxM(n===t.selectedIndex?1:-1),e.R7$(),e.vxM(n!==t.selectedIndex?2:-1)}}function Xn(o,r){if(1&o&&(e.j41(0,"div",27)(1,"div",7),e.DNE(2,Sn,2,1,"div",28)(3,In,2,1,"div",28),e.k0s(),e.j41(4,"div",29),e.DNE(5,Tn,1,1,"app-builder-side-panel-heading",30)(6,kn,2,3)(7,jn,3,2,"div",31),e.k0s()()),2&o){const n=e.XpG().$implicit,t=e.XpG(3);e.Y8G("ngClass",e.eq3(6,wn,n.typeBw!==t.stepTypes.Component)),e.R7$(2),e.vxM(n.typeBw===t.stepTypes.Component?2:-1),e.R7$(),e.vxM(n.typeBw!==t.stepTypes.Component?3:-1),e.R7$(2),e.vxM(n.typeBw===t.stepTypes.Component&&n.component?5:-1),e.R7$(),e.vxM(n.typeBw!==t.stepTypes.Component&&n.section?6:-1),e.R7$(),e.vxM(n.typeBw===t.stepTypes.Component&&n.component?7:-1)}}function $n(o,r){if(1&o&&e.nrm(0,"app-builder-side-panel-description",33),2&o){const n=e.XpG(3).$implicit;e.Y8G("text",n.component.templateField.helpDescription)("noPadding",!0)}}function Gn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(8);return e.Njj(i.editComponentSetup())}),e.nrm(1,"ion-icon",44),e.k0s()}}function Un(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(5).$implicit,a=e.XpG(3);return e.Njj(a.moveSectionComponent(i,!0))}),e.nrm(1,"ion-icon",34),e.k0s()}}function Dn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(5).$implicit,a=e.XpG(3);return e.Njj(a.moveSectionComponent(i,!1))}),e.nrm(1,"ion-icon",35),e.k0s()}}function En(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",41),e.DNE(1,Gn,2,0,"div")(2,Un,2,0,"div")(3,Dn,2,0,"div"),e.j41(4,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(7);return e.Njj(i.deleteSectionComponent())}),e.nrm(5,"ion-icon",43),e.k0s()()}if(2&o){const n=e.XpG(4).$implicit,t=e.XpG(3);e.R7$(),e.vxM(n.section.typeBw===t.sectionTypes.Dynamic&&t.hasAdminAccess()?1:-1),e.R7$(),e.vxM(t.allowSectionComponentMoveUp(n)?2:-1),e.R7$(),e.vxM(t.allowSectionComponentMoveDown(n)?3:-1)}}function Nn(o,r){if(1&o&&(e.j41(0,"div",38)(1,"ion-label",40),e.EFF(2),e.k0s(),e.DNE(3,En,6,3,"div",41),e.k0s()),2&o){let n;const t=e.XpG(3).$implicit,i=e.XpG(3);e.R7$(2),e.JRh(null!==(n=null==t.component||null==t.component.templateField?null:t.component.templateField.label)&&void 0!==n?n:null==t.component||null==t.component.componentType?null:t.component.componentType.name),e.R7$(),e.vxM(t.section.typeBw===i.sectionTypes.Dynamic&&!0!==t.component.isLocked?3:-1)}}function An(o,r){if(1&o){const n=e.RV6();e.DNE(0,$n,1,2,"app-builder-side-panel-description",33)(1,Nn,4,2,"div",38),e.j41(2,"div",37)(3,"app-form-control-selector",39),e.bIt("questionUpdated",function(i){e.eBV(n);const a=e.XpG(2).$implicit,s=e.XpG(3);return e.Njj(s.updateQuestionValue(i,a))}),e.k0s()()}if(2&o){const n=e.XpG(2).$implicit,t=e.XpG(3);e.vxM(null!=n.component&&null!=n.component.templateField&&n.component.templateField.helpDescription?0:-1),e.R7$(),e.vxM((null==n?null:n.typeBw)===t.stepTypes.Component?1:-1),e.R7$(2),e.Y8G("component",n.component)("instance",t.instance)("formGroupName",n.section.id)("formGroup",t.templateForm)("showSelected",!0)("id",t.id)("isSidePanelBuilder",!0)("rowBuilderView",n.section.typeBw===t.sectionTypes.Dynamic)("routeParams",t.routeParams)}}function zn(o,r){if(1&o&&(e.j41(0,"ion-label",40),e.EFF(1),e.k0s()),2&o){let n;const t=e.XpG(3).$implicit;e.R7$(),e.SpI(" ",null!==(n=t.section.title)&&void 0!==n?n:"Section","")}}function Ln(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(5).$implicit,a=e.XpG(3);return e.Njj(a.moveSection(i.section,!0))}),e.nrm(1,"ion-icon",34),e.k0s()}}function Yn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(5).$implicit,a=e.XpG(3);return e.Njj(a.moveSection(i.section,!1))}),e.nrm(1,"ion-icon",35),e.k0s()}}function Qn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(8);return e.Njj(i.deleteSection())}),e.nrm(1,"ion-icon",43),e.k0s()}}function Hn(o,r){if(1&o&&e.DNE(0,Ln,2,0,"div")(1,Yn,2,0,"div")(2,Qn,2,0,"div"),2&o){const n=e.XpG(4).$implicit,t=e.XpG(3);e.vxM(t.allowSectionMoveUp(n)?0:-1),e.R7$(),e.vxM(t.allowSectionMoveDown(n)?1:-1),e.R7$(),e.vxM(n.section.templateId?-1:2)}}function Wn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(5).$implicit,a=e.XpG(3);return e.Njj(a.toggleShowInstanceSection(i.instanceSection))}),e.nrm(1,"ion-icon",46),e.k0s()}}function Jn(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",42),e.bIt("click",function(){e.eBV(n);const i=e.XpG(5).$implicit,a=e.XpG(3);return e.Njj(a.toggleShowInstanceSection(i.instanceSection))}),e.nrm(1,"ion-icon",47),e.k0s()}}function Zn(o,r){if(1&o&&e.DNE(0,Wn,2,0,"div")(1,Jn,2,0,"div"),2&o){const n=e.XpG(4).$implicit,t=e.XpG(3);e.vxM(!0!==n.instanceSection.isHidden&&t.hasAdminAccess()?0:-1),e.R7$(),e.vxM(!0===n.instanceSection.isHidden&&t.hasAdminAccess()?1:-1)}}function Kn(o,r){if(1&o&&(e.j41(0,"div",41),e.DNE(1,Hn,3,3)(2,Zn,2,2),e.k0s()),2&o){const n=e.XpG(3).$implicit,t=e.XpG(3);e.R7$(),e.vxM(n.section.typeBw===t.sectionTypes.Dynamic?1:-1),e.R7$(),e.vxM(!0===(null==n||null==n.section?null:n.section.isEditable)?2:-1)}}function qn(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-instance-section-edit",49),e.bIt("instanceSectionUpdated",function(i){e.eBV(n);const a=e.XpG(4).$implicit,s=e.XpG(3);return e.Njj(s.instanceSectionUpdated(i,a.instanceSection.id))}),e.k0s()}if(2&o){const n=e.XpG(4).$implicit,t=e.XpG(3);e.Y8G("panelBuilder",t.editPanelBuilderIn)("instanceSection",n.instanceSection)("instanceId",t.instance.id)("templateId",t.template.id)}}function et(o,r){if(1&o&&e.DNE(0,qn,1,4,"app-instance-section-edit",48),2&o){const n=e.XpG(3).$implicit;e.vxM(!0===(null==n||null==n.section?null:n.section.isEditable)?0:-1)}}function nt(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-section-edit",50),e.bIt("sectionColorChanged",function(i){e.eBV(n);const a=e.XpG(3).$implicit,s=e.XpG(3);return e.Njj(s.updateInstanceSectionColor(i,a.instanceSection))}),e.k0s()}if(2&o){let n;const t=e.XpG(3).$implicit,i=e.XpG(3);e.Y8G("templateId",i.template.id)("panelBuilder",i.editPanelBuilderIn)("section",t.section)("templateForm",i.templateForm)("backGroundColor",null!==(n=null!==(n=null==t.instanceSection?null:t.instanceSection.backgroundColor)&&void 0!==n?n:null==t.section?null:t.section.backgroundColor)&&void 0!==n?n:null)}}function tt(o,r){if(1&o&&(e.j41(0,"div",38),e.DNE(1,zn,2,1,"ion-label",40)(2,Kn,3,2,"div",41),e.k0s(),e.j41(3,"div",27),e.DNE(4,et,1,1)(5,nt,1,5,"app-section-edit",45),e.k0s()),2&o){const n=e.XpG(2).$implicit,t=e.XpG(3);e.R7$(),e.vxM(n.section.typeBw===t.sectionTypes.Dynamic||!0===(null==n||null==n.section?null:n.section.isEditable)?1:-1),e.R7$(),e.vxM(!0===(null==n||null==n.section?null:n.section.isEditable)||t.allowSectionMoveUp(n)||t.allowSectionMoveDown(n)||!n.section.templateId?2:-1),e.R7$(),e.Y8G("ngClass",n.typeBw===t.stepTypes.AuthToolSection||!0===(null==n||null==n.section?null:n.section.isEditable)?"section-container selected":"section-container"),e.R7$(),e.vxM(n.typeBw===t.stepTypes.TemplateSection?4:-1),e.R7$(),e.vxM(n.typeBw===t.stepTypes.AuthToolSection?5:-1)}}function it(o,r){if(1&o&&(e.j41(0,"div",36),e.DNE(1,An,4,11,"div",37)(2,tt,6,5),e.k0s()),2&o){const n=e.XpG().$implicit,t=e.XpG(3);e.Y8G("id",n.component?n.component.id+"-left":n.section.id+"-left"),e.R7$(),e.vxM(n.typeBw===t.stepTypes.Component&&n.component?1:-1),e.R7$(),e.vxM(n.typeBw!==t.stepTypes.Component&&n.section?2:-1)}}function ot(o,r){if(1&o&&(e.j41(0,"mat-step",15),e.DNE(1,Xn,8,8,"ng-template",25)(2,it,3,3,"ng-template",26),e.k0s()),2&o){const n=r.$implicit,t=e.XpG(3);e.Y8G("stepControl",t.getFormControl(n.section.id,null==n.component?null:n.component.id))("state",t.getState(n))}}function at(o,r){}function st(o,r){if(1&o&&e.EFF(0),2&o){const n=r.index,t=e.XpG(3);e.SpI(" ",t.getStepEditIcon(n)," ")}}function rt(o,r){if(1&o&&e.EFF(0),2&o){const n=r.index,t=e.XpG(3);e.SpI(" ",t.getStepEditIcon(n)," ")}}function ct(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-button",51),e.bIt("click",function(){e.eBV(n);const i=e.XpG(3);return e.Njj(i.goBack())}),e.nrm(1,"mat-icon",52),e.EFF(2," Back "),e.k0s()}}function lt(o,r){1&o&&(e.j41(0,"span",24),e.EFF(1,"Next"),e.k0s())}function dt(o,r){1&o&&(e.j41(0,"span",24),e.EFF(1,"Finish"),e.k0s())}function pt(o,r){if(1&o){const n=e.RV6();e.j41(0,"form",3)(1,"div",5)(2,"div",6),e.nrm(3,"div",7),e.j41(4,"div",8)(5,"div",9)(6,"ion-button",10),e.bIt("click",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.closeMenu())}),e.nrm(7,"ion-icon",11),e.k0s()()()(),e.j41(8,"div",12),e.Z7z(9,xn,1,0,null,13,e.fX1),e.j41(11,"mat-stepper",14,1),e.bIt("click",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.setIndexDirect())}),e.Z7z(13,ot,3,2,"mat-step",15,e.fX1),e.DNE(15,at,0,0,"ng-template",16)(16,st,1,1,"ng-template",17)(17,rt,1,1,"ng-template",18),e.k0s()()(),e.j41(18,"footer",19)(19,"div",20),e.DNE(20,ct,3,0,"ion-button",21),e.k0s(),e.j41(21,"div",22)(22,"ion-button",23),e.bIt("click",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.goForward())}),e.DNE(23,lt,2,0,"span",24)(24,dt,2,0,"span",24),e.k0s()()()()}if(2&o){const n=e.sdS(12),t=e.XpG(2);e.Y8G("formGroup",t.templateForm),e.R7$(),e.Aen("height: Calc(100vh - "+t.contentHeight+"px);"),e.R7$(8),e.Dyx(t.steps),e.R7$(2),e.Y8G("linear",!1)("selectedIndex",t.selectedIndex),e.R7$(2),e.Dyx(t.steps),e.R7$(7),e.vxM(n.selectedIndex>0?20:-1),e.R7$(3),e.vxM(n.steps.length>n.selectedIndex+1?23:-1),e.R7$(),e.vxM(n.steps.length===n.selectedIndex+1?24:-1)}}function ut(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",53)(1,"app-dynamic-section-components",54),e.bIt("sectionComponentsChanged",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.sectionComponentsUpdated(i))}),e.k0s()()}if(2&o){const n=e.XpG(2);e.Aen("height: Calc(100vh - "+n.sectionHeight+"px);"),e.R7$(),e.Y8G("section",n.getMasterSection(n.section.id))("panelBuilder",n.editPanelBuilderIn)}}function _t(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",53)(1,"app-dynamic-section-selector",55),e.bIt("sectionSelected",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.setSectionBuilder(i))}),e.k0s()()}if(2&o){const n=e.XpG(2);e.Aen("height: Calc(100vh - "+n.sectionHeight+"px);"),e.R7$(),e.Y8G("template",n.template)("panelBuilder",n.editPanelBuilderIn)}}function mt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",53)(1,"app-component-editor",56),e.bIt("componentChanged",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.componentChanged(!0))})("closeClicked",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.componentChanged(!1))}),e.k0s()()}if(2&o){const n=e.XpG(2);e.Aen("height: Calc(100vh - "+n.sectionHeight+"px);"),e.R7$(),e.Y8G("component",n.steps[n.selectedIndex].component)}}function gt(o,r){if(1&o&&(e.j41(0,"div",2,0),e.DNE(2,pt,25,8,"form",3)(3,ut,2,4,"div",4)(4,_t,2,4,"div",4)(5,mt,2,3,"div",4),e.k0s()),2&o){const n=e.XpG();e.R7$(2),e.vxM(n.steps&&n.steps.length>0&&n.templateForm&&((null==n.editPanelBuilderIn?null:n.editPanelBuilderIn.builderType)===n.builderType.EditComponent||(null==n.editPanelBuilderIn?null:n.editPanelBuilderIn.builderType)===n.builderType.EditSection)?2:-1),e.R7$(),e.vxM((null==n.editPanelBuilderIn?null:n.editPanelBuilderIn.builderType)===n.builderType.AddComponent?3:-1),e.R7$(),e.vxM((null==n.editPanelBuilderIn?null:n.editPanelBuilderIn.builderType)===n.builderType.AddSection?4:-1),e.R7$(),e.vxM((null==n.editPanelBuilderIn?null:n.editPanelBuilderIn.builderType)===n.builderType.EditComponentSetup&&n.steps[n.selectedIndex].component?5:-1)}}let ht=(()=>{class o{constructor(n,t,i,a,s,c,p,h,g,b){this.systemPropertyService=n,this.builderService=t,this.dataService=i,this.alertService=a,this.toast=s,this.cdRef=c,this.layoutService=p,this.rolesService=h,this.instanceService=g,this.unsavedChangesGuard=b,this.editInstanceSectionsIn=[],this.onlyContent=!1,this.menuClosed=new e.bkB,this.detectChanges=new e.bkB,this.editPanelBuilderChanged=new e.bkB,this.builderType=N.V,this.componentDestroyed$=new y.B,this.selectedIndex=0,this.instanceSectionsIn=[],this.sectionTypes=O.F,this.steps=[],this.stepTypes=k,this.contentHeight=0,this.sectionHeight=0,this.autosaveTriggered=!1}ngAfterViewInit(){this.sectionHeight=this.elementView?.nativeElement.getBoundingClientRect().top,this.contentHeight=this.sectionHeight+100,"xs"===this.layoutService.currentScreenSize&&(this.contentHeight+=85,this.sectionHeight+=85)}ngOnInit(){this.autosaveTriggered=!1,this.subscribeToSectionChanges(),this.createTemplateFormGroup(),this.setupSteps()}ngAfterContentChecked(){this.autosaveTriggered||this.cdRef.detectChanges(),this.sectionHeight=this.elementView?.nativeElement.getBoundingClientRect().top,this.contentHeight=this.sectionHeight+100,"xs"===this.layoutService.currentScreenSize&&(this.contentHeight+=85,this.sectionHeight+=85)}getState(n){return n.typeBw===k.Component?"empty":"section"}getStepEditIcon(n){const t=this.steps[n];return t.typeBw===this.stepTypes.Component?"":t.sectionIndex}ngOnChanges(n){if(n.template&&this.steps&&(this.setupSteps(),this.createTemplateFormGroup()),n.editPanelBuilderIn)switch(this.editPanelBuilderIn?.builderType){case this.builderType.EditComponentSetup:this.selectedInstanceSectionComponent=this.template.instanceSections.find(t=>t.instanceSectionComponents.some(i=>i.component.id===this.editPanelBuilderIn?.id))?.instanceSectionComponents.find(t=>t.component.id===this.editPanelBuilderIn?.id);break;case this.builderType.EditSection:this.section=this.template.instanceSections.find(t=>t.section.id===this.editPanelBuilderIn?.id)?.section,this.setStep(this.editPanelBuilderIn?.id,!0);break;case this.builderType.AddComponent:{const t=this.template.instanceSections.find(i=>i.id===this.editPanelBuilderIn.id)?.section;if(t?.typeBw===O.F.Dynamic){this.section=t;break}break}case this.builderType.AddSection:break;default:this.steps?this.setStep(this.editPanelBuilderIn?.id,!0):this.setupSteps()}}setupSteps(n=!1){const t=[];let i=0;this.template.instanceSections.filter(a=>!(a.section.typeBw===O.F.Dynamic&&a.section.templateId)).forEach(a=>{i+=1;let s=1;t.push({instanceSection:a,section:a.section,sectionIndex:i,sectionComponentsLength:a.instanceSectionComponents.length??null,component:null,typeBw:a.section.typeBw===O.F.Dynamic?k.AuthToolSection:k.TemplateSection}),a.instanceSectionComponents.forEach(c=>{"Banner"!==c.component.componentType.name&&!1!==c.component.templateField?.isBuilderEnabled&&(t.push({instanceSection:a,section:a.section,instanceSectionComponentId:c.id,sectionComponentsLength:a.instanceSectionComponents.length??null,component:c.component,componentIndex:s,typeBw:k.Component}),s+=1)})}),this.steps=t,this.setStep(this.editPanelBuilderIn?.id,n)}setSectionBuilder(n){this.editPanelBuilderIn&&this.editPanelBuilderIn.builderType===this.builderType.AddSection&&""===this.editPanelBuilderIn.id&&this.addNewInstanceSection(n)}subscribeToSectionChanges(){this.builderService.selectedSection$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{n?this.section=n:this.closeMenu()}),this.builderService.sectionUpdated$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{const t=this.template.instanceSections.findIndex(i=>i.section.id===n.id);-1!==t&&(this.template.instanceSections[t].section=n,this.createTemplateFormGroup())})}addNewInstanceSection(n){const t=Math.max(...this.template.instanceSections.map(a=>a.sortOrder)),i={instanceId:this.instance.id,templateId:this.template.id,sortOrder:t+1,title:n.title??"",isHidden:!1,backgroundColor:n.backgroundColor,masterSectionId:n.id};i&&this.dataService.addInstanceSection(i).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(a=>{if(a){this.templateForm.addControl(a.id,new _.J3({title:new _.hs,description:new _.hs,hideBackground:new _.hs,sortOrder:new _.hs,typeId:new _.hs}));const s={id:a.id,sortOrder:0,section:a.section,instanceSectionComponents:[],title:"",backgroundColor:a.backgroundColor,masterSectionId:a.masterSectionId};a.instanceSectionComponents&&(s.instanceSectionComponents=a.instanceSectionComponents),this.template.instanceSections.push(s),this.setDirectNewSectionOrder(s),this.editPanelBuilderIn.id=a.section.id,this.editPanelBuilderIn.builderType=this.builderType.EditSection,this.editPanelBuilderIn.heading="Edit Section",this.updateInstanceSections()}})}setDirectNewSectionOrder(n){const i=(this.editPanelBuilderIn.previousIndex??0)+1,a=this.template.instanceSections.indexOf(n);if(-1!==a){const s=this.template.instanceSections.splice(a,1);this.template.instanceSections.splice(i,0,s[0]);const c=this.template.instanceSections.find(p=>p.id===n.id);c&&c.section&&(this.builderService.selectedSection$.next(c.section),this.editInstanceSectionsIn.push(c))}this.template.instanceSections.forEach(s=>{const c=this.template.instanceSections.findIndex(p=>p.id===s.id);s.sortOrder=c})}getStepIndexById(n){return this.steps.findIndex(t=>t.section.id===n&&!t.component||t.component?.id===n&&t.section.id!==n)}getMasterSection(n){const t=this.template.instanceSections.find(i=>i.section.id===n);return t?.masterSectionId?this.template.instanceSections.find(i=>i.section.id===t.masterSectionId)?.section:this.template.instanceSections.find(i=>i.section.typeBw===O.F.Dynamic&&i.section.templateId)?.section}persistEditPanelBuilder(n){const t=this.steps[n];t&&(this.editPanelBuilderIn.id=t?.component?t.component.id:t.section?.id,this.editPanelBuilderChanged.next(null))}setStep(n,t=!1){const i=this.getStepIndexById(n);-1!==i&&(t=this.selectedIndex!==i,this.focusOnEditableStep(i),!0===t&&this.setOnScroll(n))}focusOnEditableStep(n){this.selectedIndex=n}setOnScroll(n){setTimeout(()=>{const t=document.getElementById(`${n}-left`);t&&t.scrollIntoView(!1)},1)}setIndexDirect(){const n=this.selectedIndex;this.selectedIndex=this.stepper.selectedIndex,this.focusOnEditableStep(this.selectedIndex),n!==this.stepper.selectedIndex&&this.save(this.steps[n]),this.persistEditPanelBuilder(this.selectedIndex>=0?this.selectedIndex:0)}goBack(){this.selectedIndex=this.stepper.selectedIndex-1,this.persistEditPanelBuilder(this.selectedIndex>=0?this.selectedIndex:0)}goForward(){if(this.save(this.steps[this.selectedIndex]),this.selectedIndex===this.steps.length-1&&""!==this.editPanelBuilderIn.id)return this.closeMenu(),void("Classroom"==this.instance.feature.title&&this.toast.presentToastWithOptions().then(n=>{"Open"==n&&this.instanceService.openInstance("instance",this.instance.id,"assignments","grid")}));this.selectedIndex=this.stepper.selectedIndex+1,this.persistEditPanelBuilder(this.selectedIndex>=0?this.selectedIndex:0)}closeMenu(){this.editPanelBuilderIn.id="",this.menuClosed.next(null)}createTemplateFormGroup(){const n={};this.template.instanceSections.forEach(t=>{const i={};i.title=new _.hs(t.section?.title),i.description=new _.hs(t.section?.description),i.hideBackground=new _.hs(t.section?.hideBackground),i.sortOrder=new _.hs(t.section?.sortOrder),i.typeId=new _.hs(t.section?.typeId),t.instanceSectionComponents?.forEach(a=>{i[a.component.id]=new _.hs({value:this.getTemplateFieldValue(a)??"",disabled:!1===a?.component?.templateField?.isBuilderEnabled},a?.component?.templateField?.isRequiredField?_.k0.required:null)}),n[t.section.id]=new _.J3(i)}),this.templateForm=new _.J3(n),this.templateForm.valueChanges.pipe((0,J.B)(5e3)).subscribe(t=>{this.autosaveTriggered=!0,this.unsavedChangesGuard.canDeactivateVar=!1,this.save(this.steps[this.selectedIndex])})}save(n){if(this.editPanelBuilderIn&&this.editPanelBuilderIn.id&&n?.component&&n?.section&&n?.instanceSectionComponentId){if("Question"===n?.component?.componentType?.parentTypeName)return this.toast.presentToast("Step updated successfully!"),void this.checkShouldClose();if(this.getFormControl(n?.section?.id,n?.component?.id).pristine&&"Listing Details"!==n?.component?.componentType?.name&&"Full Name"!==n?.component?.componentType?.name&&"Phone Number"!==n?.component?.componentType?.name&&"Email Chips"!==n?.component?.componentType?.name&&"Address Search"!==n?.component?.componentType?.name&&"External Html Block"!==n?.component?.componentType?.name&&"Question Builder"!==n.component?.componentType?.name&&"Icon & Dropdown"!==n?.component?.componentType?.name)return void this.checkShouldClose();let t=!1,i=!1;const a=[],s=this.templateForm.get([n.section.id,n.component.id])?.value;if(void 0===s)return void this.checkShouldClose();if(n.component?.templateField?.systemProperty)i=!0,this.systemPropertyService.setSystemPropertyValue(n.component.templateField.systemProperty,s);else if("Question Builder"===n.component?.componentType?.name){const c={...s,questionAnswers:s.questionAnswers.map(p=>({...p,id:""!==p.id?p.id:null}))};this.systemPropertyService.setAssetSystemPropertyValueByKey("Question.QuestionTypeId",c.questionTypeId),this.systemPropertyService.setAssetSystemPropertyValueByKey("Question.QuestionText",c.questionText??""),this.systemPropertyService.setAssetSystemPropertyValueByKey("Question.QuestionDescription",c.questionDescription??""),c.isDynamicOptions&&-1===s.questionType.name.indexOf("Yes")&&-1===s.questionType.name.indexOf("True")&&(c.questionAnswers=c.questionAnswers.filter(p=>!0===p.isCorrect)),a.push(this.dataService.updateQuestion(c))}else if("Communication Manager"===n.component?.componentType?.name){const c=JSON.parse(s);a.push(this.dataService.updateAddCommunicationBlocks(c))}else if("User Tags"===n?.component?.templateField?.dropDownLinkType?.title){const c=JSON.parse(s);a.push(this.dataService.updateRemoveUserTags(c))}else if("Campaign User Tags"===n?.component?.templateField?.dropDownLinkType?.title){const c=JSON.parse(s);a.push(this.dataService.updateCampaignTags(c,this.instance.id))}else"Question Builder"!==n.component?.componentType?.name&&(t=!0);if(this.instance){if(t&&"Question Manager"!==this.instance.feature?.featureType?.name){a.push(this.dataService.updateInstanceSectionComponent(n.instanceSectionComponentId,s));const c=this.template.instanceSections.findIndex(p=>p.id===n.instanceSection.id);if(-1!==c){const p=this.template.instanceSections[c].instanceSectionComponents.findIndex(h=>h.id===n.instanceSectionComponentId);this.template.instanceSections[c].instanceSectionComponents[p].value=s}}if(i||"Listing Details"===n?.component?.componentType?.name&&(void 0===n?.component?.templateField.isInherit||!0===n?.component?.templateField?.isInherit))if(a.push(this.systemPropertyService.persistSystemPropertyValues(P.T.Instance,this.instance.id)),this.id){const c=this.instance?.feature?.featureType?.systemPropertyType?.typeBw;c&&a.push(this.systemPropertyService.persistSystemPropertyValues(c,this.id))}else this.instance.feature?.featureType?.systemPropertyType?.typeBw===P.T.User&&a.push(this.systemPropertyService.persistSystemPropertyValues(P.T.User,this.instance.id));"Achievement Completion"===this.instance.feature.featureType.name&&a.push(this.systemPropertyService.persistSystemPropertyValues(P.T.CredentialEngineBadge,this.instance.id)),("Full Name"===n?.component?.componentType?.name||"Phone Number"===n?.component?.componentType?.name||"Email Chips"===n?.component?.componentType?.name)&&a.push(this.systemPropertyService.persistSystemPropertyValues(P.T.User,this.instance.id)),"Address Search"===n?.component?.componentType?.name&&("Organization Manager"===this.instance.feature.featureType.name?a.push(this.systemPropertyService.persistSystemPropertyValues(P.T.Organization,this.routeParams.instanceSlug??"")):"Internal"===this.instance.feature.featureType.name||"Learning Objects"===this.instance.feature.featureType.name||"Marketing Objects"===this.instance.feature.featureType.name||"Training Objects"===this.instance.feature.featureType.name||"Landing Pages"===this.instance.feature.featureType.name?a.push(this.systemPropertyService.persistSystemPropertyValues(P.T.Instance,this.instance.id)):"User Manager"===this.instance.feature.featureType.name&&a.push(this.systemPropertyService.persistSystemPropertyValues(P.T.User,this.instance.id)))}if(0===a.length)return void this.checkShouldClose();(0,Q.p)(a).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.toast.presentToast("Step updated successfully!"),this.checkShouldClose()})}this.unsavedChangesGuard.canDeactivateVar=!0}updateQuestionValue(n,t){this.dataService.updateQuestion({...n,questionTypeId:n.questionType.id,questionAnswers:n.isDynamicOptions?n.questionAnswers.filter(i=>!0===i.isCorrect).map(i=>({...i,id:""!==i.id?i.id:null})):n.questionAnswers.map(i=>({...i,id:""!==i.id?i.id:null}))},this.instance.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{const i=this.template.instanceSections.findIndex(s=>s.id===t.instanceSection.id),a=this.template.instanceSections[i].instanceSectionComponents.findIndex(s=>s.id===t.instanceSectionComponentId);this.template.instanceSections[i].instanceSectionComponents[a].component.question={...n}})}checkShouldClose(){this.selectedIndex===this.steps.length&&this.closeMenu()}getTemplateFieldValue(n){return n?.component?.templateField?.systemProperty||!0===n?.component?.templateField?.isInherit?n?.component?.templateField?this.systemPropertyService.getSystemPropertyValue(n?.component?.templateField?.systemProperty):null:n?.value}getFormControl(n,t){return t&&this.templateForm.get([n,t])?this.templateForm.get([n,t]):{}}sectionComponentsUpdated(n){const t=this.template.instanceSections.findIndex(i=>i.id===this.editPanelBuilderIn.id);if(-1!==t){const i=n.find(a=>-1===this.template.instanceSections[t].instanceSectionComponents.findIndex(s=>s.id===a.id));this.template.instanceSections[t].instanceSectionComponents=n,i&&(this.editPanelBuilderIn.id=i?.component?.id)}this.setupSteps(!0),this.createTemplateFormGroup(),this.editPanelBuilderIn.builderType=this.builderType.EditComponent}componentChanged(n){!0===n&&this.forceChangeDetection(),this.editPanelBuilderIn.builderType=this.builderType.EditComponent}editComponentSetup(){const n=this.steps[this.selectedIndex];this.editPanelBuilderIn.id=n.component?.id??"",this.editPanelBuilderIn.builderType=this.builderType.EditComponentSetup,this.editPanelBuilderIn.heading="Edit Component Setup"}deleteSection(){const n=this.steps[this.selectedIndex];this.alertService.presentAlert("Confirm Delete","Are you sure you want to delete this Section").then(()=>{this.dataService.deleteInstanceSection(n.instanceSection.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{if(!0===t){const i=this.template.instanceSections.findIndex(a=>a.id===n.instanceSection.id);this.template.instanceSections.splice(i,1),this.forceChangeDetection(),this.persistEditPanelBuilder(this.selectedIndex-1),this.setupSteps()}})})}deleteSectionComponent(){const n=this.steps[this.selectedIndex];this.alertService.presentAlert("Confirm Delete","Are you sure you want to delete this Component").then(()=>{this.dataService.deleteInstanceSectionComponent(n.instanceSectionComponentId??"").pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{if(!0===t){const i=this.template.instanceSections.findIndex(s=>s.id===n.instanceSection.id),a=this.template.instanceSections[i].instanceSectionComponents.findIndex(s=>s.id===n.instanceSectionComponentId);this.template.instanceSections[i].instanceSectionComponents.splice(a,1),this.forceChangeDetection(),this.persistEditPanelBuilder(this.selectedIndex-1),this.setupSteps()}})})}moveSectionComponent(n,t){if(this.template.instanceSections.length>0){const i=this.template.instanceSections.findIndex(g=>g.section.id===n.section.id),a=this.template.instanceSections[i].instanceSectionComponents.findIndex(g=>g.component.id===n.component?.id),s=[],c=[this.template.instanceSections[i].id],p=this.steps[!0===t?this.selectedIndex-1:this.selectedIndex+1],h=this.template.instanceSections[i].instanceSectionComponents[a];if(p.typeBw===this.stepTypes.Component){const g=this.template.instanceSections[i].instanceSectionComponents.findIndex(b=>b.component.id===p.component?.id);if(p.component&&p.component?.builderRowNumber===h.component.builderRowNumber){const b=h.component.templateField.colNumber;h.component.templateField.colNumber=p.component?.templateField.colNumber,this.template.instanceSections[i].instanceSectionComponents[g].component.templateField.colNumber=b}else{const b=h.component.builderRowNumber;h.component.builderRowNumber=p.component?.builderRowNumber,this.template.instanceSections[i].instanceSectionComponents[g].component.builderRowNumber=b}this.template.instanceSections[i].instanceSectionComponents.splice(a,1),this.template.instanceSections[i].instanceSectionComponents.splice(!0===t?a-1:a+1,0,h)}else{const g=!0===t?i-1:i+1;c.push(this.template.instanceSections[g].id),this.template.instanceSections[i].instanceSectionComponents[a].instanceSectionId=this.template.instanceSections[g].id,this.template.instanceSections[i].instanceSectionComponents.splice(a,1);const b=Math.max(...this.template.instanceSections[g].instanceSectionComponents.map(R=>R.component.builderRowNumber??0));h.component.builderRowNumber=b+1,h.component.templateField.colNumber=0,!0!==t&&(h.component.builderRowNumber=0,this.template.instanceSections[g].instanceSectionComponents.forEach(R=>R.component.builderRowNumber=(R.component.builderRowNumber??0)+1)),this.template.instanceSections[g].instanceSectionComponents.push(h)}this.template.instanceSections.filter(g=>-1!==c.indexOf(g.id)).forEach(g=>{s.push(...g.instanceSectionComponents.map(b=>({...b,colNumber:b.component.templateField.colNumber,builderRowNumber:b.component.builderRowNumber})))}),-1!==a&&this.updateInstanceSectionComponents(s)}}instanceSectionUpdated(n,t){let i=this.template.instanceSections.findIndex(a=>a.id===t);-1!==i&&(this.template.instanceSections[i].title=n),i=this.steps.findIndex(a=>a.instanceSection.id===t),-1!==i&&(this.steps[i].instanceSection.title=n),this.forceChangeDetection()}hasAdminAccess(){return this.rolesService.hasRoleAccess([S.Q.Manage,S.Q.Publish])}updateInstanceSectionComponents(n){n?.length>0&&this.dataService.updateInstanceSectionComponents(n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{t&&this.setupSteps()})}allowSectionComponentMoveUp(n){if(0===this.selectedIndex||!n||n?.typeBw===this.stepTypes.TemplateSection||!0===n.component?.isLocked)return!1;const t=this.steps[this.selectedIndex-1],i=this.steps?.indexOf(n);if(null===t.component){const a=this.steps[this.selectedIndex-2],s=this.steps.find(c=>c.instanceSection.id===a?.instanceSection?.id&&null===c.component);if(s)return s.typeBw===this.stepTypes.AuthToolSection&&n.section.typeBw===O.F.Dynamic}return i>0&&n.section.typeBw===O.F.Dynamic}allowSectionComponentMoveDown(n){if(!n||n.typeBw===this.stepTypes.TemplateSection||!0===n.component?.isLocked)return!1;if(null===this.steps[this.selectedIndex+1]?.component){const i=this.steps[this.selectedIndex+1];if(this.steps.find(s=>s.instanceSection.id===i?.instanceSection?.id&&null===s.component))return i.typeBw===this.stepTypes.AuthToolSection&&n.section.typeBw===O.F.Dynamic}return n.section.typeBw===O.F.Dynamic}allowSectionMoveUp(n){return 0!==this.selectedIndex&&this.steps.filter(a=>a.typeBw!==k.Component)?.indexOf(n)>0&&n.typeBw===this.stepTypes.AuthToolSection}allowSectionMoveDown(n){const t=this.steps.filter(a=>a.typeBw!==k.Component);return t.indexOf(n)!==t.length-1&&n.typeBw===this.stepTypes.AuthToolSection}moveSection(n,t){if(this.template.instanceSections.length>0){const i=this.template.instanceSections.findIndex(s=>s.section.id===n.id),a=this.template.instanceSections[i];this.template.instanceSections.splice(i,1),this.template.instanceSections.splice(!0===t?i-1:i+1,0,a),this.template.instanceSections.forEach(s=>{const c=this.template.instanceSections.findIndex(p=>p.id===s.id);s.sortOrder=c}),n&&this.updateInstanceSections()}}updateInstanceSections(){this.sortInstanceSections(),this.instanceSectionsIn=this.template.instanceSections.map(n=>({id:n.id,instanceId:this.instance.id,templateId:this.template.id,sortOrder:n.sortOrder,backgroundColor:n.backgroundColor})),this.instanceSectionsIn?.length>0&&this.dataService.updateInstanceSections(this.instanceSectionsIn,!0).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{n&&this.setupSteps()})}toggleShowInstanceSection(n){n.isHidden=!n.isHidden;const t={...n,instanceId:this.instance.id,templateId:this.template.id,masterSectionId:null};this.dataService.updateInstanceSections([t],!1).pipe((0,u.Q)(this.componentDestroyed$)).subscribe()}updateInstanceSectionColor(n,t){t.backgroundColor=n;const i={...t,instanceId:this.instance.id,templateId:this.template.id,masterSectionId:null};this.dataService.updateInstanceSections([i],!1).pipe((0,u.Q)(this.componentDestroyed$)).subscribe()}sortInstanceSections(){this.template.instanceSections=this.template.instanceSections.sort((n,t)=>n.sortOrder-t.sortOrder)}forceChangeDetection(){this.detectChanges.next(null)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(le.a),e.rXU(G.V),e.rXU(v.u),e.rXU(j.u),e.rXU(X.A),e.rXU(e.gRc),e.rXU(x.Y),e.rXU(w.P),e.rXU(M.b),e.rXU(de.h))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-builder-side-panel"]],viewQuery:function(t,i){if(1&t&&(e.GBs(On,5),e.GBs(yn,5)),2&t){let a;e.mGM(a=e.lsd())&&(i.stepper=a.first),e.mGM(a=e.lsd())&&(i.elementView=a.first)}},inputs:{editPanelBuilderIn:"editPanelBuilderIn",editInstanceSectionsIn:"editInstanceSectionsIn",instance:"instance",template:"template",id:"id",isManager:"isManager",onlyContent:"onlyContent",routeParams:"routeParams"},outputs:{menuClosed:"menuClosed",detectChanges:"detectChanges",editPanelBuilderChanged:"editPanelBuilderChanged"},features:[e.Jv_([{provide:nn.x8,useValue:{displayDefaultIndicatorType:!1}}]),e.OA$],decls:1,vars:1,consts:[["content",""],["stepper",""],[1,"builder-side-panel"],[1,"stepper-form-container",3,"formGroup"],[1,"static-container",3,"style"],[1,"inner-content-container"],[1,"top-stepper-heading"],[1,"text-container"],[1,"actions-container"],[1,"actions"],["size","small","fill","clear","color","medium",1,"close-button",3,"click"],["name","close-circle-outline"],["cdkScrollable","",1,"stepper-container"],["matStepperIcon","i"],["orientation","vertical","cdkStepper","",3,"click","linear","selectedIndex"],["completed","false",3,"stepControl","state"],["matStepperIcon","empty"],["matStepperIcon","section"],["matStepperIcon","edit"],[1,"stepper-footer"],[1,"back-button"],["fill","clear",1,"inner-container"],[1,"next-button"],[1,"inner-container",3,"click"],[2,"color","black"],["matStepLabel",""],["matStepContent",""],[3,"ngClass"],[1,"text"],[1,"text-and-icon-container"],[3,"text"],[1,"icon-container"],[3,"isSectionHeading","text"],[3,"text","noPadding"],["name","chevron-up"],["name","chevron-down"],[1,"step-snap-container",3,"id"],[1,"step-component-container","selected"],[1,"header-row"],[3,"questionUpdated","component","instance","formGroupName","formGroup","showSelected","id","isSidePanelBuilder","rowBuilderView","routeParams"],[1,"header","title"],[1,"header","buttons"],[3,"click"],["name","trash"],["name","settings"],[3,"templateId","panelBuilder","section","templateForm","backGroundColor"],["name","eye-outline"],["name","eye-off-outline"],[3,"panelBuilder","instanceSection","instanceId","templateId"],[3,"instanceSectionUpdated","panelBuilder","instanceSection","instanceId","templateId"],[3,"sectionColorChanged","templateId","panelBuilder","section","templateForm","backGroundColor"],["fill","clear",1,"inner-container",3,"click"],["svgIcon","chevron-arrow-left"],[1,"static-container"],[3,"sectionComponentsChanged","section","panelBuilder"],[3,"sectionSelected","template","panelBuilder"],[3,"componentChanged","closeClicked","component"]],template:function(t,i){1&t&&e.DNE(0,gt,6,4,"div",2),2&t&&e.vxM(i.templateForm&&i.steps?0:-1)},dependencies:[tn.Q,on.k,pe.f,C.YU,d.Jm,d.iq,d.he,_.qT,_.cb,_.j4,_.$R,an.uv,U.V5,U.Ti,U.M6,U.xJ,U.yS,ue.An,sn.c,rn.O,_n,Cn,Mn],styles:["[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]{background-color:#2d2e32;height:100%;width:100%;position:relative}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]{width:100%;padding:10px 10px 10px 20px;overflow-y:auto}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .top-stepper-heading[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .top-stepper-heading[_ngcontent-%COMP%]   .actions-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .top-stepper-heading[_ngcontent-%COMP%]   .actions-container[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin:0;cursor:pointer}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .top-stepper-heading[_ngcontent-%COMP%]   .actions-container[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .top-stepper-heading[_ngcontent-%COMP%]   .actions-container[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:25px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .stepper-container[_ngcontent-%COMP%]{height:calc(100% - 10px)}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .stepper-container[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;margin-bottom:-3px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .stepper-container[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:fit-content;background:#f99e00;color:#000;border-color:#f99e00;border-width:1px;border-style:solid;border-radius:3px 3px 0 0;font-family:Roboto;font-weight:700;line-height:1.6;letter-spacing:.3px;text-align:left;height:24px;z-index:1000;font-size:12px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .stepper-container[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{padding:2px 10px 16px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .stepper-container[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{display:flex;flex-direction:row}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .stepper-container[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{height:23px;width:23px;font-size:16px;display:flex;flex-direction:column;justify-content:center;align-items:center;align-content:center;cursor:pointer}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   .stepper-container[_ngcontent-%COMP%]   .step-component-container[_ngcontent-%COMP%]{margin-top:2px;margin-bottom:10px;border-radius:3px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]{background-color:transparent;position:sticky;top:0;z-index:1}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-vertical-stepper-header{padding:20px 0 20px 10px;border-radius:5px;height:fit-content;width:100%}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-vertical-content-container{margin-left:22px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-step-icon-selected{border:1px solid rgb(249,158,0)!important;color:#f99e00!important;background-color:transparent}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-step-icon-content{font-family:Roboto;font-weight:900;color:#fff}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-step-icon{border:1px solid #6f6f6f;color:#6f6f6f;background-color:#2d2e32;margin-right:10px;z-index:2}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-vertical-content{padding:0 0 10px 25px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     mat-step-header[aria-selected=false]~.mat-stepper-vertical-line:before{top:-28px;bottom:-28px;border-left:#555555 2px solid}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     mat-step-header[aria-selected=true]~.mat-stepper-vertical-line:before{top:-90px;bottom:-45px;border-left:2px solid transparent;border-image:linear-gradient(to bottom,transparent 0%,rgb(249,158,0) 20%,transparent 90%)!important;border-image-slice:1!important}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-step-label{width:100%}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .inner-content-container[_ngcontent-%COMP%]   mat-stepper[_ngcontent-%COMP%]     .mat-step-text-label{font-size:18px;color:#949494}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]{background:linear-gradient(to bottom,#0000,#000000a6 29%,#000 53% 100%);left:0;right:0;width:100%;bottom:0;margin:auto;height:100px;display:flex;align-items:center;justify-content:space-between}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%]{margin-right:10px;--background: #f99e00}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{text-transform:none;color:#0000;border-radius:3px;font-family:Roboto;font-weight:500;font-size:1.125em;line-height:1;text-align:center;letter-spacing:.1em}@media screen and (max-width: 960px){[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 4px;min-height:36px;--padding-top: 0px;--padding-bottom: 0px}}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{margin-left:10px}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{--padding-start: 0 !important;text-transform:none;color:#aaa;border-radius:3px;font-weight:500;font-size:18px;text-decoration:underline;line-height:1;letter-spacing:.1em}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{width:24px;height:24px;padding-left:5px;margin-right:18px;color:#fff}@media screen and (max-width: 960px){[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--border-radius: 4px;min-height:36px;--padding-top: 0px;--padding-bottom: 0px}}@media (max-width: 960px){[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .stepper-form-container[_ngcontent-%COMP%]   .stepper-footer[_ngcontent-%COMP%]{justify-content:center}}[_nghost-%COMP%]   .builder-side-panel[_ngcontent-%COMP%]   .static-container[_ngcontent-%COMP%]{padding:10px 10px 0 20px;position:relative;height:100%}[_nghost-%COMP%]   .section-container[_ngcontent-%COMP%]{margin-top:2px;margin-bottom:10px;border-radius:3px;padding:10px}[_nghost-%COMP%]   .selected[_ngcontent-%COMP%]{border:1px solid rgb(249,158,0)}[_nghost-%COMP%]   .text-container[_ngcontent-%COMP%]{display:flex;align-items:center}[_nghost-%COMP%]   .text-container[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:#aaa;font-family:Roboto;font-weight:900;font-size:14px;font-style:italic;letter-spacing:.5px;text-align:left}[_nghost-%COMP%]   .text-and-icon-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between}[_nghost-%COMP%]   .text-and-icon-container[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]{color:#fff;font-size:20px;margin:0 10px;align-items:center;display:flex}"]})}}return o})();var f=l(10467),me=function(o){return o[o.Wizard=1]="Wizard",o[o.Editor=2]="Editor",o[o.Preview=3]="Preview",o}(me||{}),Ct=l(74805),$=l(50019);function ft(o,r){1&o&&e.nrm(0,"mat-icon",6)}function Pt(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-grid",0)(1,"ion-row",1)(2,"ion-col",2)(3,"span",3),e.EFF(4,"Preview"),e.k0s()(),e.j41(5,"ion-col",4)(6,"ion-button",5),e.bIt("click",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.openPublishOptions(i,a.instance.status))}),e.DNE(7,ft,1,0,"mat-icon",6),e.EFF(8),e.k0s()()()()}if(2&o){const n=e.XpG();e.R7$(7),e.vxM("publish"===n.publishButtonText?7:-1),e.R7$(),e.SpI(" ",n.publishButtonText," ")}}let bt=(()=>{class o{constructor(n,t,i,a){this.popOver=n,this.modalController=t,this.router=i,this.location=a,this.publishClicked=new e.bkB,this.showPublishedOptions=!0,this.typeOptions=me}ngOnInit(){this.checkFeatureTypes(),this.showPublishedOptions&&this.setPublishText()}checkFeatureTypes(){switch(this.instance?.feature?.featureType?.name){case"Product Manager":case"Question Manager":case"Organization Manager":case"Media Manager":case"User Manager":case"Network Manager":case"Communication Manager":this.showPublishedOptions=!1;break;default:this.showPublishedOptions=!0}}setPublishText(){this.publishButtonText=this.instance?.status?"private"!==this.instance.status?"unpublish":"publish":"unpublish"}openPublishOptions(n,t){var i=this;return(0,f.A)(function*(){if(t&&"private"===t){if("Modifiable Learning Container Pages"===i.instance?.feature.featureType.name)return void(yield i.openPublishDialog("organization"));const a=[{key:"organization",value:"To my organization"},{key:"public",value:"Publicly"},{key:"network",value:"To my network"}],s=yield i.popOver.create({component:$.u,cssClass:"question-type-popover",componentProps:{options:a},event:n,side:"bottom"});s.onDidDismiss().then(function(){var c=(0,f.A)(function*(p){p.data&&(yield i.openPublishDialog(p.data.key))});return function(p){return c.apply(this,arguments)}}()),yield s.present()}else i.publishClicked.emit("private"),i.appendUnpublishedToUrl(),i.publishButtonText="publish"})()}openPublishDialog(n){var t=this;return(0,f.A)(function*(){const i=yield t.modalController.create({component:Ct.D,cssClass:"confirm-dialog",componentProps:{headerText:"Update your page",bodyText:`You're about to update your ${t.instance?.feature?.title}. Once you publish your work it will be visible to enrolled people and other users at your Organization.`,buttonText:"Publish"}});i.onDidDismiss().then(a=>{"confirm"===a.role&&(t.publishClicked.emit(n),t.removeUnpublishedFromUrl(),t.publishButtonText="unpublish")}),yield i.present()})()}appendUnpublishedToUrl(){const n=this.currentCustomUrl||this.router.url;n.endsWith("/unpublished")||(this.currentCustomUrl=n.endsWith("/")?`${n}unpublished`:`${n}/unpublished`,this.location.go(this.currentCustomUrl))}removeUnpublishedFromUrl(){let n=this.currentCustomUrl||this.router.url;n.endsWith("/unpublished")&&(n=n.replace("/unpublished",""),this.location.go(n),this.currentCustomUrl=n)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(d.IE),e.rXU(d.W3),e.rXU(I.Ix),e.rXU(C.aZ))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-builder-view-options-row"]],inputs:{publishDisabled:"publishDisabled",instance:"instance",showPublishedOptions:"showPublishedOptions"},outputs:{publishClicked:"publishClicked"},decls:1,vars:1,consts:[[1,"parent-container"],[1,"view-options-row","ion-justify-content-between"],["size","auto",1,"preview-col"],[1,"preview-header"],["size","auto",1,"publish-col"],["color","warning","fill","solid",1,"publish-button",3,"click"],["slot","end","svgIcon","caret_down"]],template:function(t,i){1&t&&e.DNE(0,Pt,9,2,"ion-grid",0),2&t&&e.vxM(i.instance&&i.showPublishedOptions?0:-1)},dependencies:[d.Jm,d.hU,d.lO,d.ln,ue.An],styles:[".parent-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]{height:45px;position:relative}.parent-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .preview-col[_ngcontent-%COMP%]{display:flex;align-items:center}.parent-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .preview-col[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]{color:#fff;font-family:Roboto;font-size:25px;font-weight:700}@media (min-width: 960px){.parent-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .preview-col[_ngcontent-%COMP%]{width:100%!important;justify-content:center}}@media (max-width: 960px){.parent-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .preview-col[_ngcontent-%COMP%]{justify-content:flex-start}}.parent-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .publish-col[_ngcontent-%COMP%]{position:absolute;right:0}.parent-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .publish-col[_ngcontent-%COMP%]   .publish-button[_ngcontent-%COMP%]{fill:#f99e00;cursor:pointer}"]})}}return o})();var Z=l(17763);function Mt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",3)(1,"div",10)(2,"ion-row",11)(3,"ion-col",12)(4,"ion-fab-button",13),e.bIt("click",function(){e.eBV(n);const i=e.XpG();return e.Njj(i.goBack(i.routeParams.viewType))}),e.nrm(5,"ion-icon",14),e.k0s()(),e.j41(6,"ion-col",15)(7,"div",16)(8,"span",17),e.EFF(9,"You are creating a"),e.k0s()(),e.j41(10,"div",18)(11,"span",19),e.EFF(12),e.nI1(13,"parsePipe"),e.nI1(14,"async"),e.nrm(15,"ion-icon",20),e.k0s()()()()()()}if(2&o){let n;const t=e.XpG();e.R7$(),e.Aen("--background-image:url("+t.assetUrl+");"),e.R7$(11),e.SpI("",e.bMT(14,6,e.i5U(13,3,null!==(n=null==t.instance||null==t.instance.feature?null:t.instance.feature.title)&&void 0!==n?n:"",null==t.instance?null:t.instance.id)),". ")}}function Ot(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",21),e.bIt("cdkDragMoved",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.onDragMove(i))}),e.nrm(1,"ion-icon",22),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("cdkDragFreeDragPosition",n.dragPosition)}}let yt=(()=>{class o{constructor(n,t,i,a){this.rolesService=n,this.dataService=t,this.instanceService=i,this.breadCrumbservice=a,this.onlyContent=!1,this.setupSteps=new e.bkB,this.openMenu=!1,this.builderType=N.V,this.editPanelBuilder={},this.editInstanceSections=[],this.drawerWidth=40,this.leftMargin=0,this.contentWidth=100,this.dragPosition={x:0,y:0},this.componentDestroyed$=new y.B,this.isManager=!1}ngOnInit(){this.setupSidePanelBuilder(!1),this.setGradientImageOverlay()}ngOnChanges(n){n.template&&this.setupSidePanelBuilder(!1)}setGradientImageOverlay(){this.assetUrl=this.instance.coverMediaAssetId?`${B.c.contentUrl}asset/${this.instance.coverMediaAssetId}/content`:this.instance.feature.coverMediaAssetId?`${B.c.contentUrl}asset/${this.instance.feature.coverMediaAssetId}/content`:"assets/images/defaultbackgroundgradient.png"}setupSidePanelBuilder(n){if(this.isManager=-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("manager"),(-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("user")||-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("organization"))&&window.innerWidth>768&&!n)return;const t=this.template.instanceSections.findIndex(a=>a.instanceSectionComponents?.length>0),i=this.template.instanceSections[t];if(i&&i.instanceSectionComponents.some(a=>a)&&(this.publishEnabled()||!0===this.isManager)){const a=i.instanceSectionComponents[0].component;a&&(this.editPanelBuilder={id:a.id,builderType:this.builderType.EditComponent,heading:"Edit Component"},this.openSideMenu())}else i&&i.section.typeBw===O.F.Dynamic&&!i.section.templateId&&this.publishEnabled()?(this.editPanelBuilder={id:i.id,builderType:this.builderType.EditSection,heading:"Edit Section"},this.openSideMenu()):this.closeMenu()}publishEnabled(){return!0===this.isManager||this.rolesService.hasRoleAccess([S.Q.Publish])}sideBuilderUpdated(){this.editPanelBuilder={...this.editPanelBuilder},this.editPanelBuilder.builderType===this.builderType.SortSection||this.editPanelBuilder.builderType===this.builderType.SortComponent?this.closeMenu():this.openSideMenu()}openSideMenu(){this.publishEnabled()&&(!0!==this.isManager||-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("user")||-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("organization"))&&window.innerWidth>768?(this.contentWidth=100-this.drawerWidth,this.leftMargin=this.drawerWidth):(!0===this.isManager||window.innerWidth<=768)&&(this.contentWidth=0,this.leftMargin=0,this.drawerWidth=100),(this.publishEnabled()||!0===this.isManager||window.innerWidth<=768)&&(this.openMenu=!0)}closeMenu(){this.openMenu=!1,this.contentWidth=100,this.leftMargin=0}onDragMove(n){this.drawerWidth=this.getNewWidth(n),this.contentWidth=100-this.drawerWidth,this.leftMargin=this.drawerWidth}updateInstanceStatus(n){const t=[this.dataService.updateInstanceStatus(this.instance.id,n)];"Media Manager"===this.instance?.feature?.featureType?.name&&this.routeParams.instanceSlug&&t.push(this.dataService.publishAsset(this.routeParams.instanceSlug)),(0,Q.p)(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.instance.status=n,this.setupSidePanelBuilder(!1)})}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}getId(){return this.instanceService.isValidGUID(this.routeParams.instanceSlug??"")?this.routeParams.instanceSlug:this.instance.id}goBack(n){this.breadCrumbservice.goToPrev(n)}forceChangeDetection(){this.template.instanceSections=[...this.template.instanceSections.map(n=>({...n,instanceSectionComponents:n.instanceSectionComponents.map(t=>({...t,component:{...t.component}}))}))],this.template={...this.template}}editPanelBuilderChanged(){this.editPanelBuilder={...this.editPanelBuilder}}getNewWidth(n){const t=document.getElementById("#side-nav-container")?.offsetWidth??0,i=document.getElementById("#side-nav")?.offsetWidth??0,a=n.source.getFreeDragPosition().x+i;this.dragPosition={x:0,y:0};const s=a/t*100;return s<50?s>20?s:20:50}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(w.P),e.rXU(v.u),e.rXU(M.b),e.rXU(H.b))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-instance-side-panel-builder"]],inputs:{template:"template",instance:"instance",featureTab:"featureTab",routeParams:"routeParams",onlyContent:"onlyContent"},outputs:{setupSteps:"setupSteps"},features:[e.OA$],decls:10,vars:30,consts:[[1,"side-panel-builder-container"],["id","#side-nav-container",3,"ngClass"],["id","#side-nav","position","start","mode","side",1,"side-nav-inner-container",3,"openedChange","opened"],[1,"small-header"],[3,"menuClosed","detectChanges","editPanelBuilderChanged","editPanelBuilderIn","editInstanceSectionsIn","instance","template","id","isManager","onlyContent","routeParams"],[1,"builder-content"],["cdkDrag","","cdkDragLockAxis","x",1,"cdk-drag-handle",3,"cdkDragFreeDragPosition"],[3,"publishClicked","instance","showPublishedOptions","publishDisabled"],[1,"preview-parent-content-container",3,"ngClass"],[3,"setupStepsOnEditClick","editAddClicked","id","panelState","template","instance","routeParams","disabled","editInstanceSections","editPanelBuilderIn"],[1,"image-header-gradient"],[1,"inner-container"],[1,"back-button-col"],["size","small","aria-label","Go Back",1,"back-btn",3,"click"],["name","arrow-back-outline"],["size","10",1,"header-col"],[1,"description-container"],[1,"description"],[1,"title-container"],[1,"title"],["name","information-circle-outline"],["cdkDrag","","cdkDragLockAxis","x",1,"cdk-drag-handle",3,"cdkDragMoved","cdkDragFreeDragPosition"],["cdkDragHandle","","src","/assets/icons/ExpandIcon.svg",1,"svg-icon"]],template:function(t,i){if(1&t&&(e.j41(0,"div",0)(1,"mat-sidenav-container",1)(2,"mat-sidenav",2),e.mxI("openedChange",function(s){return e.DH7(i.openMenu,s)||(i.openMenu=s),s}),e.DNE(3,Mt,16,8,"div",3),e.j41(4,"app-builder-side-panel",4),e.bIt("menuClosed",function(){return i.closeMenu()})("detectChanges",function(){return i.forceChangeDetection()})("editPanelBuilderChanged",function(){return i.editPanelBuilderChanged()}),e.k0s()(),e.j41(5,"mat-sidenav-content",5),e.DNE(6,Ot,2,1,"div",6),e.j41(7,"app-builder-view-options-row",7),e.bIt("publishClicked",function(s){return i.updateInstanceStatus(s)}),e.k0s(),e.j41(8,"div",8)(9,"app-builder-instance-preview",9),e.bIt("setupStepsOnEditClick",function(s){return i.setupSidePanelBuilder(s)})("editAddClicked",function(){return i.sideBuilderUpdated()}),e.k0s()()()()()),2&t){let a,s;e.R7$(),e.Y8G("ngClass",i.onlyContent?"side-nav-container no-header":"side-nav-container with-header"),e.R7$(),e.xc7("width",i.drawerWidth,"%"),e.R50("opened",i.openMenu),e.R7$(),e.vxM(i.onlyContent?3:-1),e.R7$(),e.Y8G("editPanelBuilderIn",i.editPanelBuilder)("editInstanceSectionsIn",i.editInstanceSections)("instance",i.instance)("template",i.template)("id",null!==(a=i.getId())&&void 0!==a?a:"")("isManager",i.isManager)("onlyContent",i.onlyContent)("routeParams",i.routeParams),e.R7$(),e.xc7("width",i.contentWidth,"%")("margin-left",0!==i.leftMargin?i.leftMargin:null,"%"),e.R7$(),e.vxM(!0===i.openMenu?6:-1),e.R7$(),e.Y8G("instance",i.instance)("showPublishedOptions",!i.isManager)("publishDisabled",!i.publishEnabled()),e.R7$(),e.Y8G("ngClass",!0===i.onlyContent?"preview-parent-container-height":"preview-parent-container-height-with-header"),e.R7$(),e.Y8G("id",null!==(s=i.getId())&&void 0!==s?s:"")("panelState",i.openMenu)("template",i.template)("instance",i.instance)("routeParams",i.routeParams)("disabled",!i.publishEnabled())("editInstanceSections",i.editInstanceSections)("editPanelBuilderIn",i.editPanelBuilder)}},dependencies:[C.YU,d.hU,d.YW,d.iq,d.ln,V.LG,V.US,V.El,L.T1,L.Fb,en,ht,bt,C.Jj,Z.F],styles:['[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]{height:100px;width:100%}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#1e1e1e59 25%,#1e1e1e 75%),var(--background-image);background-size:cover;height:100px;border-right:3px solid #292929;border-bottom:2px solid #454545;display:flex;align-items:flex-end;width:100%}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{display:flex;align-items:flex-end;width:100%}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .back-button-col[_ngcontent-%COMP%]{width:75px;max-width:75px;padding-left:20px;padding-right:15px}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .back-button-col[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{margin-bottom:15px;font-weight:500;font-size:18px;line-height:1}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .back-button-col[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:24px;height:24px}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]   .title-container[_ngcontent-%COMP%]{margin-bottom:10px}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]   .title-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{padding-left:15px;color:#fff;font-weight:900;font-family:"Exo 2";font-size:1.875em;line-height:1.1;letter-spacing:.03em}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]   .title-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   ion-icon[name=information-circle-outline][_ngcontent-%COMP%]{display:inline-block!important;width:22px;height:22px;font-size:22px;padding-left:9px;color:#f99e00}[_nghost-%COMP%]   .small-header[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]   .description-container[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{color:#aaa;font-family:Roboto;font-weight:400;font-size:.875em;font-style:italic;letter-spacing:.03em;line-height:1.1;padding-bottom:0;padding-left:15px}[_nghost-%COMP%]   .with-header[_ngcontent-%COMP%]{height:calc(100% - 250px)}[_nghost-%COMP%]   .no-header[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]{display:flex;flex-direction:row;height:100%;background-color:transparent}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .side-nav-inner-container[_ngcontent-%COMP%]{background-color:#2d2e32}@media (min-width: 956px){[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-container-height[_ngcontent-%COMP%]{height:calc(100vh - 150px)}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-container-height-with-header[_ngcontent-%COMP%]{height:calc(100vh - 270px)}}@media (min-width: 1300px){[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-container-height-with-header[_ngcontent-%COMP%]{height:calc(100vh - 290px)}}@media (min-width: 1700px){[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-container-height-with-header[_ngcontent-%COMP%]{height:calc(100vh - 300px)}}@media (max-width: 956px){[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-container-height[_ngcontent-%COMP%]{height:calc(100vh - 200px)}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-container-height-with-header[_ngcontent-%COMP%]{height:calc(100vh - 310px)}}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]{--offset-bottom: auto !important;--overflow: hidden;overflow:auto}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .preview-parent-content-container[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .builder-content[_ngcontent-%COMP%]{max-width:1250px;margin:0 auto;padding-left:2.5vw;padding-right:1.5vw;padding-top:15px;overflow:hidden}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .builder-content[_ngcontent-%COMP%]   .builder-preview[_ngcontent-%COMP%]{height:100%;width:100%;float:left}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .cdk-drag-handle[_ngcontent-%COMP%]{position:absolute;left:0;top:50%;display:flex;align-items:center;justify-content:center}[_nghost-%COMP%]   .side-panel-builder-container[_ngcontent-%COMP%]   .side-nav-container[_ngcontent-%COMP%]   .cdk-drag-handle[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{border-top-right-radius:5px;border-bottom-right-radius:5px;padding:15px 5px 15px 0;background-color:#2d2e32;font-size:25px;color:gray;cursor:ew-resize}[_nghost-%COMP%]     .mat-drawer-inner-container{overflow-y:hidden}']})}}return o})(),wt=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=e.$C({type:o})}static{this.\u0275inj=e.G2t({imports:[pe.f,_e.U,Y.G,E.T,je.x,L.ad,Ve.t]})}}return o})();var K=l(85359),vt=l(14591),xt=l(28228),ge=l(47347),he=l(38694),m=l(86737),St=l(96896),It=l(56375),Ce=l(18121),A=l(96354),Tt=l(15389),F=l(54622),Ft=l(69193),q=l(85758),ee=l(75351),ne=l(71190);let fe=(()=>{class o{constructor(n,t,i,a,s,c,p){this.dataService=n,this.storageService=t,this.dialog=i,this.activatedRoute=a,this.layoutService=s,this.scormService=c,this.rolesService=p,this.selectedItem=1,this.optionSelected=new e.bkB,this.rowFiltered=new e.bkB,this.buttonChanged=new e.bkB,this.mobileMenuClicked=new e.bkB,this.userSelected=new e.bkB,this.viewType=m.q,this.componentDestroyed$=new y.B,this.users=[],this.usersSelectOptions=[],this.hidden=!1}ngOnInit(){this.checkHidden(),this.getFilterComponents(),this.getUsers(),this.currentSlug=this.activatedRoute.snapshot.data.featureSlug??this.activatedRoute.snapshot.params.featureSlug,"global-search"===this.currentSlug&&(this.searchValue=this.storageService.initialSearchVal??"",this.filterRows(),this.storageService.globalSearchValue$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{this.searchValue=n,this.filterRows()}))}getFilterComponents(){this.dataService.getFilters(this.template.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{n&&(n.length>2&&(n.length=2),this.templateFields=n)})}getUsers(){this.users$=this.dataService.getPeopleTableByInstanceId(this.instance.id,0,100).pipe((0,u.Q)(this.componentDestroyed$),(0,A.T)(n=>(n&&(this.users=n,this.usersSelectOptions=n.map(t=>({id:t.userId,value:t.name&&t.name.length>0?t.name:t.userId}))),n)))}selectUser(n){this.userSelected.emit(n)}selectOption(n){"xs"===this.layoutService.currentScreenSize?(this.optionSelectedChange(Number(n.detail.value)),this.selectedItem=Number(n.detail.value)):this.optionSelectedChange(n)}generateScormFile(n){this.scormService.generateScormFile(this.instance,n)}optionSelectedChange(n){this.optionSelected.emit(n),this.searchValue=""}filterRows(n){n?.detail?.value&&""!==n?.detail?.value?this.rowFiltered.emit(n.detail.value):this.searchValue&&""!==this.searchValue&&this.rowFiltered.emit(this.searchValue)}buttonClicked(n){this.buttonChanged.emit(n)}openTagModal(n,t){this.tagDialog=this.dialog.open(Ft.t,{disableClose:!0,data:{tagId:n??"4c9f2d31-8b20-4461-a5f3-eeb09fcad575",level:t??3,view:"isFilter"}}),this.tagDialog.afterClosed().pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{})}openMobileMenu(){this.mobileMenuClicked.emit(!0)}checkHidden(){this.hidden=this.template.instanceSections.every(n=>n.instanceSectionComponents?.every(t=>"People Table"===t.component.componentType.name||"Users Table"===t.component.componentType.name)),!1===this.hidden&&(this.hidden=this.selectedItem===m.q.Player&&!this.hasAdminAccess)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(q.n),e.rXU(ee.bZ),e.rXU(I.nX),e.rXU(x.Y),e.rXU(ne.O),e.rXU(w.P))}}static{this.\u0275dir=e.FsC({type:o,inputs:{canAdd:"canAdd",addRoute:"addRoute",template:"template",featureTab:"featureTab",selectedItem:"selectedItem",instance:"instance",selectedUserId:"selectedUserId",isEducator:"isEducator",hasAdminAccess:"hasAdminAccess",hideAddButton:"hideAddButton"},outputs:{optionSelected:"optionSelected",rowFiltered:"rowFiltered",buttonChanged:"buttonChanged",mobileMenuClicked:"mobileMenuClicked",userSelected:"userSelected"}})}}return o})();function Bt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",8)(1,"app-select-option-control",9),e.bIt("valueChanged",function(i){e.eBV(n);const a=e.XpG(3);return e.Njj(a.selectUser(i))}),e.k0s()()}if(2&o){const n=e.XpG(3);e.R7$(),e.Y8G("floating",!0)("label","Results for")("placeHolder","Please select a user")("options",n.usersSelectOptions)("backgroundColor","#333333")("showLabel",!0)("textValue",n.selectedUserId)}}function kt(o,r){if(1&o&&(e.DNE(0,Bt,2,7,"div",8),e.nI1(1,"async")),2&o){let n;const t=e.XpG(2);e.vxM((n=e.bMT(1,1,t.users$))?0:-1,n)}}function Rt(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-button",11),e.bIt("click",function(){const i=e.eBV(n).$implicit,a=e.XpG(4);return e.Njj(a.openTagModal(i.tagTreeId,i.limitTo))}),e.EFF(1),e.k0s()}if(2&o){const n=r.$implicit;e.R7$(),e.JRh(n.label)}}function Vt(o,r){if(1&o&&e.Z7z(0,Rt,2,1,"ion-button",null,e.fX1),2&o){const n=e.XpG(3);e.Dyx(n.templateFields)}}function jt(o,r){if(1&o){const n=e.RV6();e.DNE(0,Vt,2,0),e.j41(1,"ion-searchbar",10),e.mxI("ngModelChange",function(i){e.eBV(n);const a=e.XpG(2);return e.DH7(a.searchValue,i)||(a.searchValue=i),e.Njj(i)}),e.bIt("ionChange",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.filterRows(i))}),e.k0s()}if(2&o){const n=e.XpG(2);e.vxM(n.templateFields&&n.templateFields.length>0?0:-1),e.R7$(),e.R50("ngModel",n.searchValue)}}function Xt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",7)(1,"ion-button",12),e.bIt("click",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.buttonClicked(i))}),e.EFF(2),e.k0s()()}if(2&o){const n=e.XpG(2);e.R7$(2),e.JRh(null==n.featureTab?null:n.featureTab.buttonText)}}function $t(o,r){if(1&o&&(e.j41(0,"div",1)(1,"ion-grid",2)(2,"ion-row",3)(3,"ion-col",4),e.DNE(4,kt,2,3)(5,jt,2,2,"ion-searchbar",5),e.k0s(),e.j41(6,"ion-col",6),e.DNE(7,Xt,3,1,"div",7),e.k0s()()()()),2&o){const n=e.XpG();e.AVh("page-margins-player",4===n.selectedItem)("page-margins",4!==n.selectedItem),e.R7$(4),e.vxM(!0===n.isEducator?4:-1),e.R7$(),e.vxM(!0!==n.isEducator&&n.selectedItem!==n.viewType.Player?5:-1),e.R7$(2),e.vxM(null!=n.featureTab&&n.featureTab.buttonText&&""!==n.featureTab.buttonText&&n.featureTab.featureTabButtons.length>0?7:-1)}}let Pe=(()=>{class o extends fe{constructor(n,t,i,a,s,c,p){super(n,t,i,a,s,c,p)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(q.n),e.rXU(ee.bZ),e.rXU(I.nX),e.rXU(x.Y),e.rXU(ne.O),e.rXU(w.P))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-view-options-row-lg"]],features:[e.Vt3],decls:1,vars:1,consts:[[1,"container",3,"page-margins-player","page-margins"],[1,"container"],[1,"grid-container"],[1,"view-options-row","ion-nowrap"],["size","7",1,"view-options-col"],["debounce","600","type","search","placeholder","Search this page",3,"ngModel"],["size","5",1,"view-options-col","view-options-col-right"],[1,"button-container"],[1,"user-search-custom"],[3,"valueChanged","floating","label","placeHolder","options","backgroundColor","showLabel","textValue"],["debounce","600","type","search","placeholder","Search this page",3,"ngModelChange","ionChange","ngModel"],[3,"click"],["size","small","fill","solid","color","primary",3,"click"]],template:function(t,i){1&t&&e.DNE(0,$t,8,7,"div",0),2&t&&e.vxM(i.hidden?-1:0)},dependencies:[ge.c,d.Jm,d.hU,d.lO,d.ln,d.S1,d.Gw,_.BC,_.vS,C.Jj],styles:[".container[_ngcontent-%COMP%]{display:flex;justify-content:center}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]{display:flex;flex-direction:row;padding:10px 8px 0 0;color:#fff;align-items:center;border-bottom:1px solid #333333;margin-bottom:10px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:flex-start;padding:0}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: #333333 !important;border-radius:5px;color:#fff;font-size:1em;text-transform:none;display:flex;justify-content:center;align-items:center;margin:0 .5em;height:42px;font-weight:400;margin-left:0!important}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   .button-text[_ngcontent-%COMP%]{font-weight:400;font-size:1em!important}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:focus{border:1px solid #f99e00}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]{--ion-background-color: #333333;--icon-color: white;--color: white;--box-shadow: 0;--border-radius: 5px;max-width:300px;margin-left:0!important;padding-left:0!important}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col-right[_ngcontent-%COMP%]{justify-content:flex-end}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col-right[_ngcontent-%COMP%]   .view-options[_ngcontent-%COMP%]{font-size:1.3em;display:flex;flex-direction:row;justify-content:center;align-items:center}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col-right[_ngcontent-%COMP%]   .view-options[_ngcontent-%COMP%]:hover{color:#f99e00;cursor:pointer}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col-right[_ngcontent-%COMP%]   .view-options-padding[_ngcontent-%COMP%]{padding-right:16px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col-right[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]{margin-left:16px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col-right[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:auto;float:right;padding-right:0;margin-right:0!important;height:42px;background:var(--ion-color-base);color:var(--ion-color-contrast);font-size:1.125em;font-weight:500}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:.875em;font-weight:500;padding-left:5px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{height:30px;width:30px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .active.active[_ngcontent-%COMP%]{color:#f99e00}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .text-description[_ngcontent-%COMP%]{margin-left:5px;font-size:1em}.container[_ngcontent-%COMP%]   .user-search-custom[_ngcontent-%COMP%]{width:300px;margin-top:-7px!important;height:62px}.page-margins[_ngcontent-%COMP%]{margin-left:var(--page-margin-left);margin-right:var(--page-margin-right)}.page-margins-player[_ngcontent-%COMP%]{padding-left:var(--page-margin-left-player);padding-right:var(--page-margin-right-player)}"]})}}return o})();function Gt(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-button",9),e.bIt("click",function(){const i=e.eBV(n).$implicit,a=e.XpG(4);return e.Njj(a.openTagModal(i.tagTreeId,i.limitTo))}),e.EFF(1),e.k0s()}if(2&o){const n=r.$implicit;e.R7$(),e.JRh(n.label)}}function Ut(o,r){if(1&o&&e.Z7z(0,Gt,2,1,"ion-button",null,e.fX1),2&o){const n=e.XpG(3);e.Dyx(n.templateFields)}}function Dt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",4),e.DNE(1,Ut,2,0),e.j41(2,"ion-searchbar",8),e.mxI("ngModelChange",function(i){e.eBV(n);const a=e.XpG(2);return e.DH7(a.searchValue,i)||(a.searchValue=i),e.Njj(i)}),e.bIt("ionChange",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.filterRows(i))}),e.k0s()()}if(2&o){const n=e.XpG(2);e.R7$(),e.vxM(n.templateFields&&n.templateFields.length>0?1:-1),e.R7$(),e.R50("ngModel",n.searchValue)}}function Et(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-button",10),e.bIt("click",function(){e.eBV(n);const i=e.XpG(2);return e.Njj(i.openMobileMenu())}),e.nrm(1,"ion-icon",11),e.k0s()}}function Nt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",7)(1,"ion-button",12),e.bIt("click",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.buttonClicked(i))}),e.EFF(2),e.k0s()()}if(2&o){const n=e.XpG(2);e.R7$(2),e.JRh(null==n.featureTab?null:n.featureTab.buttonText)}}function At(o,r){if(1&o&&(e.j41(0,"div",1)(1,"ion-grid",2)(2,"ion-row",3),e.DNE(3,Dt,3,2,"div",4),e.j41(4,"div")(5,"div",5),e.DNE(6,Et,2,0,"ion-button",6)(7,Nt,3,1,"div",7),e.k0s()()()()()),2&o){const n=e.XpG();e.AVh("page-margins-player",4===n.selectedItem)("page-margins",4!==n.selectedItem),e.R7$(3),e.vxM(n.mobileMenuClosed?-1:3),e.R7$(3),e.vxM(4===n.selectedItem?6:-1),e.R7$(),e.vxM(null!=n.featureTab&&n.featureTab.buttonText&&""!==n.featureTab.buttonText&&n.featureTab.featureTabButtons.length>0?7:-1)}}let be=(()=>{class o extends fe{constructor(n,t,i,a,s,c,p){super(n,t,i,a,s,c,p),this.mobileMenuClosed=!1}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(q.n),e.rXU(ee.bZ),e.rXU(I.nX),e.rXU(x.Y),e.rXU(ne.O),e.rXU(w.P))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-view-options-row-xs"]],inputs:{mobileMenuClosed:"mobileMenuClosed"},features:[e.Vt3],decls:1,vars:1,consts:[[1,"container",3,"page-margins-player","page-margins"],[1,"container"],[1,"grid-container"],[1,"view-options-row","ion-nowrap"],[1,"view-options-col"],[1,"main-btn-container"],[1,"menu-button"],[1,"btn-container"],["debounce","600","type","search","placeholder","Search",3,"ngModelChange","ionChange","ngModel"],[3,"click"],[1,"menu-button",3,"click"],["size","large","color","light","name","menu"],["size","small","fill","solid","color","primary",3,"click"]],template:function(t,i){1&t&&e.DNE(0,At,8,7,"div",0),2&t&&e.vxM(i.hidden?-1:0)},dependencies:[d.Jm,d.lO,d.iq,d.ln,d.S1,d.Gw,_.BC,_.vS],styles:[".container[_ngcontent-%COMP%]{display:flex}.container[_ngcontent-%COMP%]   .main-btn-container[_ngcontent-%COMP%]{justify-content:flex-end;width:100%;display:flex;align-items:center}.container[_ngcontent-%COMP%]   .main-btn-container[_ngcontent-%COMP%]   .btn-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{background:var(--ion-color-base);color:var(--ion-color-contrast);font-size:14px;font-weight:500}.container[_ngcontent-%COMP%]   .view-select[_ngcontent-%COMP%]{border:1px solid #444;border-radius:5px;justify-content:flex-end;height:42px;margin-right:1px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]{display:flex;flex-direction:row;padding:0;color:#fff;align-items:center;border-bottom:1px solid #333333;justify-content:space-between;width:100%}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--background: #333333 !important;border-radius:5px;color:#fff;height:42px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:flex-start;padding:0}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{border-radius:5px;font-size:1em;text-transform:none;display:flex;justify-content:center;align-items:center;margin:0 .5em;height:42px;font-weight:400;margin-left:0!important}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]:focus{border:1px solid #f99e00}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]{--ion-background-color: #333333;--icon-color: white;--color: white;--box-shadow: 0;--border-radius: 5px;width:70vw;max-width:300px;min-width:100px;margin-left:0!important;padding-left:0!important;padding-right:4px!important}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .view-options-row[_ngcontent-%COMP%]   .view-options-col[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]     .searchbar-input.sc-ion-searchbar-md{padding-inline-end:30px!important;padding-inline-start:40px!important}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .button-row[_ngcontent-%COMP%]{justify-content:flex-end;border:none!important}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{height:30px;width:30px}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .active.active[_ngcontent-%COMP%]{color:#f99e00}.container[_ngcontent-%COMP%]   .grid-container[_ngcontent-%COMP%]   .text-description[_ngcontent-%COMP%]{margin-left:5px;font-size:1em}.page-margins[_ngcontent-%COMP%]{margin-left:var(--page-margin-left);margin-right:var(--page-margin-right)}.page-margins-player[_ngcontent-%COMP%]{padding-left:var(--page-margin-left-player);padding-right:var(--page-margin-right-player)}"]})}}return o})();function zt(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-view-options-row-lg",2),e.bIt("optionSelected",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.optionSelectedChange(i))})("rowFiltered",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.rowFilteredChange(i))})("buttonChanged",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.buttonClicked(i))})("userSelected",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.selectUser(i))}),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("canAdd",n.canAdd)("addRoute",n.addRoute)("template",n.template)("featureTab",n.featureTab)("selectedItem",n.selectedItem)("instance",n.instance)("isEducator",n.isEducator)("selectedUserId",n.selectedUserId)}}function Lt(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-view-options-row-xs",3),e.bIt("optionSelected",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.optionSelectedChange(i))})("rowFiltered",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.rowFilteredChange(i))})("buttonChanged",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.buttonClicked(i))})("mobileMenuClicked",function(){e.eBV(n);const i=e.XpG();return e.Njj(i.openMobileMenu())}),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("canAdd",n.canAdd)("addRoute",n.addRoute)("template",n.template)("featureTab",n.featureTab)("selectedItem",n.selectedItem)("instance",n.instance)("mobileMenuClosed",n.mobileMenuClosed)}}let D=(()=>{class o{constructor(n){this.layoutService=n,this.selectedItem=1,this.mobileMenuClosed=!1,this.optionSelected=new e.bkB,this.rowFiltered=new e.bkB,this.buttonChanged=new e.bkB,this.mobileMenuClicked=new e.bkB,this.userSelected=new e.bkB}optionSelectedChange(n){this.optionSelected.emit(n)}rowFilteredChange(n){this.rowFiltered.emit(n)}buttonClicked(n){this.buttonChanged.emit(n)}openMobileMenu(){this.mobileMenuClicked.emit(!0)}selectUser(n){this.userSelected.emit(n)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(x.Y))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-view-options-row"]],inputs:{canAdd:"canAdd",addRoute:"addRoute",template:"template",featureTab:"featureTab",selectedItem:"selectedItem",mobileMenuClosed:"mobileMenuClosed",instance:"instance",isEducator:"isEducator",hasAdminAccess:"hasAdminAccess",hideAddButton:"hideAddButton",selectedUserId:"selectedUserId"},outputs:{optionSelected:"optionSelected",rowFiltered:"rowFiltered",buttonChanged:"buttonChanged",mobileMenuClicked:"mobileMenuClicked",userSelected:"userSelected"},decls:2,vars:2,consts:[[3,"canAdd","addRoute","template","featureTab","selectedItem","instance","isEducator","selectedUserId"],[3,"canAdd","addRoute","template","featureTab","selectedItem","instance","mobileMenuClosed"],[3,"optionSelected","rowFiltered","buttonChanged","userSelected","canAdd","addRoute","template","featureTab","selectedItem","instance","isEducator","selectedUserId"],[3,"optionSelected","rowFiltered","buttonChanged","mobileMenuClicked","canAdd","addRoute","template","featureTab","selectedItem","instance","mobileMenuClosed"]],template:function(t,i){1&t&&e.DNE(0,zt,1,8,"app-view-options-row-lg",0)(1,Lt,1,7,"app-view-options-row-xs",1),2&t&&(e.vxM("lg"===i.layoutService.currentScreenSize?0:-1),e.R7$(),e.vxM("xs"===i.layoutService.currentScreenSize?1:-1))},dependencies:[Pe,be]})}}return o})(),te=(()=>{class o{constructor(n,t,i,a,s,c,p){this.instanceService=n,this.eventService=t,this.rolesService=i,this.globalToastService=a,this.renderer=s,this.el=c,this.layoutService=p,this.rowFiltered=new e.bkB,this.buttonChanged=new e.bkB,this.optionSelected=new e.bkB,this.viewTypes=m.q,this.openMobileMenu=!1,this.componentDestroyed$=new y.B,this.isEducator=!1}ngOnInit(){this.instanceService.reload$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.getInstanceUserRoles()}),this.getInstanceUserRoles(),this.checkUserIsEducator(),this.toastSubscription=this.globalToastService.toastVisible$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{var t;(t=this.el.nativeElement.querySelector("xs"===this.layoutService.currentScreenSize?".player-outer-container":".player-view-info-container"))&&(n.visible?this.renderer.addClass(t,"completed-row-progress-toast"===n.toastType?"toast-visible-big":"toast-visible"):this.renderer.removeClass(t,"toast-visible"))})}ngAfterViewInit(){this.eventService.subscribe("section",n=>{this.scrollToElement(n)})}scrollToElement(n){const t=document.getElementById(n)?.offsetTop,i=document.getElementById("visualRow"),a=document.getElementById("scrollContent");a&&t&&i&&a.scrollTo(0,t+i.offsetHeight+i.offsetTop)}buttonClicked(n){this.buttonChanged.emit(n)}setSelectedViewType(n){this.optionSelected.emit(n)}setFinishedInstance(n){!0===n.shouldNavigate&&this.optionSelected.emit(1),this.status=n.status,this.containsGrading=n.containsGrading,this.isGraded=n.isGraded}rowFilteredChange(n){this.searchFilter=n,this.rowFiltered.emit(n)}getInstanceUserRoles(){this.instanceService.getInstanceUserRoles(this.routeParams?.instanceSlug??this.instance.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{this.instanceUserRoles=n})}setSelectedUserId(n){this.selectedUserId=n}checkUserIsEducator(){this.isEducator=(this.rolesService.hasRoleAccess([S.Q.Manage,S.Q.Publish])||!0===this.instance.isOwner)&&("Modifiable Learning Container Pages"===this.instance.feature.featureType.name||"Accredited Learning Container Pages"===this.instance.feature.featureType.name)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete(),this.eventService.unsubscribe("section")}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(M.b),e.rXU(F.s),e.rXU(w.P),e.rXU(X.A),e.rXU(e.sFG),e.rXU(e.aKT),e.rXU(x.Y))}}static{this.\u0275dir=e.FsC({type:o,inputs:{template:"template",instance:"instance",featureTab:"featureTab",routeParams:"routeParams",searchFilter:"searchFilter",selectedUserId:"selectedUserId"},outputs:{rowFiltered:"rowFiltered",buttonChanged:"buttonChanged",optionSelected:"optionSelected"}})}}return o})(),Me=(()=>{class o{constructor(n,t,i,a){this.dataService=n,this.alertService=t,this.instanceService=i,this.rolesService=a,this.isEducator=!1,this.finishedInstance=new e.bkB,this.componentDestroyed$=new y.B,this.expand=!1,this.showAddUserButton=!1}ngOnInit(){this.instanceService.reload$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.loadData()}),this.loadData()}findInstanceBadgeUser(n){this.dataService.findInstanceBadgeUser(n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{t||(this.showAddUserButton=!0)})}setFinishedInstance(n){this.finishedInstance.emit(n)}openBuilder(n){this.instanceService.openInstance("instance",n,null,"builder")}addInstanceBadgeUser(n){this.dataService.addInstanceBadgeUser(n.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{t&&this.instanceService.openInstance("instance",n.slug?n.slug:n.id,null,null),this.showAddUserButton=!1})}showAlert(n){this.alertService.presentAlert("",n)}loadData(){this.instance$=this.instanceService.getInstance(this.routeParams?.instanceSlug??this.routeParams.featureSlug).pipe((0,A.T)(n=>(n&&(this.iconUrl=n?.iconAssetId?`${B.c.contentUrl}asset/${n.iconAssetId}/content`:"assets/images/no-image.png",this.assetUrl=n?.coverMediaAssetId?`${B.c.contentUrl}asset/${n.coverMediaAssetId}/content`:"assets/images/no-image.png",this.rolesService.getProductFeatureRoles(n.feature.id,!0),this.getUserInstanceTracking(n),"Achievement Manager"===n.feature.featureType.name&&this.findInstanceBadgeUser(n.id)),n)))}getUserInstanceTracking(n){this.dataService.getUserInstanceTracking(n.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.userInstanceTracking=t})}hasAdminAccess(){return this.rolesService.hasRoleAccess([S.Q.Manage,S.Q.Publish])}hasEditAccess(n){const t=n.feature?.featureTabs?.flatMap(i=>i?.featureTabEditActions);return this.rolesService.hasRoleAccessPlayerView(t&&0!==t?.length?t.map(i=>i.actionBw):[S.Q.Publish])}noImage(){this.iconUrl="assets/images/no-image.png"}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(j.u),e.rXU(M.b),e.rXU(w.P))}}static{this.\u0275dir=e.FsC({type:o,inputs:{routeParams:"routeParams",selectedUserId:"selectedUserId",isEducator:"isEducator"},outputs:{finishedInstance:"finishedInstance"}})}}return o})();var Oe=l(23848);function Yt(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-button",6),e.bIt("click",function(){e.eBV(n);const i=e.XpG(),a=e.XpG();return e.Njj(a.addInstanceBadgeUser(i))}),e.EFF(1,"Start"),e.k0s()}}function Qt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",3)(1,"ion-button",7),e.bIt("click",function(){e.eBV(n);const i=e.XpG(),a=e.XpG();return e.Njj(a.openBuilder(i.id))}),e.nrm(2,"ion-icon",8),e.EFF(3," Edit "),e.k0s()()}}function Ht(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-instance-sections-and-components",9),e.bIt("finishedInstance",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.setFinishedInstance(i))}),e.k0s()}if(2&o){e.XpG();const n=e.sdS(1),t=e.XpG();e.Y8G("containerTop",n.getBoundingClientRect().top)("routeParams",t.routeParams)("showInstanceHeader",!1)("isViewField",!0)("searchFilter",t.searchFilter)("selectedUserId",t.selectedUserId)("isEducator",t.isEducator)}}function Wt(o,r){if(1&o&&(e.j41(0,"div",1,0),e.DNE(2,Yt,2,0,"ion-button",2)(3,Qt,4,0,"div",3),e.j41(4,"div",4),e.DNE(5,Ht,1,7,"app-instance-sections-and-components",5),e.k0s()()),2&o){const n=r,t=e.XpG();e.R7$(2),e.vxM("Achievement Manager"===n.feature.featureType.name&&t.showAddUserButton?2:-1),e.R7$(),e.vxM(t.hasEditAccess(n)?3:-1),e.R7$(2),e.vxM(t.routeParams&&t.routeParams.instanceSlug&&"default"!==t.routeParams.instanceSlug?5:-1)}}let ye=(()=>{class o extends Me{constructor(n,t,i,a){super(n,t,i,a)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(j.u),e.rXU(M.b),e.rXU(w.P))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-player-view-information-xs"]],inputs:{searchFilter:"searchFilter"},features:[e.Vt3],decls:2,vars:3,consts:[["container",""],[1,"info-container"],["fill","solid","color","primary",1,"add-badge-user-button"],[1,"edit-button"],[1,"content"],[3,"containerTop","routeParams","showInstanceHeader","isViewField","searchFilter","selectedUserId","isEducator"],["fill","solid","color","primary",1,"add-badge-user-button",3,"click"],["fill","clear",1,"inner-container",3,"click"],["name","pencil"],[3,"finishedInstance","containerTop","routeParams","showInstanceHeader","isViewField","searchFilter","selectedUserId","isEducator"]],template:function(t,i){if(1&t&&(e.DNE(0,Wt,6,3,"div",1),e.nI1(1,"async")),2&t){let a;e.vxM((a=e.bMT(1,1,i.instance$))?0:-1,a)}},dependencies:[d.Jm,d.iq,Oe.a,C.Jj],styles:[".info-container[_ngcontent-%COMP%]{position:relative;height:100%;display:flex;padding:0}.info-container[_ngcontent-%COMP%]   .add-badge-user-button[_ngcontent-%COMP%]{position:absolute;right:15px;top:15px;z-index:9999;font-weight:700}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]{position:absolute;right:10px;top:10px;z-index:9999}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{margin:0;color:#000;text-transform:none;border-radius:3px;font-weight:500;font-size:18px;background-color:#f99e00}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:18px;height:18px;padding-left:10px;margin-right:10px;color:#000}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{min-height:36px;--padding-top: 0px;--padding-bottom: 0px}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   ion-button.inner-container[_ngcontent-%COMP%]::part(native){line-height:normal;--padding-start: 0 !important;--padding-end: 10px !important}.info-container[_ngcontent-%COMP%]     .text-value{font-size:1.25em;font-weight:400;color:#ccc;line-height:1.4}.info-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{position:absolute;padding:8px;width:100%;background-image:linear-gradient(to bottom,transparent 60%,#232323),var(--background-image);background-size:cover;z-index:999;border-top-left-radius:13px;border-top-right-radius:13px;height:140px}.info-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{align-self:flex-end;z-index:1000;width:100%;border-radius:5px;height:100%}.info-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:ng-deep   .icon-inner[_ngcontent-%COMP%]{margin-top:3px}.info-container   [_nghost-%COMP%]     div.parent-container{margin-top:0;margin-right:10px;border:0px}"]})}}return o})();function Jt(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-button",6),e.bIt("click",function(){e.eBV(n);const i=e.XpG(),a=e.XpG();return e.Njj(a.addInstanceBadgeUser(i))}),e.EFF(1,"Start"),e.k0s()}}function Zt(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",3)(1,"ion-button",7),e.bIt("click",function(){e.eBV(n);const i=e.XpG(),a=e.XpG();return e.Njj(a.openBuilder(i.id))}),e.nrm(2,"ion-icon",8),e.EFF(3," Edit "),e.k0s()()}}function Kt(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-instance-sections-and-components",9),e.bIt("finishedInstance",function(i){e.eBV(n);const a=e.XpG(2);return e.Njj(a.setFinishedInstance(i))}),e.k0s()}if(2&o){e.XpG();const n=e.sdS(1),t=e.XpG();e.Y8G("containerTop",n.getBoundingClientRect().top)("routeParams",t.routeParams)("showInstanceHeader",!1)("isViewField",!0)("searchFilter",t.searchFilter)("selectedUserId",t.selectedUserId)("isEducator",t.isEducator)}}function qt(o,r){if(1&o&&(e.j41(0,"div",1,0),e.DNE(2,Jt,2,0,"ion-button",2)(3,Zt,4,0,"div",3),e.j41(4,"div",4),e.DNE(5,Kt,1,7,"app-instance-sections-and-components",5),e.k0s()()),2&o){const n=r,t=e.XpG();e.R7$(2),e.vxM("Achievement Manager"===(null==n.feature||null==n.feature.featureType?null:n.feature.featureType.name)&&t.showAddUserButton?2:-1),e.R7$(),e.vxM(t.hasEditAccess(n)?3:-1),e.R7$(2),e.vxM(t.routeParams&&t.routeParams.instanceSlug&&"default"!==t.routeParams.instanceSlug?5:-1)}}let we=(()=>{class o extends Me{constructor(n,t,i,a){super(n,t,i,a)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(j.u),e.rXU(M.b),e.rXU(w.P))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-player-view-information-lg"]],inputs:{searchFilter:"searchFilter"},features:[e.Vt3],decls:2,vars:3,consts:[["container",""],[1,"info-container","ion-padding"],["fill","solid","color","primary",1,"add-badge-user-button"],[1,"edit-button"],[1,"content"],[3,"containerTop","routeParams","showInstanceHeader","isViewField","searchFilter","selectedUserId","isEducator"],["fill","solid","color","primary",1,"add-badge-user-button",3,"click"],["fill","clear",1,"inner-container",3,"click"],["name","pencil"],[3,"finishedInstance","containerTop","routeParams","showInstanceHeader","isViewField","searchFilter","selectedUserId","isEducator"]],template:function(t,i){if(1&t&&(e.DNE(0,qt,6,3,"div",1),e.nI1(1,"async")),2&t){let a;e.vxM((a=e.bMT(1,1,i.instance$))?0:-1,a)}},dependencies:[d.Jm,d.iq,Oe.a,C.Jj],styles:[".info-container[_ngcontent-%COMP%]{position:relative;height:100%;display:flex;padding:0}.info-container[_ngcontent-%COMP%]   .add-badge-user-button[_ngcontent-%COMP%]{position:absolute;right:25px;top:40px;z-index:9999;font-weight:700}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]{position:absolute;right:10px;top:10px;z-index:9999}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{margin:0;color:#000;text-transform:none;border-radius:3px;font-weight:500;font-size:18px;background-color:#f99e00}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:18px;height:18px;padding-left:10px;margin-right:10px;color:#000}.info-container[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   ion-button.inner-container[_ngcontent-%COMP%]::part(native){line-height:normal;--padding-start: 0 !important;--padding-end: 10px !important}.info-container[_ngcontent-%COMP%]     .text-value{font-size:1.25em;font-weight:400;color:#ccc;line-height:1.4}.info-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{position:absolute;padding:8px;width:100%;background-image:linear-gradient(to bottom,transparent 60%,#232323),var(--background-image);background-size:cover;z-index:999;border-top-left-radius:13px;border-top-right-radius:13px;height:140px}.info-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{align-self:flex-end;z-index:1000;width:100%;height:100%}.info-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .visual-row[_ngcontent-%COMP%]{margin-top:90px;display:flex;height:auto;align-items:flex-start;width:100%}.info-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:ng-deep   .icon-inner[_ngcontent-%COMP%]{margin-top:3px}.info-container   [_nghost-%COMP%]     div.parent-container{margin-top:0;margin-right:10px;border:0px}.info-container[_ngcontent-%COMP%]   .image-header-gradient[_ngcontent-%COMP%]{width:100%;background-image:linear-gradient(to bottom,#1e1e1e59 25%,#1e1e1e 75%),var(--background-image);background-size:cover;border-radius:11px;height:180px;top:0;left:0;position:absolute}"]})}}return o})();function ei(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-player-view-information-xs",1),e.bIt("finishedInstance",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.setFinishedInstance(i))}),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("selectedUserId",n.selectedUserId)("routeParams",n.routeParams)("searchFilter",n.searchFilter)("isEducator",n.isEducator)}}function ni(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-player-view-information-lg",1),e.bIt("finishedInstance",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.setFinishedInstance(i))}),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("selectedUserId",n.selectedUserId)("routeParams",n.routeParams)("searchFilter",n.searchFilter)("isEducator",n.isEducator)}}let z=(()=>{class o{constructor(n){this.layoutService=n,this.isEducator=!1,this.finishedInstance=new e.bkB}setFinishedInstance(n){this.finishedInstance.emit(n)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(x.Y))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-player-view-information"]],inputs:{routeParams:"routeParams",searchFilter:"searchFilter",selectedUserId:"selectedUserId",isEducator:"isEducator"},outputs:{finishedInstance:"finishedInstance"},decls:2,vars:2,consts:[[3,"selectedUserId","routeParams","searchFilter","isEducator"],[3,"finishedInstance","selectedUserId","routeParams","searchFilter","isEducator"]],template:function(t,i){1&t&&e.DNE(0,ei,1,4,"app-player-view-information-xs",0)(1,ni,1,4,"app-player-view-information-lg",0),2&t&&(e.vxM("xs"===i.layoutService.currentScreenSize?0:-1),e.R7$(),e.vxM("lg"===i.layoutService.currentScreenSize?1:-1))},dependencies:[ye,we]})}}return o})();function ti(o,r){if(1&o&&(e.j41(0,"h4",0),e.EFF(1),e.k0s()),2&o){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",null==n||null==n.section?null:n.section.title," ")}}function ii(o,r){if(1&o){const n=e.RV6();e.j41(0,"ion-col",1)(1,"app-component-row-selector",2),e.bIt("rowContentSelected",function(i){e.eBV(n);const a=e.XpG(3);return e.Njj(a.selectedRowContent(i))}),e.k0s()()}if(2&o){const n=r.$implicit,t=e.XpG(3);e.R7$(),e.Y8G("routeParams",t.routeParams)("instanceSectionComponent",n)("instance",t.instance)("searchFilter",t.searchFilter)("isPlayerSidePanel",!0)("selectedUserId",t.selectedUserId)("onlyRows",!0)("playerSidePanel",!0)}}function oi(o,r){if(1&o&&(e.j41(0,"ion-row"),e.Z7z(1,ii,2,8,"ion-col",1,e.fX1),e.nI1(3,"orderBy"),e.k0s()),2&o){const n=r.$implicit;e.R7$(),e.Dyx(e.i5U(3,0,n,"component.templateField.colNumber"))}}function ai(o,r){if(1&o&&(e.j41(0,"div"),e.DNE(1,ti,2,1,"h4",0),e.j41(2,"div")(3,"ion-grid"),e.Z7z(4,oi,4,3,"ion-row",null,e.fX1),e.nI1(6,"groupBy"),e.nI1(7,"values"),e.k0s()()()),2&o){const n=r.$implicit;e.R7$(),e.vxM(null!=n&&null!=n.section&&n.section.title&&(null==n||null==n.section||null==n.section.title?null:n.section.title.length)>0&&null!=n&&null!=n.section&&n.section.showTitleOnPlayer?1:-1),e.R7$(3),e.Dyx(e.bMT(7,4,e.i5U(6,1,n.instanceSectionComponents,"component.rowNumber")))}}let ie=(()=>{class o{constructor(n,t){this.eventsService=n,this.instanceService=t,this.isLastContentItem=!1}ngOnInit(){this.eventsService.subscribe("isLastRowContentItem",n=>{this.isLastContentItem=n})}ngAfterViewInit(){this.eventsService.subscribe("playerRowCompleted",n=>{const t=this.GetNextSection(n);if(t){const i=t.instanceSectionComponents.findIndex(a=>null!==a.component.rowId||a.instanceSectionComponentRows.length>0);if(-1!==i){let a=t.instanceSectionComponents[i].component.rowId;a||(a=t.instanceSectionComponents[i].instanceSectionComponentRows[0].rowId),this.eventsService.publish("playerRowSelected",a)}}else{const i=this.instanceService.getPrevInstanceSlug();if(i&&i.length>0)return this.routeParams.instanceSlug=i?.length>0?i:this.routeParams.instanceSlug,void this.instanceService.openInstance(this.routeParams.featureSlug,i?.length>0?i:this.routeParams.instanceSlug,this.routeParams.tabName,"grid");this.eventsService.publish("changeViewType",1)}})}selectedRowContent(n){const t=this.GetNextSection(n);(!t||0===t?.instanceSectionComponents?.length)&&this.isLastContentItem&&this.eventsService.publish("isLastSection")}GetNextSection(n){let t=this.template.instanceSections.findIndex(i=>i.instanceSectionComponents.some(a=>a.component.rowId===n));return-1===t&&(t=this.template.instanceSections.findIndex(i=>i.instanceSectionComponents.some(a=>a.instanceSectionComponentRows.some(s=>s.rowId===n)))),this.template.instanceSections.filter(i=>!0===i.section.showOnPlayerSidepanel)[t+1]}ngOnChanges(n){n.template&&(this.instanceSections=this.template.instanceSections.filter(t=>!1!==t.section.showOnPlayerSidepanel&&!(t.section.typeBw===O.F.Dynamic&&t.section.templateId)))}ngOnDestroy(){this.eventsService.unsubscribe("playerRowCompleted"),this.eventsService.unsubscribe("isLastRowContentItem")}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(F.s),e.rXU(M.b))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-player-view-side-panel"]],inputs:{template:"template",routeParams:"routeParams",instance:"instance",searchFilter:"searchFilter",selectedUserId:"selectedUserId"},features:[e.OA$],decls:2,vars:0,consts:[[1,"side-panel-title"],["size","12"],[3,"rowContentSelected","routeParams","instanceSectionComponent","instance","searchFilter","isPlayerSidePanel","selectedUserId","onlyRows","playerSidePanel"]],template:function(t,i){1&t&&e.Z7z(0,ai,8,6,"div",null,e.fX1),2&t&&e.Dyx(i.instanceSections)},dependencies:[d.hU,d.lO,d.ln,W.A,T.lU,T.Qe,T.Op],styles:[".side-panel-title[_ngcontent-%COMP%]{margin:0 16px;padding-bottom:7px;padding-top:20px;font-weight:700;font-size:1.25em;line-height:1.1;letter-spacing:.01em;word-wrap:break-word;font-family:roboto;font-style:italic}"]})}}return o})();const ve=o=>({big:o});function si(o,r){if(1&o){const n=e.RV6();e.j41(0,"div",10)(1,"div",11)(2,"div")(3,"app-player-view-information",12),e.bIt("finishedInstance",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.setFinishedInstance(i))}),e.k0s()()()()}if(2&o){const n=e.XpG();e.R7$(),e.AVh("player-outer-container",n.routeParams.viewType===n.viewTypes.Player),e.Y8G("ngClass",e.eq3(7,ve,!1===n.searchBarAvailable)),e.R7$(),e.AVh("player-container",n.routeParams.viewType===n.viewTypes.Player),e.R7$(),e.Y8G("routeParams",n.routeParams)("searchFilter",n.searchFilter)}}let xe=(()=>{class o extends te{constructor(n,t,i,a,s,c,p){super(n,t,i,a,s,c,p),this.searchBarAvailable=!0}ngAfterContentInit(){const n=document.querySelector(".view-options-row");this.searchBarAvailable=null!=n}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(M.b),e.rXU(F.s),e.rXU(w.P),e.rXU(X.A),e.rXU(e.sFG),e.rXU(e.aKT),e.rXU(x.Y))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-player-view-xs"]],features:[e.Vt3],decls:11,vars:14,consts:[["sidenav",""],[1,"parent-container"],["slot","fixed",1,"top-border"],[3,"rowFiltered","buttonChanged","optionSelected","mobileMenuClicked","template","canAdd","selectedItem","featureTab","instance"],[1,"player-row-flex",3,"ngClass"],["position","end","mode","side",3,"openedChange","opened"],[1,"player-side-panel-container"],[3,"template","routeParams","searchFilter","instance"],[1,"player-width",3,"click"],[1,"player-inner-container"],[1,"content-wrapper","player-view-info-container"],[3,"ngClass"],[3,"finishedInstance","routeParams","searchFilter"]],template:function(t,i){if(1&t){const a=e.RV6();e.j41(0,"div",1)(1,"div",2)(2,"app-view-options-row",3),e.bIt("rowFiltered",function(c){return e.eBV(a),e.Njj(i.rowFilteredChange(c))})("buttonChanged",function(c){return e.eBV(a),e.Njj(i.buttonClicked(c))})("optionSelected",function(c){return e.eBV(a),e.Njj(i.setSelectedViewType(c))})("mobileMenuClicked",function(){return e.eBV(a),e.Njj(i.openMobileMenu=!i.openMobileMenu)}),e.k0s()(),e.j41(3,"mat-sidenav-container",4)(4,"mat-sidenav",5,0),e.mxI("openedChange",function(c){return e.eBV(a),e.DH7(i.openMobileMenu,c)||(i.openMobileMenu=c),e.Njj(c)}),e.j41(6,"div",6),e.nrm(7,"app-player-view-side-panel",7),e.k0s()(),e.j41(8,"mat-sidenav-content",8),e.bIt("click",function(){e.eBV(a);const c=e.sdS(5);return e.Njj(i.openMobileMenu?c.toggle():null)}),e.j41(9,"div",9),e.DNE(10,si,4,9,"div",10),e.k0s()()()()}if(2&t){let a;e.R7$(2),e.Y8G("template",i.template)("canAdd","Admin"===(null==i.instance?null:i.instance.title))("selectedItem",null!==(a=null==i.routeParams?null:i.routeParams.viewType)&&void 0!==a?a:0)("featureTab",i.featureTab)("instance",i.instance),e.R7$(),e.Y8G("ngClass",e.eq3(12,ve,!1===i.searchBarAvailable)),e.R7$(),e.R50("opened",i.openMobileMenu),e.R7$(3),e.Y8G("template",i.template)("routeParams",i.routeParams)("searchFilter",i.searchFilter)("instance",i.instance),e.R7$(3),e.vxM(i.openMobileMenu?-1:10)}},dependencies:[C.YU,V.LG,V.US,V.El,D,z,ie],styles:[".parent-container[_ngcontent-%COMP%]{height:100%}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]{display:flex;flex-direction:row;background-color:transparent;height:calc(100vh - 280px);overflow:hidden}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   mat-sidenav[_ngcontent-%COMP%]{width:80%;background-color:#292929;overflow-y:auto;height:calc(100vh - 270px)}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   mat-sidenav[_ngcontent-%COMP%]   .player-side-panel-container[_ngcontent-%COMP%]{background-color:#292929}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]{width:100%;overflow:hidden;position:relative}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]{width:100%;float:left;height:100%}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{height:100%;overflow:auto}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-view-info-container[_ngcontent-%COMP%]{width:100%;height:100%;margin-top:5px}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]{margin-right:var(--page-margin-right-player);margin-left:var(--page-margin-left-player);height:calc(100vh - 300px)}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]   .player-container[_ngcontent-%COMP%]{border:2px solid rgba(249,158,0,.4);background-color:#232323;border-radius:13px 13px 15px 15px;margin-bottom:20px;overflow:hidden;height:100%}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .big[_ngcontent-%COMP%]{height:calc(100vh - 230px)}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .toast-visible[_ngcontent-%COMP%]{height:calc(100vh - 315px)}.parent-container[_ngcontent-%COMP%]   .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .toast-visible-big[_ngcontent-%COMP%]{height:calc(100vh - 335px)}.parent-container[_ngcontent-%COMP%]   .big[_ngcontent-%COMP%]{height:calc(100vh - 210px)}"]})}}return o})();const ri=o=>({"custom-info-height":o}),ci=o=>({"player-outer-container":o}),li=(o,r,n,t)=>({"player-container":o,"player-container-completed":r,"player-container-in-progress":n,"player-container-incomplete":t});function di(o,r){1&o&&(e.j41(0,"span",12),e.EFF(1,"GRADED"),e.k0s())}function pi(o,r){1&o&&(e.j41(0,"span",13),e.EFF(1,"STUDENT IN PROGRESS"),e.k0s())}function ui(o,r){1&o&&(e.j41(0,"span",13),e.EFF(1,"STUDENT NOT STARTED"),e.k0s())}function _i(o,r){1&o&&(e.j41(0,"span",14),e.EFF(1,"UNGRADED"),e.k0s())}function mi(o,r){if(1&o&&(e.j41(0,"div",8)(1,"ion-row")(2,"ion-col"),e.DNE(3,di,2,0,"span",12)(4,pi,2,0,"span",13)(5,ui,2,0,"span",13)(6,_i,2,0,"span",14),e.k0s()()()),2&o){const n=e.XpG();e.R7$(3),e.vxM("Completed"===n.status&&!0===n.isGraded&&!0===n.containsGrading?3:-1),e.R7$(),e.vxM("InProgress"===n.status?4:-1),e.R7$(),e.vxM("NotStarted"===n.status?5:-1),e.R7$(),e.vxM("Completed"===n.status&&!1===n.isGraded&&!0===n.containsGrading?6:-1)}}let Se=(()=>{class o extends te{constructor(n,t,i,a,s,c,p){super(n,t,i,a,s,c,p)}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(M.b),e.rXU(F.s),e.rXU(w.P),e.rXU(X.A),e.rXU(e.sFG),e.rXU(e.aKT),e.rXU(x.Y))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-player-view-lg"]],features:[e.Vt3],decls:12,vars:29,consts:[[1,"player-row-flex"],[1,"player-width"],[1,"player-inner-container"],["slot","fixed",1,"top-border"],[3,"rowFiltered","buttonChanged","optionSelected","mobileMenuClicked","userSelected","template","canAdd","selectedItem","featureTab","isEducator","instance","selectedUserId"],[1,"content-wrapper","player-view-info-container",3,"ngClass"],["id","scrollContent",3,"ngClass"],[1,"full-parent-height","player-border",3,"ngClass"],[1,"completed-header-container"],[3,"finishedInstance","routeParams","searchFilter","selectedUserId","isEducator"],[1,"player-side-panel-container"],[3,"template","routeParams","searchFilter","instance","selectedUserId"],[1,"inner-completed"],[1,"inner-in-progress"],[1,"inner-incomplete"]],template:function(t,i){if(1&t&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"app-view-options-row",4),e.bIt("rowFiltered",function(s){return i.rowFilteredChange(s)})("buttonChanged",function(s){return i.buttonClicked(s)})("optionSelected",function(s){return i.setSelectedViewType(s)})("mobileMenuClicked",function(){return i.openMobileMenu=!i.openMobileMenu})("userSelected",function(s){return i.setSelectedUserId(s)}),e.k0s()(),e.j41(5,"div",5)(6,"div",6)(7,"div",7),e.DNE(8,mi,7,4,"div",8),e.j41(9,"app-player-view-information",9),e.bIt("finishedInstance",function(s){return i.setFinishedInstance(s)}),e.k0s()()()()()(),e.j41(10,"div",10),e.nrm(11,"app-player-view-side-panel",11),e.k0s()()),2&t){let a;e.R7$(4),e.Y8G("template",i.template)("canAdd","Admin"===(null==i.instance?null:i.instance.title))("selectedItem",null!==(a=null==i.routeParams?null:i.routeParams.viewType)&&void 0!==a?a:0)("featureTab",i.featureTab)("isEducator",i.isEducator)("instance",i.instance)("selectedUserId",i.selectedUserId),e.R7$(),e.Y8G("ngClass",e.eq3(20,ri,!0===i.isEducator||(null==i.featureTab?null:i.featureTab.buttonText)&&""!==i.featureTab.buttonText&&i.featureTab.featureTabButtons.length>0)),e.R7$(),e.Y8G("ngClass",e.eq3(22,ci,i.routeParams.viewType===i.viewTypes.Player)),e.R7$(),e.Y8G("ngClass",e.ziG(24,li,!(i.routeParams.viewType!==i.viewTypes.Player||i.status&&0!==(null==i.status?null:i.status.length)&&i.selectedUserId),i.routeParams.viewType===i.viewTypes.Player&&"Completed"===i.status&&i.selectedUserId&&!0===i.isGraded&&!0===i.containsGrading,i.routeParams.viewType===i.viewTypes.Player&&("InProgress"===i.status||"NotStarted"===i.status)&&i.selectedUserId,i.routeParams.viewType===i.viewTypes.Player&&"Completed"===i.status&&i.selectedUserId&&!0===i.containsGrading&&!1===i.isGraded)),e.R7$(),e.vxM(i.selectedUserId?8:-1),e.R7$(),e.Y8G("routeParams",i.routeParams)("searchFilter",i.searchFilter)("selectedUserId",i.selectedUserId)("isEducator",i.isEducator),e.R7$(2),e.Y8G("template",i.template)("routeParams",i.routeParams)("searchFilter",i.searchFilter)("instance",i.instance)("selectedUserId",i.selectedUserId)}},dependencies:[C.YU,d.hU,d.ln,D,z,ie],styles:[".player-row-flex[_ngcontent-%COMP%]{display:flex;flex-direction:row;height:100%}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]{width:100%;position:relative}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]{width:100%;float:left;height:100%}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .top-border[_ngcontent-%COMP%]{border-color:#171717;border-width:1px;border-style:solid;width:100%}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .content-wrapper[_ngcontent-%COMP%]{height:100%}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-view-info-container[_ngcontent-%COMP%]{width:100%;overflow-y:scroll;height:calc(100vh - 175px)}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .toast-visible[_ngcontent-%COMP%], .player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .toast-visible-big[_ngcontent-%COMP%]{padding-bottom:60px}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .custom-info-height[_ngcontent-%COMP%]{height:calc(100vh - 230px)!important}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]{margin:auto;padding-left:3.5vw;padding-right:2.5vw;height:100%;overflow:hidden}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]   .player-border[_ngcontent-%COMP%]{background-color:#232323;border-radius:13px 13px 15px 15px;margin-bottom:20px;overflow:hidden}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]   .player-container[_ngcontent-%COMP%]{border:2px solid rgba(249,158,0,.4)}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]   .player-container-completed[_ngcontent-%COMP%]{border:2px solid green;margin-top:10px}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]   .player-container-in-progress[_ngcontent-%COMP%]{border:2px solid #606060;margin-top:10px}.player-row-flex[_ngcontent-%COMP%]   .player-width[_ngcontent-%COMP%]   .player-inner-container[_ngcontent-%COMP%]   .player-outer-container[_ngcontent-%COMP%]   .player-container-incomplete[_ngcontent-%COMP%]{border:2px solid #5f2524;margin-top:10px}.player-row-flex[_ngcontent-%COMP%]   .completed-header-container[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{z-index:1006;position:absolute;top:0;left:calc(50% - 36px)}.player-row-flex[_ngcontent-%COMP%]   .completed-header-container[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .inner-completed[_ngcontent-%COMP%]{background-color:green;padding:2px;font-size:12px;border-radius:5px;color:#fff;margin-right:16px}.player-row-flex[_ngcontent-%COMP%]   .completed-header-container[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .inner-in-progress[_ngcontent-%COMP%]{background-color:#ac7f00;padding:2px;font-size:12px;border-radius:5px;color:#fff;margin-right:16px}.player-row-flex[_ngcontent-%COMP%]   .completed-header-container[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   .inner-incomplete[_ngcontent-%COMP%]{background-color:red;padding:2px;font-size:12px;border-radius:5px;color:#fff;margin-right:16px}.player-row-flex[_ngcontent-%COMP%]   .player-side-panel-container[_ngcontent-%COMP%]{overflow-y:auto;height:calc(100vh - 150px);width:25%;background-color:#292929}"]})}}return o})();function gi(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-player-view-lg",2),e.bIt("rowFiltered",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.rowFiltered(i))})("buttonChanged",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.buttonClicked(i))})("optionSelected",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.setSelectedViewType(i))}),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("template",n.instanceTemplate)("instance",n.instance)("featureTab",n.featureTab)("routeParams",n.routeParams)("searchFilter",n.searchFilter)("selectedUserId",n.selectedUserId)}}function hi(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-player-view-xs",3),e.bIt("rowFiltered",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.rowFiltered(i))})("buttonChanged",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.buttonClicked(i))})("optionSelected",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.setSelectedViewType(i))}),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("template",n.instanceTemplate)("instance",n.instance)("featureTab",n.featureTab)("routeParams",n.routeParams)("searchFilter",n.searchFilter)}}let Ie=(()=>{class o{constructor(n,t){this.layoutService=n,this.eventService=t,this.isScorm=!1,this.buttonChanged=new e.bkB,this.optionSelected=new e.bkB,this.hidden=!1}ngOnInit(){this.checkHidden()}buttonClicked(n){this.buttonChanged.emit(n)}setSelectedViewType(n){this.optionSelected.emit(n)}rowFiltered(n){this.searchFilter=n}checkHidden(){this.hidden=this.instanceTemplate.instanceSections.some(n=>n.section.components?.some(t=>"People Table"===t.componentType.name))}ngOnDestroy(){this.eventService.unsubscribe("checkCompletion"),this.eventService.unsubscribe("instanceCompleted"),this.eventService.unsubscribe("playerRowSelected"),this.eventService.unsubscribe("instanceSectionComponentCompleted"),this.eventService.unsubscribe("scrollToIndex"),this.eventService.unsubscribe("instanceIsGraded"),this.eventService.unsubscribe("isLastSection")}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(x.Y),e.rXU(F.s))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-player-view"]],inputs:{instanceTemplate:"instanceTemplate",instance:"instance",featureTab:"featureTab",routeParams:"routeParams",isScorm:"isScorm",searchFilter:"searchFilter",selectedUserId:"selectedUserId"},outputs:{buttonChanged:"buttonChanged",optionSelected:"optionSelected"},decls:2,vars:2,consts:[[3,"template","instance","featureTab","routeParams","searchFilter","selectedUserId"],[3,"template","instance","featureTab","routeParams","searchFilter"],[3,"rowFiltered","buttonChanged","optionSelected","template","instance","featureTab","routeParams","searchFilter","selectedUserId"],[3,"rowFiltered","buttonChanged","optionSelected","template","instance","featureTab","routeParams","searchFilter"]],template:function(t,i){1&t&&e.DNE(0,gi,1,6,"app-player-view-lg",0)(1,hi,1,5,"app-player-view-xs",1),2&t&&(e.vxM("lg"!==i.layoutService.currentScreenSize||i.isScorm||i.hidden?-1:0),e.R7$(),e.vxM("xs"!==i.layoutService.currentScreenSize||i.isScorm||i.hidden?-1:1))},dependencies:[xe,Se]})}}return o})();var Ci=l(37437);let Te=(()=>{class o{constructor(){this.componentDestroyed$=new y.B,this.environment=B.c,this.year=(0,Ci.A)(new Date,"yyyy")}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-link-footer"]],decls:41,vars:15,consts:[[1,"link-parent-container"],["size","12","sizeSm","4",1,"ion-align-self-start"],["color","light","expand","full","fill","clear",3,"href"],["align","middle"]],template:function(t,i){1&t&&(e.j41(0,"ion-grid",0)(1,"ion-row")(2,"ion-col",1)(3,"h5"),e.EFF(4,"PRODUCT"),e.k0s(),e.j41(5,"ion-button",2),e.EFF(6,"Help Desk"),e.k0s(),e.j41(7,"ion-button",2),e.EFF(8,"Tools and Pricing"),e.k0s(),e.j41(9,"ion-button",2),e.EFF(10,"Webinars"),e.k0s(),e.j41(11,"ion-button",2),e.EFF(12,"Events"),e.k0s(),e.j41(13,"ion-button",2),e.EFF(14,"Casting Call"),e.k0s()(),e.j41(15,"ion-col",1)(16,"h5"),e.EFF(17,"COMPANY"),e.k0s(),e.j41(18,"ion-button",2),e.EFF(19,"News"),e.k0s(),e.j41(20,"ion-button",2),e.EFF(21,"About Us"),e.k0s(),e.j41(22,"ion-button",2),e.EFF(23,"Meet The Team"),e.k0s(),e.j41(24,"ion-button",2),e.EFF(25,"Contact Us"),e.k0s()(),e.j41(26,"ion-col",1)(27,"h5"),e.EFF(28,"LEGALS"),e.k0s(),e.j41(29,"ion-button",2),e.EFF(30,"Terms of Use"),e.k0s(),e.j41(31,"ion-button",2),e.EFF(32,"Privacy Policy"),e.k0s(),e.j41(33,"ion-button",2),e.EFF(34,"Data Processing"),e.k0s(),e.j41(35,"ion-button",2),e.EFF(36,"Privacy Whitepaper"),e.k0s()()(),e.j41(37,"ion-row")(38,"ion-col")(39,"ion-footer",3),e.EFF(40),e.k0s()()()()),2&t&&(e.R7$(5),e.Y8G("href","/help"),e.R7$(2),e.Y8G("href","https://offers.edgefactor.com/tools-and-pricing"),e.R7$(2),e.Y8G("href","https://offers.edgefactor.com/webinars"),e.R7$(2),e.Y8G("href","https://offers.edgefactor.com/events"),e.R7$(2),e.Y8G("href","https://offers.edgefactor.com/casting-call"),e.R7$(5),e.Y8G("href","https://blog.edgefactor.com/"),e.R7$(2),e.Y8G("href","https://offers.edgefactor.com/aboutedgefactor"),e.R7$(2),e.Y8G("href","https://offers.edgefactor.com/meet-the-team"),e.R7$(2),e.Y8G("href","https://offers.edgefactor.com/contact-us"),e.R7$(5),e.Y8G("href","/legal/terms-of-use"),e.R7$(2),e.Y8G("href","/legal/privacy-policy"),e.R7$(2),e.Y8G("href","/legal/data-processing-agreement"),e.R7$(2),e.Y8G("href","/legal/privacy-whitepaper"),e.R7$(5),e.Lme(" Edge Factor \xa9 ",i.year,", All rights reserved, V",i.environment.version," "))},dependencies:[d.Jm,d.hU,d.M0,d.lO,d.ln],styles:['.link-parent-container[_ngcontent-%COMP%]{width:100%;background-color:#111;padding-top:10px;padding-bottom:20px;bottom:0;text-align:center}.link-parent-container[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-family:"Exo 2";font-weight:800;color:#f99e00;margin:16px 0}ion-footer[_ngcontent-%COMP%]{display:contents;background-color:#111;color:#fff;font-size:12px}']})}}return o})();var fi=l(77340);const Pi=o=>({"content-no-header":o});function bi(o,r){if(1&o){const n=e.RV6();e.j41(0,"div")(1,"app-view-options-row",3),e.bIt("buttonChanged",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.buttonClicked(i))})("optionSelected",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.setSelectedViewType(i))})("rowFiltered",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.rowFiltered(i))}),e.k0s()()}if(2&o){let n;const t=e.XpG();e.R7$(),e.Y8G("template",t.template)("canAdd","Admin"===(null==t.instance?null:t.instance.title))("instance",t.instance)("featureTab",t.featureTab)("selectedItem",null!==(n=null==t.routeParams?null:t.routeParams.viewType)&&void 0!==n?n:0)}}function Mi(o,r){if(1&o&&(e.j41(0,"h1",8),e.EFF(1),e.k0s()),2&o){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",null==n.section?null:n.section.title," ")}}function Oi(o,r){if(1&o&&(e.j41(0,"div",9),e.EFF(1),e.k0s()),2&o){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",null==n.section?null:n.section.description," ")}}function yi(o,r){if(1&o&&e.nrm(0,"div"),2&o){const n=e.XpG(6);e.Aen("--background-image:url("+n.assetUrl+");")}}function wi(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-component-row-selector",13),e.bIt("updateInstanceComponentValue",function(i){e.eBV(n);const a=e.XpG().$implicit,s=e.XpG(5);return e.Njj(s.setNewValue(i,a.id))}),e.k0s()}if(2&o){let n;const t=e.XpG().$implicit,i=e.XpG(2).$implicit,a=e.XpG(3);e.Y8G("instance",a.instance)("instanceSectionComponent",t)("routeParams",a.routeParams)("searchFilter",a.searchFilter)("instanceSection",i)("continuousFeedback",null!==(n=null==i||null==i.section?null:i.section.isContinuousFeedback)&&void 0!==n&&n)}}function vi(o,r){if(1&o&&(e.j41(0,"ion-col",10),e.DNE(1,yi,1,2,"div",11)(2,wi,1,6,"app-component-row-selector",12),e.k0s()),2&o){const n=r.$implicit,t=r.$index,i=e.XpG(2).$index,a=e.XpG(3);e.Y8G("size","xs"===a.layoutService.currentScreenSize&&"Organization Manager"===a.instance.feature.featureType.name?"auto":0!==(null==n.component||null==n.component.templateField?null:n.component.templateField.colspan)?null==n.component||null==n.component.templateField?null:n.component.templateField.colspan:null),e.R7$(),e.vxM("Listing Details"===n.component.componentType.name&&0===i&&0===t&&!0===a.instance.feature.isFullWidth?1:-1),e.R7$(),e.vxM(null!=n.component&&null!=n.component.templateField&&n.component.templateField.isFilter?-1:2)}}function xi(o,r){if(1&o&&(e.j41(0,"ion-row",7),e.Z7z(1,vi,3,3,"ion-col",10,e.fX1),e.nI1(3,"pageViewComponentFilter"),e.nI1(4,"orderBy"),e.k0s()),2&o){const n=r.$implicit,t=e.XpG(4);e.Y8G("ngClass","Organization Manager"===t.instance.feature.featureType.name?"vertical-row":""),e.R7$(),e.Dyx(e.i5U(4,4,e.i5U(3,1,n,!0===(null==t.instance||null==t.instance.feature?null:t.instance.feature.isFullWidth)),"component.templateField.colNumber"))}}function Si(o,r){if(1&o&&(e.j41(0,"div",7,0),e.DNE(2,Mi,2,1,"h1",8)(3,Oi,2,1,"div",9),e.j41(4,"ion-grid"),e.Z7z(5,xi,5,7,"ion-row",7,e.fX1),e.nI1(7,"groupBy"),e.nI1(8,"values"),e.k0s()()),2&o){const n=r.$implicit;e.Aen(!0!==(null==n.section?null:n.section.hideBackground)&&(null!=n.section&&n.section.backgroundColor||null!=n&&n.backgroundColor)?"background:"+((null==n||null==n.backgroundColor?null:n.backgroundColor.length)>0?null==n?null:n.backgroundColor:n.section.backgroundColor):"background-color:none"),e.Y8G("ngClass",n.section.hideBackground?"section-no-background":"section"),e.R7$(2),e.vxM(null!=n.section&&n.section.title&&n.section.title.length>0&&n.section.showTitleOnPlayer&&!0===n.section.showTitleOnPlayer?2:-1),e.R7$(),e.vxM(null!=n.section&&n.section.description&&n.section.description.length>0&&!0===n.section.showOnInstanceViewer&&!0===n.section.showDescOnPlayer?3:-1),e.R7$(2),e.Dyx(e.bMT(8,8,e.i5U(7,5,n.instanceSectionComponents,"component.builderRowNumber")))}}function Ii(o,r){if(1&o&&e.Z7z(0,Si,9,10,"div",6,e.fX1),2&o){const n=e.XpG(2);e.Dyx(n.template.instanceSections)}}function Ti(o,r){1&o&&(e.j41(0,"div"),e.nrm(1,"app-link-footer"),e.k0s())}function Fi(o,r){if(1&o&&(e.j41(0,"ion-content",4)(1,"div",5),e.DNE(2,Ii,2,0),e.k0s(),e.DNE(3,Ti,2,0,"div"),e.k0s()),2&o){const n=e.XpG();e.Aen("height:"+n.height+"vh"),e.Y8G("ngClass",e.eq3(6,Pi,"V7 Landing Page"===n.instance.feature.featureType.name||!0===n.instance.feature.isFullWidth&&n.routeParams.viewType!==n.viewTypes.Player))("scrollEvents",!0),e.R7$(2),e.vxM((null==n.routeParams?null:n.routeParams.viewType)!==n.viewTypes.Player?2:-1),e.R7$(),e.vxM("V7 Landing Page"===n.instance.feature.featureType.name||n.isScorm?-1:3)}}function Bi(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-player-view",14),e.bIt("optionSelected",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.setSelectedViewType(i))})("buttonChanged",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.buttonClicked(i))}),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("instanceTemplate",n.template)("instance",n.instance)("featureTab",n.featureTab)("routeParams",n.routeParams)("isScorm",n.isScorm)("searchFilter",n.searchFilter)("selectedUserId",n.selectedUserId)}}let Fe=(()=>{class o{constructor(n,t,i,a,s,c,p,h,g,b,R){this.dataService=n,this.router=t,this.alertController=i,this.instanceService=a,this.layoutService=s,this.toast=c,this.popoverController=p,this.authService=h,this.breadcrumbService=g,this.eventsService=b,this.modalController=R,this.instanceRowsInLite=[],this.viewTypes=m.q,this.componentDestroyed$=new y.B,this.minimized=!1,this.isScorm=!1,this.height=0,this.windowResize=!1}onResize(){this.windowResize=!0}ngOnInit(){this.isScorm=this.router.url.startsWith("/scorm/"),this.assetUrl=this.instance?.feature?.coverMediaAssetId?`${B.c.contentUrl}asset/${this.instance?.feature?.coverMediaAssetId}/content?height=1000`:"assets/images/defaultbackgroundgradient.png",this.routeParams?.viewType===m.q.Player&&(this.minimized=!0),this.eventsService.subscribe("changeViewType",n=>{this.setSelectedViewType(n)})}ngOnChanges(n){(this.windowResize||n.containerTop?.currentValue)&&(this.calculateHeight(),this.windowResize=!1)}getElementRef(n){return new e.aKT(n)}calculateHeight(){let n=0;"xs"===this.layoutService.currentScreenSize?this.containerTop=this.containerTop<80?this.containerTop+85:this.containerTop+145:this.containerTop>80&&(this.containerTop=this.containerTop+75),n=this.containerTop/window.innerHeight*100,this.height=100-n}setSelectedViewType(n){this.routeParams.viewType=n,this.searchFilter="",this.instanceService.openInstance(this.routeParams.featureSlug,this.routeParams.instanceSlug,this.routeParams.tabName,m.q[n])}addItem(n){var t=this;this.dataService.getFeatureTypes().pipe((0,u.Q)(this.componentDestroyed$),(0,A.T)(i=>i.map(a=>({key:a.id,value:a.name})))).subscribe(function(){var i=(0,f.A)(function*(a){const s=yield t.popoverController.create({component:$.u,cssClass:"feature-type-select-popover",componentProps:{header:"Please select a feature type:",options:a},event:n,side:"bottom"});s.onDidDismiss().then(function(){var c=(0,f.A)(function*(p){p.data&&t.dataService.createFeature(p.data.key).pipe((0,u.Q)(t.componentDestroyed$)).subscribe(h=>{t.instanceService.openInstance("repository/builder",h)})});return function(p){return c.apply(this,arguments)}}()),yield s.present()});return function(a){return i.apply(this,arguments)}}())}rowFiltered(n){this.searchFilter=n}setNewValue(n,t){const i=this.template.instanceSections.findIndex(s=>s.instanceSectionComponents.some(c=>c.id===t)),a=this.template.instanceSections[i].instanceSectionComponents.findIndex(s=>s.id===t);-1!==a&&(this.template.instanceSections[i].instanceSectionComponents[a].value=n)}ngOnDestroy(){this.eventsService.unsubscribe("changeViewType"),this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}buttonClicked(n){this.featureTab?.featureTabButtons&&(1===this.featureTab?.featureTabButtons.length?this.optionSelected(this.featureTab.featureTabButtons[0],n):this.openButtonOptions(n))}openButtonOptions(n){var t=this;return(0,f.A)(function*(){if(t.featureTab?.featureTabButtons){const i=yield t.popoverController.create({component:$.u,cssClass:"question-type-popover",componentProps:{options:t.featureTab?.featureTabButtons.map(a=>({key:a.id,value:a.buttonText}))},event:n,side:"bottom"});i.onDidDismiss().then(a=>{if(a.data){const s=t.featureTab?.featureTabButtons.find(c=>c.id===a.data.key);t.optionSelected(s,n)}}),yield i.present()}})()}optionSelected(n,t){this.customFeature=null,n?.referenceId&&""!==n.referenceId?this.dataService.getFeatureById(n.referenceId).subscribe(i=>{switch(i.featureType?.name){case"Product Manager":this.addProduct(i);break;case"Question Manager":this.openSelectQuestionType(i,t);break;case"Organization Manager":this.addOrganization(i);break;case"Media Manager":this.addMedia(t);break;case"Modifiable Learning Container Pages":"Modifiable Learning Container Pages"!==this.instance.feature.featureType.name&&"Accredited Learning Container Pages"!==this.instance.feature.featureType.name||!0!==this.instance.isDefault?"Modifiable Learning Container Pages"===this.instance.feature.featureType.name||"Accredited Learning Container Pages"===this.instance.feature.featureType.name?this.addAssignment():this.template.instanceSections.find(a=>a.instanceSectionComponents?.some(s=>"Row Manager"===s.component.componentType.name))?this.openSelectRowContent(t):this.addClassroom():this.addClassroom();break;case"Accredited Learning Container Pages":break;case"Feature Manager":this.addItem(t);break;default:this.addInstance(t)}}):this.template.instanceSections.find(i=>i.instanceSectionComponents?.some(a=>"Row Manager"===a.component.componentType.name))&&this.openSelectRowContent(t)}addClassroom(){var n=this;return(0,f.A)(function*(){const t=yield n.modalController.create({component:Ce.u,componentProps:{selectedInstance:n.instance,featureTypeName:n.instance?.feature?.featureType?.name,actionType:he.w.CreateClass,reload:!0},cssClass:"my-instances-dialog"});t.onDidDismiss().then(i=>{!0===i.data&&n.dataService.reload$.next(null)}),yield t.present()})()}openSelectQuestionType(n,t){var i=this;return(0,f.A)(function*(){i.dataService.getQuestionTypes().subscribe(function(){var a=(0,f.A)(function*(s){if(s){const c=yield i.popoverController.create({component:$.u,cssClass:"question-type-popover",componentProps:{options:s.map(p=>({key:p.id,value:p.name}))},event:t,side:"bottom"});c.onDidDismiss().then(p=>{p.data&&i.createQuestion(n,p.data.key)}),yield c.present()}});return function(s){return a.apply(this,arguments)}}())})()}openSelectRowContent(n){var t=this;return(0,f.A)(function*(){if(1===t.featureTab?.featureTabRowTypes?.length)t.addRowToRowManager(t.featureTab.featureTabRowTypes[0].rowType.id);else if(t.featureTab?.featureTabRowTypes?.length>1){const i=yield t.popoverController.create({component:$.u,cssClass:"question-type-popover",componentProps:{options:t.featureTab.featureTabRowTypes.map(a=>({key:a.rowType.id,value:a.rowType.name}))},event:n,side:"bottom"});i.onDidDismiss().then(a=>{a.data&&t.addRowToRowManager(a.data.key)}),yield i.present()}else t.dataService.getRowTypes().subscribe(function(){var i=(0,f.A)(function*(a){if(a){const s=yield t.popoverController.create({component:$.u,cssClass:"question-type-popover",componentProps:{options:a.map(c=>({key:c.id,value:c.name}))},event:n,side:"bottom"});s.onDidDismiss().then(c=>{c.data&&t.addRowToRowManager(c.data.key)}),yield s.present()}});return function(a){return i.apply(this,arguments)}}())})()}addAssignment(){var n=this;return(0,f.A)(function*(){const t=yield n.modalController.create({component:Ce.u,componentProps:{selectedInstance:n.instance,instanceId:n.instance.id,featureTypeName:n.instance?.feature?.featureType?.name,actionType:he.w.CreateAssignment,reload:!0},cssClass:"my-instances-dialog"});t.onDidDismiss().then(i=>{!0===i.data&&n.dataService.reload$.next(null)}),yield t.present()})()}addAllPeopleToAssignment(n){this.dataService.addAllPeopleToAssignment(this.instance.id,n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{t&&this.toast.presentToast("Active users added to assignment")})}addRowToRowManager(n){this.dataService.createRow({title:"New Assignment",rowTypeId:n,status:"Published"}).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{if(t&&""!==t){const i=this.template.instanceSections.findIndex(s=>s.instanceSectionComponents?.some(c=>"Row Manager"===c.component.componentType.name)),a=this.template.instanceSections[i]?.instanceSectionComponents?.find(s=>"Row Manager"===s.component.componentType.name)??null;if(a){const s=this.template.instanceSections[i].instanceSectionComponents.findIndex(p=>p.id===a.id);a.instanceSectionComponentRows?(this.template.instanceSections[i].instanceSectionComponents[s].instanceSectionComponentRows=[{id:"",instanceSectionComponentId:a.id,rowId:t,sortOrder:0,hidden:!1}].concat(a.instanceSectionComponentRows.map(p=>({id:p.id,instanceSectionComponentId:p.instanceSectionComponentId,rowId:p.rowId,sortOrder:p.sortOrder+1,hidden:!1}))),this.instanceRowsInLite=this.template.instanceSections[i].instanceSectionComponents[s].instanceSectionComponentRows.map(p=>({id:p.rowId,sortOrder:p.sortOrder}))):(this.template.instanceSections[i].instanceSectionComponents[s].instanceSectionComponentRows=[{id:"",instanceSectionComponentId:a.id,rowId:t,sortOrder:0,hidden:!1}],this.instanceRowsInLite=this.template.instanceSections[i].instanceSectionComponents[s].instanceSectionComponentRows.map(p=>({id:p.rowId,sortOrder:p.sortOrder})));const c=JSON.stringify(this.instanceRowsInLite);this.template.instanceSections[i].instanceSectionComponents[s].value=c,this.dataService.updateInstanceSectionComponent(a.id,a.value).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{"Modifiable Learning Container Pages"===this.instance.feature.featureType.name&&this.addAllPeopleToAssignment(t),this.forceChangeDetection()})}}})}forceChangeDetection(){this.template.instanceSections=[...this.template.instanceSections.map(n=>({...n,instanceSectionComponents:n.instanceSectionComponents.map(t=>({...t,component:{...t.component},instanceSectionComponentRows:[...t.instanceSectionComponentRows]}))}))],this.template={...this.template}}createQuestion(n,t){this.dataService.createQuestion({id:null,questionTypeId:t}).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{i&&this.openInstanceBuilder(n,i)})}openInstanceBuilder(n,t){null!=n.featureType&&("Product Manager"===n.featureType?.name||"Organization Manager"===n.featureType?.name||"Question Manager"===n.featureType?.name||"Network Manager"===n.featureType?.name?this.instanceService.openInstance(n.featureSlug,t):this.instanceService.openInstance("instance",t,null,"builder"))}getSectionComponents(n){return n.instanceSectionComponents.map(t=>t.component)}addInstance(n){var t=this;this.dataService.getMyOrganizations(this.instance.feature.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(function(){var i=(0,f.A)(function*(a){a.length<1?yield t.presentAlert():a.length>1||!0===t.authService.userContext?.canManage?t.addInstanceToOrganization(n):t.saveInstance(a[0].id)});return function(a){return i.apply(this,arguments)}}())}populateNewInstanceIn(n){return{title:this.customFeature?.title??this.instance?.feature?.title,featureId:this.customFeature?.id??this.instance?.feature?.id,organizationId:n,status:"Modifiable Learning Container Pages"===this.instance.feature.featureType.name?"public":"private"}}addInstanceToOrganization(n){var t=this;return(0,f.A)(function*(){const i=yield t.popoverController.create({component:It.e,cssClass:"add-search-modal",componentProps:{linkTypeName:"Organizations",criteriaType:null,options:null},event:n,side:"bottom"});i.onDidDismiss().then(a=>{a.data&&t.saveInstance(a.data.id)}),yield i.present()})()}saveInstance(n){const t=this.populateNewInstanceIn(n);this.dataService.createInstance(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{this.breadcrumbService.hardRefresh=!0,this.instanceService.openInstance("instance",i.id,null,"builder")})}addMedia(n){var t=this;return(0,f.A)(function*(){yield(yield t.popoverController.create({component:St.Z,cssClass:"add-media-popover",event:n,side:"bottom"})).present()})()}presentAlert(){var n=this;return(0,f.A)(function*(){const t=yield n.alertController.create({cssClass:"",header:"Please Note:",message:"User not linked to an organization.",buttons:["OK"]});yield t.present(),yield t.onDidDismiss()})()}addProduct(n){const t={name:this.instance?.feature?.title+Math.floor(1e3+9e3*Math.random()),description:this.instance?.feature?.description};this.dataService.addProduct(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{this.openInstanceBuilder(n,i.id)})}addOrganization(n){const t={name:this.instance?.feature?.title+Math.floor(1e3+9e3*Math.random())};this.dataService.addOrganization(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{i&&this.openInstanceBuilder(n,i.id)})}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(I.Ix),e.rXU(d.hG),e.rXU(M.b),e.rXU(x.Y),e.rXU(X.A),e.rXU(d.IE),e.rXU(Tt.u),e.rXU(H.b),e.rXU(F.s),e.rXU(d.W3))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-instance-viewer"]],hostBindings:function(t,i){1&t&&e.bIt("resize",function(s){return i.onResize(s)},!1,e.tSv)},inputs:{containerTop:"containerTop",template:"template",instance:"instance",featureTab:"featureTab",routeParams:"routeParams",selectedUserId:"selectedUserId"},features:[e.OA$],decls:3,vars:3,consts:[["parentDiv",""],[3,"style","ngClass","scrollEvents"],[3,"instanceTemplate","instance","featureTab","routeParams","isScorm","searchFilter","selectedUserId"],[3,"buttonChanged","optionSelected","rowFiltered","template","canAdd","instance","featureTab","selectedItem"],[3,"ngClass","scrollEvents"],[1,"full-container"],[3,"style","ngClass"],[3,"ngClass"],[1,"instance-title-default"],[1,"description"],["col-12","","col-md-6","","col-lg-4","","col-xl-3","",3,"size"],[3,"style"],[3,"instance","instanceSectionComponent","routeParams","searchFilter","instanceSection","continuousFeedback"],[3,"updateInstanceComponentValue","instance","instanceSectionComponent","routeParams","searchFilter","instanceSection","continuousFeedback"],[3,"optionSelected","buttonChanged","instanceTemplate","instance","featureTab","routeParams","isScorm","searchFilter","selectedUserId"]],template:function(t,i){1&t&&e.DNE(0,bi,2,5,"div")(1,Fi,4,8,"ion-content",1)(2,Bi,1,7,"app-player-view",2),2&t&&(e.vxM((null==i.routeParams?null:i.routeParams.viewType)!==i.viewTypes.Player&&"V7 Landing Page"!==i.instance.feature.featureType.name&&!0!==i.instance.feature.isFullWidth?0:-1),e.R7$(),e.vxM((null==i.routeParams?null:i.routeParams.viewType)!==i.viewTypes.Player?1:-1),e.R7$(),e.vxM((null==i.routeParams?null:i.routeParams.viewType)===i.viewTypes.Player?2:-1))},dependencies:[C.YU,d.hU,d.W9,d.lO,d.ln,W.A,D,Ie,Te,T.lU,T.Qe,T.Op,fi.A],styles:[".content-no-header[_ngcontent-%COMP%]{display:contents;height:100%;--padding-left: 1.25vw !important;--padding-right: 1.25vw !important}.full-container[_ngcontent-%COMP%]{position:relative;min-height:100%}ion-content[_ngcontent-%COMP%]::part(scroll){overflow-y:overlay!important}[_nghost-%COMP%]{background-color:#1e1e1e;color:#fff}[_nghost-%COMP%]   ion-grid[_ngcontent-%COMP%]{height:100%;min-height:100%}[_nghost-%COMP%]   ion-row[_ngcontent-%COMP%]{height:100%}@media screen and (max-width: 960px){[_nghost-%COMP%]   .vertical-row[_ngcontent-%COMP%]{gap:20px;flex-direction:column}}[_nghost-%COMP%]   .description[_ngcontent-%COMP%]{margin-bottom:25px;margin-left:var(--page-margin-left-player)}[_nghost-%COMP%]   .section[_ngcontent-%COMP%]{flex:1;position:relative;overflow:hidden}[_nghost-%COMP%]   .section-no-background[_ngcontent-%COMP%]{flex:1;border-radius:5px;position:relative;overflow:hidden;display:contents}  .mdc-tab__content{overflow:hidden}.player-outer-container[_ngcontent-%COMP%]{margin-right:var(--page-margin-right-player);margin-left:var(--page-margin-left-player);overflow-y:auto;height:calc(100% - 1px)}.player-outer-container[_ngcontent-%COMP%]   .player-container[_ngcontent-%COMP%]{border:1px solid #f99e00;background-color:#393939;border-radius:11px}.player-side-panel-container[_ngcontent-%COMP%]{overflow-y:auto;height:calc(100% - 249px);width:20%;background-color:#1e1e1e;margin-top:5px;margin-right:5px;border-radius:10px}.top-border[_ngcontent-%COMP%]{border-color:#171717;border-width:1px;border-style:solid;width:100%}.player-inner-container[_ngcontent-%COMP%]{width:100%;float:left}ion-col[_ngcontent-%COMP%]{min-height:0px!important}"]})}}return o})();const ki=[ge.c];let Ri=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=e.$C({type:o})}static{this.\u0275inj=e.G2t({imports:[ki,Y.G,K.Q,E.T,vt.X,xt.j,K.Q,E.T]})}}return o})();var Be=l(94862),Vi=l(84412),ke=l(61594),ji=l(66413),oe=l(13130),Xi=l(25727),$i=l(345),Gi=l(16372),ae=l(96850),Ui=l(12536);function Di(o,r){if(1&o&&e.nrm(0,"app-feature-instance-side-panel-builder",2),2&o){const n=e.XpG(2),t=e.XpG();e.Y8G("template",n)("instance",t.instance)("featureTab",t.featureTab)("routeParams",t.routeParams)("onlyContent",t.onlyContent)}}function Ei(o,r){if(1&o&&e.DNE(0,Di,1,5,"app-feature-instance-side-panel-builder",2),2&o){const n=e.XpG(),t=e.XpG();e.vxM(t.instance&&n?0:-1)}}function Ni(o,r){if(1&o&&e.nrm(0,"app-feature-instance-viewer",3),2&o){const n=e.XpG(2),t=e.sdS(1),i=e.XpG();e.Y8G("containerTop",t.getBoundingClientRect().top)("template",n)("instance",i.instance)("featureTab",i.featureTab)("routeParams",i.routeParams)("selectedUserId",i.selectedUserId)}}function Ai(o,r){if(1&o&&e.DNE(0,Ni,1,6,"app-feature-instance-viewer",3),2&o){const n=e.XpG(),t=e.XpG();e.vxM(t.instance&&n?0:-1)}}function zi(o,r){if(1&o&&(e.j41(0,"div",1,0),e.DNE(2,Ei,1,1)(3,Ai,1,1),e.k0s()),2&o){const n=e.XpG();e.R7$(2),e.vxM("Instance Builder"!==(null==n.featureTab.type?null:n.featureTab.type.name)&&"Repository Builder"!==(null==n.featureTab.type?null:n.featureTab.type.name)||(null==n.routeParams?null:n.routeParams.viewType)!==n.viewTypes.Builder?3:2)}}let se=(()=>{class o{constructor(n,t){this.dataService=n,this.instanceService=t,this.disabled=!0,this.onlyContent=!1,this.formChanged=new e.bkB,this.instanceChanged=new e.bkB,this.componentType=Ui.I,this.componentDestroyed$=new y.B,this.viewTypes=m.q}ngOnInit(){this.dataService.reload$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.setTemplate()}),"Instance Builder"===this.featureTab.type?.name&&"default"!==this.routeParams.tabName&&(-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("manager")||-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("organization")||-1!==this.instance.feature.featureType.name.toLocaleLowerCase().indexOf("learning container pages"))&&this.routeParams.viewType!==m.q.Builder&&(this.routeParams.viewType=m.q.Builder,this.instanceService.openInstance(this.routeParams.featureSlug,this.routeParams.instanceSlug,this.featureTab.tab.name,"builder")),this.setTemplate()}getId(){return this.instanceService.isValidGUID(this.routeParams.instanceSlug??"")?this.routeParams.instanceSlug:this.instance.id}setTemplate(){this.template$=this.dataService.getInstanceTemplate(this.instance.id,this.templateId,"Instance Builder"!==this.featureTab.type?.name||this.routeParams.viewType!==m.q.Builder,"Instance Builder"===this.featureTab.type?.name&&this.routeParams.viewType===m.q.Builder).pipe((0,A.T)(n=>n))}formChanges(n){this.formChanged.emit(n)}instanceUpdated(){this.instanceChanged.emit(null)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(v.u),e.rXU(M.b))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-template"]],inputs:{templateId:"templateId",instance:"instance",featureTab:"featureTab",disabled:"disabled",routeParams:"routeParams",onlyContent:"onlyContent",selectedUserId:"selectedUserId"},outputs:{formChanged:"formChanged",instanceChanged:"instanceChanged"},decls:2,vars:3,consts:[["container",""],[2,"height","100%"],[3,"template","instance","featureTab","routeParams","onlyContent"],[3,"containerTop","template","instance","featureTab","routeParams","selectedUserId"]],template:function(t,i){if(1&t&&(e.DNE(0,zi,4,1,"div",1),e.nI1(1,"async")),2&t){let a;e.vxM((a=e.bMT(1,1,i.template$))?0:-1,a)}},dependencies:[Fe,yt,C.Jj]})}}return o})();const Li=["matTabGroup"],Yi=o=>({"header-less-tabs":o});function Qi(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-template",6),e.bIt("formChanged",function(i){e.eBV(n);const a=e.XpG(3);return e.Njj(a.formChanged(i))})("instanceChanged",function(){e.eBV(n);const i=e.XpG(3);return e.Njj(i.instanceUpdated())}),e.k0s()}if(2&o){const n=e.XpG().$implicit,t=e.XpG(2);e.Y8G("templateId",n.tab.templateId)("instance",t.instance)("featureTab",n)("routeParams",t.routeParams)("selectedUserId",t.selectedUserId)}}function Hi(o,r){1&o&&(e.j41(0,"mat-tab",4),e.DNE(1,Qi,1,5,"ng-template",5),e.k0s()),2&o&&e.Y8G("label",r.$implicit.tab.name)}function Wi(o,r){if(1&o){const n=e.RV6();e.j41(0,"mat-tab-group",3,0),e.bIt("selectedIndexChange",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.selectedIndex.set(i))})("selectedTabChange",function(i){e.eBV(n);const a=e.XpG();return e.Njj(a.onTabChanged(i))}),e.Z7z(2,Hi,2,1,"mat-tab",4,e.fX1),e.k0s()}if(2&o){const n=e.XpG();e.Y8G("selectedIndex",n.selectedIndex())("ngClass",e.eq3(2,Yi,n.featureTabs.length<=1||n.instance.feature.isFullWidth||n.onPlayer())),e.R7$(2),e.Dyx(n.getTabs())}}let Re=(()=>{class o{constructor(n,t,i,a,s,c,p){this.route=n,this.alertService=t,this.tabFilterPipe=i,this.instanceService=a,this.eventService=s,this.unsavedChangesGuard=c,this.rolesService=p,this.selectedIndex=(0,e.vPA)(0),this.reloadSystemProperties=new e.bkB,this.emitformChanged=new e.bkB,this.instanceChanged=new e.bkB,this.previousIndex=0,this.componentDestroyed$=new y.B,this.openPlayer=!1,this.tabChanged$=new y.B}ngOnInit(){this.route.fragment.subscribe(n=>{if(n)this.selectedIndex.set(this.featureTabs.findIndex(t=>t.tab.name.toLowerCase()===n.toLowerCase()));else if("classroom"==this.instance.feature.featureSlug){const t=this.getTabs();this.selectedIndex.set(t.findIndex(i=>i.tab&&"assignments"===i.tab.name.toLowerCase())),t.find(i=>i.tab&&"assignments"===i.tab.name.toLowerCase())&&(this.routeParams.tabName="assignments"),this.previousIndex=this.selectedIndex()}}),this.instanceService.reload$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{this.routeParams.tabName!==n&&n?(this.routeParams.tabName=n,this.selectedIndex.set(this.getTabs().findIndex(t=>t.tab&&t.tab.name.toLowerCase()===n.toLowerCase()))):!n&&"classroom"==this.instance.feature.featureSlug&&this.selectedIndex.set(this.getTabs().findIndex(t=>t.type&&"Instance Builder"===t.type.name)),this.previousIndex=this.selectedIndex()}),this.eventService.subscribe("openStudentResults",n=>{this.selectedUserId=n;const t=this.getTabs().findIndex(i=>"assignments"===i.tab.name.toLocaleLowerCase());this.routeParams.instanceSlug="default",this.openPlayer=!0,this.selectedIndex.set(t)}),this.setFirstBuilderTab(),this.tabChanged$.pipe((0,J.B)(700)).subscribe(n=>{this.setTabChangeLogic(n)})}onPlayer(){return this.routeParams.viewType===m.q.Player}setFirstBuilderTab(){if("my-journey"!==this.routeParams?.featureSlug&&this.routeParams.viewType===m.q.Builder){const n=this.featureTabs.filter(!0===this.instance.isDefault?i=>!0===i.isDefaultInstanceTab:i=>!0!==i.isDefaultInstanceTab);if(!n)return;const t=n.findIndex(i=>!0===i.showTab&&"Instance Builder"===i.type?.name);this.selectedIndex.set(-1!==t?t:0),this.previousIndex=this.selectedIndex()}else if(this.routeParams.tabName){if(!this.featureTabs.filter(!0===this.instance.isDefault?i=>!0===i.isDefaultInstanceTab:i=>!0!==i.isDefaultInstanceTab))return;const t=this.getTabs().findIndex(i=>i.tab.name?.toLocaleLowerCase()?.replace(" ","")===this.routeParams.tabName?.toLocaleLowerCase());this.selectedIndex.set(-1!==t?t:0),this.previousIndex=this.selectedIndex()}}formChanged(n){this.formHasChanged=n,this.emitformChanged.emit()}onTabChanged(n){var t=this;return(0,f.A)(function*(){t.tabChanged$.next(n)})()}setTabChangeLogic(n){var t=this;return(0,f.A)(function*(){if(t.selectedIndex.set(0===t.previousIndex&&0===n.index?0:n.index),n.index!==t.previousIndex){t.unsavedChangesGuard.canDeactivateVar?t.previousIndex=n.index:(t.selectedIndex.set(t.previousIndex),yield t.alertService.leaveBuilderAlert("Leave the builder?","Don't worry, your changes have been autosaved and published.").then(()=>{t.unsavedChangesGuard.canDeactivateVar=!0,t.selectedIndex.set(n.index),t.reloadSystemProperties.emit()}));const i=t.getTabs()[t.selectedIndex()];"Instance Builder"!==i?.type?.name&&(t.routeParams.viewType=t.openPlayer?m.q.Player:m.q.Grid,t.routeParams.tabName=i.tab.name,t.instanceService.openInstance(t.routeParams.featureSlug,!1===t.instanceService.isValidGUID(t.routeParams.instanceSlug??"")?null:t.routeParams.instanceSlug,i.tab.name,t.openPlayer?"player":"grid")),t.openPlayer=!1}})()}hasEditAccess(){const n=this.instance.feature?.featureTabs?.flatMap(t=>t?.featureTabEditActions);return this.rolesService.hasRoleAccessPlayerView(n&&0!==n?.length?n.map(t=>t.actionBw):[S.Q.Publish])}getTabs(){return this.tabFilterPipe.transform(this.instance?.feature.featureTabs,this.instance?.isDefault??!1)}instanceUpdated(){this.instanceChanged.emit(null)}ngOnDestroy(){this.eventService.unsubscribe("openStudentResults"),this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(I.nX),e.rXU(j.u),e.rXU(oe.m),e.rXU(M.b),e.rXU(F.s),e.rXU(de.h),e.rXU(w.P))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-tabs"]],viewQuery:function(t,i){if(1&t&&e.GBs(Li,5),2&t){let a;e.mGM(a=e.lsd())&&(i.tabGroup=a.first)}},inputs:{featureTabs:"featureTabs",instance:"instance",routeParams:"routeParams",selectedIndex:"selectedIndex"},outputs:{reloadSystemProperties:"reloadSystemProperties",emitformChanged:"emitformChanged",instanceChanged:"instanceChanged"},decls:2,vars:1,consts:[["matTabGroup",""],[1,"tab-container"],["color","primary","mat-stretch-tabs","false","mat-align-tabs","start",2,"height","100%",3,"selectedIndex","ngClass"],["color","primary","mat-stretch-tabs","false","mat-align-tabs","start",2,"height","100%",3,"selectedIndexChange","selectedTabChange","selectedIndex","ngClass"],[1,"tabs",3,"label"],["matTabContent",""],[3,"formChanged","instanceChanged","templateId","instance","featureTab","routeParams","selectedUserId"]],template:function(t,i){1&t&&(e.j41(0,"div",1),e.DNE(1,Wi,4,4,"mat-tab-group",2),e.k0s()),2&t&&(e.R7$(),e.vxM(i.featureTabs?1:-1))},dependencies:[C.YU,ae.$L,ae.mq,ae.T8,se],styles:[".tab-container{height:100%;min-height:100%;overflow-y:hidden}.tab-container .mat-mdc-tab-header-pagination-chevron{border-color:#fff}.tab-container>.mat-mdc-tab-group>.mat-mdc-tab-header{padding-left:var(--page-margin-left);padding-right:var(--page-margin-right);background-color:#1e1e1e}.tab-container>.mat-mdc-tab-group .mat-mdc-tab{margin-right:35px!important;padding:0!important;justify-content:flex-start!important;min-width:0px!important;width:auto!important;color:#fff;border-bottom:#f99e00;font-size:1em;font-weight:700;letter-spacing:.02em;opacity:100%}.tab-container>.mat-mdc-tab-group .mdc-tab--active{color:#f99e00}.tab-container>.mat-mdc-tab-group .mat-ink-bar{background:#f99e00}.tab-container>.mat-mdc-tab-group .mdc-tab__ripple:before{color:#fff;background-color:transparent}.tab-container>.mat-mdc-tab-group .mat-mdc-tab-ripple{color:#fff;background-color:transparent}.tab-container>.mat-mdc-tab-group .mdc-tab-indicator{display:none!important}.tab-container>.mat-mdc-tab-group .mdc-tab:hover .mdc-tab__text-label{color:#aaa!important}.tab-container>.mat-mdc-tab-group .mdc-tab__text-label{color:#aaa}.tab-container .mat-mdc-tab-body-wrapper{height:calc(100% - 50px)!important;min-height:calc(100% - 50px)!important}.tab-container div#mat-mdc-tab-0-0{padding:0!important;width:auto!important;margin-left:0!important}.tab-container .header-less-tabs>.mat-mdc-tab-header{display:none}.tab-container .header-less-tabs .mat-mdc-tab-body-wrapper{height:100%!important;min-height:100%!important}\n"],encapsulation:2})}}return o})();function Ji(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-tabs",5),e.bIt("reloadSystemProperties",function(){e.eBV(n);const i=e.XpG(3);return e.Njj(i.reloadSystemProperties())})("instanceChanged",function(){e.eBV(n);const i=e.XpG(3);return e.Njj(i.setData())}),e.k0s()}if(2&o){const n=e.XpG(3);e.Y8G("featureTabs",n.instance.feature.featureTabs)("instance",n.instance)("routeParams",n.routeParams)("selectedIndex",n.selectedIndex)}}function Zi(o,r){if(1&o){const n=e.RV6();e.j41(0,"app-template",6),e.bIt("instanceChanged",function(){e.eBV(n);const i=e.XpG(3);return e.Njj(i.setData())}),e.k0s()}if(2&o){const n=e.XpG(3);e.Y8G("templateId",n.getTabs()[0].tab.templateId)("instance",n.instance)("featureTab",n.getTabs()[0])("routeParams",n.routeParams)("onlyContent",n.onlyContent())}}function Ki(o,r){if(1&o&&e.DNE(0,Ji,1,4,"app-tabs",3)(1,Zi,1,5,"app-template",4),2&o){let n,t;const i=e.XpG(2);e.vxM(i.getTabs()&&(null==(n=i.getTabs())?null:n.length)>1?0:-1),e.R7$(),e.vxM(i.getTabs()&&1===(null==(t=i.getTabs())?null:t.length)?1:-1)}}function qi(o,r){if(1&o&&(e.nrm(0,"app-page-header",2),e.DNE(1,Ki,2,2,"ng-template",null,0,e.C5r)),2&o){const n=e.sdS(2),t=e.XpG();e.Y8G("onlyContent",t.onlyContent())("scrollPosition",t.scrollPosition)("viewType",t.routeParams.viewType)("instance",t.instance)("content",n)("routeParams",t.routeParams)("featureTab",t.getTabs()[t.selectedIndex()])("featureTabs",t.getTabs())("selectedIndex",t.selectedIndex)}}function eo(o,r){1&o&&(e.j41(0,"ion-grid",1)(1,"ion-row",7)(2,"ion-col",8),e.nrm(3,"img",9)(4,"br")(5,"ion-spinner",10),e.k0s()()())}let re=(()=>{class o{constructor(n,t,i,a,s,c,p,h,g,b,R,oo,ao){this.activatedRoute=n,this.systemPropertiesService=t,this.geolocation=i,this.rolesServie=a,this.eventsService=s,this.router=c,this.tabFilterPipe=p,this.joinCodeService=h,this.instanceService=g,this.breadcrumbService=b,this.parseContentPipe=R,this.meta=oo,this.parseService=ao,this.componentDestroyed$=new y.B,this.selectedIndex=(0,e.vPA)(0),this.scrollPosition=0,this.isScorm=!1,this.scormLoading=!1,this.progress=new Vi.t(0)}ngOnInit(){this.setData(),!1===this.joinCodeService.hasOpened&&this.joinCodeService.openJoin(!0),this.instanceService.hardReload$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.breadcrumbService.hardRefresh=!1,this.setData()}),this.instanceService.breadcrumbReload$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.setData()})}ionViewDidEnter(){this.instance&&this.setData()}setBreadCrumb(){this.instance&&(("my-journey"===this.instance?.feature?.featureSlug||"library"===this.instance?.feature?.featureSlug||"directory"===this.instance?.feature?.featureSlug||"workspace"===this.instance?.feature?.featureSlug||"admin"===this.instance?.feature?.featureSlug)&&this.breadcrumbService.resetBreadCrumbs(),this.parseContentPipe.transform(this.instance.title,this.instance.id,null,!0).pipe((0,ke.$)()).subscribe(n=>{this.breadcrumbService.addBreadCrumb(this.instance?.id??"",this.router.url,n,"my-organization"===this.instance?.feature.featureSlug?this.orgId:null,this.instance?.feature.featureType.name??""),this.setMetaDescription()}))}setMetaDescription(){this.instance&&"private"!=this.instance.status&&this.parseContentPipe.transform("internal"===this.instance.feature.featureType.name.toLowerCase()?this.instance.feature.description??"":this.instance.description??"",this.instance.id,null,!0).pipe((0,ke.$)()).subscribe(n=>{n&&this.meta.updateTag({name:"description",content:n})})}setRouteParams(){const n=this.getRouteParamValue("featureSlug"),t="default"!==(this.getRouteParamValue("instanceslug")&&this.getRouteParamValue("instanceslug"))?this.getRouteParamValue("instanceslug"):null,i="default"!==(this.getRouteParamValue("tabName")&&this.getRouteParamValue("tabName"))?this.getRouteParamValue("tabName"):null,a="default"!==(this.getRouteParamValue("view")&&this.getRouteParamValue("view"))?this.getRouteParamValue("view"):"grid";this.routeParams={featureSlug:n,instanceSlug:t,tabName:i,viewType:this.getViewType(a)}}getViewType(n){return"builder"===n?.toLowerCase()?m.q.Builder:"player"===n?.toLowerCase()?m.q.Player:"list"===n?.toLowerCase()?m.q.List:m.q.Grid}getTabs(){return this.instance?.feature?.featureTabs?this.tabFilterPipe.transform(this.instance?.feature?.featureTabs,this.instance?.isDefault??!1):[]}getRouteParamValue(n){return this.activatedRoute.snapshot.data[n]??this.activatedRoute.snapshot.params[n]}setData(){this.setRouteParams(),this.instanceService.setPrevFeatureSlug(this.routeParams.featureSlug??""),this.routeParams?.featureSlug&&(this.userContext=JSON.parse(localStorage.getItem("user_context")),this.geolocation.getCurrentPosition().then(n=>{this.userContext&&(this.userContext.latitude=n.coords.latitude,this.userContext.longitude=n.coords.longitude,localStorage.setItem("user_context",JSON.stringify(this.userContext)))})),this.setInstance()}ionViewWillLeave(){this.eventsService.publish("viewLeft",null)}setInstance(){let n=this.routeParams.featureSlug,t="";if("instance"===this.routeParams.featureSlug?.toLocaleLowerCase())n=this.routeParams.instanceSlug;else if("my-organization"===this.routeParams.featureSlug?.toLocaleLowerCase()&&this.routeParams.viewType!==m.q.Player&&this.instanceService.isValidGUID(this.routeParams.instanceSlug??"")){if(this.selectedIndex.set(0),this.orgId===this.routeParams.instanceSlug&&this.instance)return void this.setInstanceData(this.instance);this.routeParams.viewType!==m.q.Player&&(this.orgId=this.routeParams.instanceSlug??"")}this.instanceService.isValidGUID(this.routeParams.instanceSlug??"")&&this.routeParams.viewType!==m.q.Player&&(t=this.orgId??this.routeParams.instanceSlug??""),n&&"undefined"!==n&&(this.instance=null,this.instanceService.getInstance(n,t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{this.setInstanceData(i)}))}setInstanceData(n){this.rolesServie.getProductFeatureRoles(n?.feature?.id),this.getSystemProperties(n)}getSystemProperties(n){if(this.systemPropertiesService.clearAllSystemProperties(),n){const t=[];if(t.push(this.systemPropertiesService.getSystemPropertyValues(P.T.Instance,n.id)),n?.feature?.id&&t.push(this.systemPropertiesService.getSystemPropertyValues(P.T.Feature,n.feature.id)),n.organizationId&&n.feature?.featureType?.systemPropertyType?.typeBw!==P.T.Organization&&t.push(this.systemPropertiesService.getSystemPropertyValues(P.T.Organization,n.organizationId)),"Badge Manager"==n.feature?.title&&t.push(this.systemPropertiesService.getSystemPropertyValues(P.T.CredentialEngineBadge,n.id)),n.feature?.featureType?.systemPropertyType?.typeBw)if(this.routeParams?.instanceSlug&&this.instanceService.isValidGUID(this.routeParams?.instanceSlug)){const i=n.feature.featureType.systemPropertyType.typeBw;i&&t.push(this.systemPropertiesService.getSystemPropertyValues(i,this.routeParams?.instanceSlug))}else n.feature?.featureType?.systemPropertyType?.typeBw===P.T.User&&t.push(this.systemPropertiesService.getSystemPropertyValues(P.T.User,n.id));(0,Q.p)(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.instance={...n},n?.feature?.featureType?.systemPropertyType?.typeBw===P.T.Organization&&this.parseService.populateOrganizationAssets(this.instance,this.systemPropertiesService.organizationProperties),this.setBreadCrumb()})}}reloadSystemProperties(){this.instance&&this.getSystemProperties(this.instance)}onlyContent(){return 1===(this.getTabs()?.length??0)&&this.routeParams?.viewType===m.q.Builder&&-1===this.instance?.feature?.featureType?.name.toLocaleLowerCase().indexOf("manager")||!0===this.instance?.feature.isFullWidth&&this.routeParams?.viewType!==m.q.Player}ngOnDestroy(){this.instance?.feature?.featureType?.systemPropertyType?.typeBw===P.T.Organization&&this.parseService.clearOrganizationAssets(),this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(t){return new(t||o)(e.rXU(I.nX),e.rXU(le.a),e.rXU(ji.L),e.rXU(w.P),e.rXU(F.s),e.rXU(I.Ix),e.rXU(oe.m),e.rXU(Xi.N),e.rXU(M.b),e.rXU(H.b),e.rXU(Z.F),e.rXU($i.W8),e.rXU(Gi.V))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-instance"]],decls:2,vars:2,consts:[["content",""],[1,"scormloader"],[3,"onlyContent","scrollPosition","viewType","instance","content","routeParams","featureTab","featureTabs","selectedIndex"],[3,"featureTabs","instance","routeParams","selectedIndex"],[1,"no-tabs",3,"templateId","instance","featureTab","routeParams","onlyContent"],[3,"reloadSystemProperties","instanceChanged","featureTabs","instance","routeParams","selectedIndex"],[1,"no-tabs",3,"instanceChanged","templateId","instance","featureTab","routeParams","onlyContent"],[1,"ion-align-items-center"],["size","12"],["src","assets/images/EdgeFactor-EF_rings-2018-white_small_png.png",1,"img"],["color","primary","name","dots"]],template:function(t,i){1&t&&e.DNE(0,qi,3,9)(1,eo,6,0,"ion-grid",1),2&t&&(e.vxM(i.instance?0:-1),e.R7$(),e.vxM(i.scormLoading?1:-1))},dependencies:[Be.y,d.hU,d.lO,d.ln,d.w2,Re,se],styles:['[_nghost-%COMP%]{height:100%;background-color:#181818;color:#fff;position:relative}[_nghost-%COMP%]   ion-split-pane[_ngcontent-%COMP%]{--side-width: 400px;--side-max-width: 400px}[_nghost-%COMP%]   ion-split-pane[_ngcontent-%COMP%]   ion-menu[_ngcontent-%COMP%]{background-color:#2d2e32}[_nghost-%COMP%]   ion-split-pane[_ngcontent-%COMP%]   ion-menu[_ngcontent-%COMP%]   .side-pane-content-parent[_ngcontent-%COMP%]{--padding-bottom: 20px;--padding-end: 20px;--padding-start: 20px;--padding-top: 20px}[_nghost-%COMP%]   ion-split-pane[_ngcontent-%COMP%]   ion-menu[_ngcontent-%COMP%]   .side-pane-content-parent[_ngcontent-%COMP%]   .cancel-col[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}[_nghost-%COMP%]   ion-split-pane[_ngcontent-%COMP%]   ion-menu[_ngcontent-%COMP%]   .side-pane-content-parent[_ngcontent-%COMP%]   .cancel-col[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{margin:8px;width:20px;height:20px}[_nghost-%COMP%]   ion-split-pane[_ngcontent-%COMP%]   ion-menu[_ngcontent-%COMP%]   .side-pane-content-parent[_ngcontent-%COMP%]   .cancel-col[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}[_nghost-%COMP%]   ion-split-pane[_ngcontent-%COMP%]   ion-menu[_ngcontent-%COMP%]   .side-pane-content-parent[_ngcontent-%COMP%]   .edit-col[_ngcontent-%COMP%]   .edit-component[_ngcontent-%COMP%]{margin-top:10px}[_nghost-%COMP%]   ion-content[_ngcontent-%COMP%]{height:calc(100% - 250px)}[_nghost-%COMP%]   h1[_ngcontent-%COMP%], [_nghost-%COMP%]   h2[_ngcontent-%COMP%], [_nghost-%COMP%]   h3[_ngcontent-%COMP%], [_nghost-%COMP%]   h4[_ngcontent-%COMP%], [_nghost-%COMP%]   h5[_ngcontent-%COMP%], [_nghost-%COMP%]   h6[_ngcontent-%COMP%]{font-family:"Exo 2";text-shadow:2px 2px #000;margin:0;font-weight:800;align-self:flex-end}[_nghost-%COMP%]   h1[_ngcontent-%COMP%]{font-size:40px}[_nghost-%COMP%]   h2[_ngcontent-%COMP%]{font-size:2.5em}[_nghost-%COMP%]   h3[_ngcontent-%COMP%]{font-size:2em}[_nghost-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.8em}[_nghost-%COMP%]   h5[_ngcontent-%COMP%]{font-size:1.5em}[_nghost-%COMP%]   h6[_ngcontent-%COMP%]{font-size:1.2em}[_nghost-%COMP%]   p[_ngcontent-%COMP%]{font-family:Roboto;text-shadow:2px 2px #000}[_nghost-%COMP%]   .scormloader[_ngcontent-%COMP%]{background-color:#111;height:100%}[_nghost-%COMP%]   .scormloader[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]   .scormloader[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{text-align:center;color:#f99e00}[_nghost-%COMP%]   .full-height[_ngcontent-%COMP%]{height:100%}']})}}return o})();const no=[Be.y],to=[{path:"",component:re},{path:":id",component:re}];let io=(()=>{class o{static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275mod=e.$C({type:o})}static{this.\u0275inj=e.G2t({providers:[G.V,oe.m,Z.F],imports:[no,Y.G,K.Q,E.T,Ri,wt,I.iI.forChild(to)]})}}return o})()}}]);