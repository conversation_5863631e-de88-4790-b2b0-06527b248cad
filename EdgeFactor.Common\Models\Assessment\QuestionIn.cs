namespace EdgeFactor.Common.Models.Assessment
{
    public class QuestionIn
    {
        public Guid? Id { get; set; }
        public string? Title { get; set; }
        public Guid? Category { get; set; }
        public double? Runtime { get; set; }
        public Guid QuestionTypeId { get; set; }
        public string? QuestionText { get; set; }
        public string? QuestionDescription { get; set; }
        public int? Weighting { get; set; }
        public bool? Required { get; set; }
        public int? SortOrder { get; set; }
        public Guid? Measures { get; set; }
        public bool? IsDynamicOptions { get; set; }
        public bool? IsAuthorEditable { get; set; }
        public bool? IsRandom { get; set; }
        public bool? IsOtherOption { get; set; }
        public bool? IsLimitTo { get; set; }
        public int? Limit { get; set; }
        public bool? IsOptionPhoto { get; set; }
        public int? ScaleStart { get; set; }
        public int? ScaleEnd { get; set; }
        public string? ScaleLabelOne { get; set; }
        public string? ScaleLabelTwo { get; set; }
        public string? ScaleLabelThree { get; set; }
        public Guid? BackgroundImageAssetId { get; set; }
        public string? Purpose { get; set; }
        public Guid? DynamicOptionsParentId { get; set; }
        public string? AnswerText { get; set; }
        public Guid? MeasuredTagId { get; set; }

        public IEnumerable<QuestionAnswerIn>? QuestionAnswers { get; set; }
    }
}
