using System.Text.Json.Serialization;

namespace EdgeFactor.Common.Models.LTI
{
    public class CustomParameters
    {
        [JsonPropertyName("context_id_history")]
        public string ContextIdHistory { get; set; }

        [JsonPropertyName("context_start_date")]
        public string ContextStartDate { get; set; }

        [JsonPropertyName("context_end_date")]
        public string ContextEndDate { get; set; }

        [JsonPropertyName("resource_link_history")]
        public string ResourceLinkHistory { get; set; }

        [JsonPropertyName("resource_available_start")]
        public string ResourceAvailableStart { get; set; }

        [JsonPropertyName("resource_available_end")]
        public string ResourceAvailableEnd { get; set; }

        [JsonPropertyName("resource_submission_start")]
        public string ResourceSubmissionStart { get; set; }

        [JsonPropertyName("resource_submission_end")]
        public string ResourceSubmissionEnd { get; set; }
    }
    public class HttpsPurlImsglobalOrgSpecLtiToolConfiguration
    {
        [JsonPropertyName("domain")]
        public string Domain { get; set; }

        [Json<PERSON>ropertyName("deployment_id")]
        public string DeploymentId { get; set; }

        [JsonPropertyName("target_link_uri")]
        public string TargetLinkUri { get; set; }

        [JsonPropertyName("custom_parameters")]
        public CustomParameters CustomParameters { get; set; }

        [JsonPropertyName("claims")]
        public List<string> Claims { get; set; }

        [JsonPropertyName("messages")]
        public List<Message> Messages { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }
    }
    public class Message
    {
        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("label")]
        public string Label { get; set; }

        [JsonPropertyName("custom_parameters")]
        public MessageParameters CustomParameters { get; set; }

        [JsonPropertyName("logo_uri")]
        public string LogoUri { get; set; }
        [JsonPropertyName("target_link_uri")]
        public string TargetLinkUri { get; set; }

    }
    public class MessageParameters
    {

        [JsonPropertyName("message_type")]
        public string MessageType { get; set; }

        [JsonPropertyName("fullname_LtiResourceLinkRequest")]
        public string FullnameLtiResourceLinkRequest { get; set; }

        [JsonPropertyName("fullname_LtiDeepLinkingRequest")]
        public string FullnameLtiDeepLinkingRequest { get; set; }

        [JsonPropertyName("fullname_LtiDeepLinkingUpdateRequest")]
        public string FullnameLtiDeepLinkingUpdateRequest { get; set; }
    }
    public class Registration
    {
        public Registration() { }
        public Registration(string apiBaseUrl, string appBaseUrl)
        {

            ApplicationType = "web";
            ResponseTypes = new List<string>
            {
                "id_token"
            };
            GrantTypes = new List<string>() {
                "implicit","client_credentials"
            };

            InitiateLoginUri = $"{apiBaseUrl}/lti/login";
            RedirectUris = new List<string>() { $"{apiBaseUrl}/lti/launch" };
            ClientName = "EdgeFactor";
            PolicyURL = $"{appBaseUrl}/privacy-policy";
            TOSURL = $"{appBaseUrl}/terms-of-use";
            JwksUri = $"{apiBaseUrl}/lti/jwks";
            TokenEndpointAuthMethod = "private_key_jwt";
            HttpsPurlImsglobalOrgSpecLtiToolConfiguration = new HttpsPurlImsglobalOrgSpecLtiToolConfiguration()
            {
                // Domain = $"{new Uri(apiBaseUrl).Host}:5001",
                Domain = $"{new Uri(apiBaseUrl).Host}",
                TargetLinkUri = $"{apiBaseUrl}/lti",
                CustomParameters = new CustomParameters()
                {
                    ContextIdHistory = "$Context.id.history",
                    ContextStartDate = "$CourseSection.timeFrame.begin",
                    ContextEndDate = "$CourseSection.timeFrame.end",
                    ResourceLinkHistory = "$ResourceLink.id.history",
                    ResourceAvailableStart = "$ResourceLink.available.startDateTime",
                    ResourceAvailableEnd = "$ResourceLink.available.startDateTime",
                    ResourceSubmissionStart = "$ResourceLink.available.startDateTime",
                    ResourceSubmissionEnd = "$ResourceLink.submission.endDateTime"
                },
                Claims = new List<string>() { "sub", "iss", "name", "given_name", "family_name", "email", "picture" },

                Messages = new List<Message>() {
                    new Message() {
                        Type = "LtiDeepLinkingRequest",
                        Label = "EdgeFactor",
                        TargetLinkUri=$"{appBaseUrl}"
                    },

                    new Message() {
                        Type = "LtiResourceLinkRequest",
                        LogoUri = $"{appBaseUrl}/assets/images/Yellow_EF_Logo.png",
                        Label = "EdgeFactor",
                        CustomParameters= new MessageParameters(){
                            MessageType ="LtiResourceLinkRequest",
                            FullnameLtiResourceLinkRequest="$Person.name.full"
                        }
                    },
                    new Message() {
                        Type = "LtiDeepLinkingRequest",
                        LogoUri = $"{appBaseUrl}/assets/images/Yellow_EF_Logo.png",
                        Label = "EdgeFactor",
                        CustomParameters= new MessageParameters(){
                            MessageType ="LtiDeepLinkingRequest",
                            FullnameLtiDeepLinkingRequest="$Person.name.full"
                        }
                    },
                    new Message() {
                        Type = "LtiDeepLinkingUpdateRequest",
                        LogoUri = $"{appBaseUrl}/assets/images/Yellow_EF_Logo.png",
                        Label = "EdgeFactor",
                        CustomParameters= new MessageParameters(){
                            MessageType ="LtiDeepLinkingUpdateRequest",
                            FullnameLtiDeepLinkingUpdateRequest="$Person.name.full"
                        }
                    }
                },
                Description = "Story-Driven Career & Workforce Development Solutions"
            };
            Scope = "https://purl.imsglobal.org/spec/lti-ags/scope/lineitem https://purl.imsglobal.org/spec/lti-ags/scope/lineitem.readonly https://purl.imsglobal.org/spec/lti-ags/scope/result.readonly https://purl.imsglobal.org/spec/lti-ags/scope/score https://purl.imsglobal.org/spec/lti-nrps/scope/contextmembership.readonly https://purl.imsglobal.org/spec/lti-bo/scope/basicoutcome https://purl.imsglobal.org/spec/lti-ts/scope/toolsetting https://purl.imsglobal.org/spec/lti-reg/scope/registration https://purl.imsglobal.org/spec/lti-reg/scope/registration.readonly https://purl.imsglobal.org/spec/lti-ext/scope/outcomes https://purl.imsglobal.org/spec/lti-ext/scope/memberships https://purl.imsglobal.org/spec/lti-ext/scope/setting";
            LogoUri = $"{appBaseUrl}/assets/images/Yellow_EF_Logo.png";
        }

        [JsonPropertyName("application_type")]
        public string ApplicationType { get; set; }

        [JsonPropertyName("response_types")]
        public List<string> ResponseTypes { get; set; }

        [JsonPropertyName("grant_types")]
        public List<string> GrantTypes { get; set; }

        [JsonPropertyName("initiate_login_uri")]
        public string InitiateLoginUri { get; set; }

        [JsonPropertyName("redirect_uris")]
        public List<string> RedirectUris { get; set; }

        [JsonPropertyName("client_name")]
        public string ClientName { get; set; }

        [JsonPropertyName("client_uri")]
        public string ClientURI { get; set; }
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; }

        [JsonPropertyName("policy_uri")]
        public string PolicyURL { get; set; }
        [JsonPropertyName("tos_uri")]
        public string TOSURL { get; set; }

        [JsonPropertyName("jwks_uri")]
        public string JwksUri { get; set; }

        [JsonPropertyName("token_endpoint_auth_method")]
        public string TokenEndpointAuthMethod { get; set; }

        [JsonPropertyName("https://purl.imsglobal.org/spec/lti-tool-configuration")]
        public HttpsPurlImsglobalOrgSpecLtiToolConfiguration HttpsPurlImsglobalOrgSpecLtiToolConfiguration { get; set; }

        [JsonPropertyName("scope")]
        public string Scope { get; set; }

        [JsonPropertyName("logo_uri")]
        public string LogoUri { get; set; }
    }
}
