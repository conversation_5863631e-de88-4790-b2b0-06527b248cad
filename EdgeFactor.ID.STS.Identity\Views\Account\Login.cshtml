@using Microsoft.AspNetCore.Mvc.Localization
@using EdgeFactor.ID.STS.Identity.Configuration.Interfaces
@using EdgeFactor.ID.STS.Identity.Helpers.Localization
@inject IViewLocalizer Localizer
@model EdgeFactor.ID.STS.Identity.ViewModels.Account.LoginViewModel
@inject IRootConfiguration RootConfiguration

<div class="login-page">

    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@500;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,700&display=swap" rel="stylesheet">
    <style>
        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        textarea:-webkit-autofill,
        textarea:-webkit-autofill:hover,
        textarea:-webkit-autofill:focus,
        select:-webkit-autofill,
        select:-webkit-autofill:hover,
        select:-webkit-autofill:focus {
            border: 1px solid transparent;
            -webkit-text-fill-color: #F1F1F1;
            -webkit-box-shadow: unset;
            background: transparent;
            transition: background-color 5000s ease-in-out 0s;
            width: calc(100% - 20px);
            margin-right: 20px;
            box-sizing: border-box;
            font-family: 'Montserrat', Arial, sans-serif;
        }

        label {
            margin-bottom: 0 !important
        }

        .cc-window {
            display: none !important;
        }

        .logo {
            width: 403px;
            height: 89.22px;
            position: fixed;
            left: 56px;
            top: 42px;
        }

        .outer-container {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;

            display: flex;
            justify-content: center;
            align-items: center;
        }

        .main-container {
            width: 475px;
            height: 601px;
            border-radius: 8px;
            position: relative;
            border: 1px solid black;
            background-color: #1e1e1e;
        }

        .inner-container {
            position: absolute;
            width: auto;
            top: 1px;
            bottom: 1px;
            left: 16px;
            right: 16px;
        }

        h2 {
            height: 39px;
            color: #ffffff;
            font-family: 'Exo 2', Arial, sans-serif;
            font-weight: 800;
            font-size: 30px;
            line-height: 1.3;
            text-align: left;
            margin: 0;
            padding: 0;
            margin-left: 36px;
            margin-top: 20px;
        }

        .InputBox {
            height: 56px;
            background: #1A191B;
            width: calc(100% - 72px);
            margin-top: 17.48px;
            margin-left: 36px;
            margin-right: 36px;
            border-bottom: 2px solid #D4D3DD;
            position: relative
        }

        .InputLabel {
            margin-left: 8px;
            line-height: 22px;
            font-weight: 400;
            color: #D4D3DD;
            font-family: 'Montserrat';
            font-size: 10px;
        }

        .Username {
            all: unset;
            width: 100%;
            background: #1A191B;
            margin-left: 8px;
            font-family: 'Montserrat', Arial, sans-serif;
            font-size: 14px;
        }

        .Password {
            all: unset;
            width: 100%;
            background: #1A191B;
            margin-left: 8px;
            font-family: 'Montserrat', Arial, sans-serif;
            font-size: 14px;
        }

        .PasswordBox {
            border-bottom: 2px solid #F99E00;
        }

        .ViewPassword {
            position: absolute;
            right 20px;
        }

        #togglePassword {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            cursor: pointer;
            width: 24px;
            height: 24px;
            font-size: 24px;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-options {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-top: 20px;
            margin-left: 36px;
            margin-right: 36px;
        }

        .remember-me input[type="checkbox"] {
            margin-right: 8px;
            accent-color: #fff;
            width: 16px;
            height: 16px;
            display: inline-block;
            vertical-align: middle;
        }

        .remember-me label {
            color: #D4D3DD;
            font-size: 14px;
            font-family: 'Montserrat', Arial, sans-serif;
            display: inline-block;
            vertical-align: middle;
            line-height: 16px;
            font-weight: 400;
            height: 16px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            height: 16px;
        }

        .forgot-password-link {
            color: #EE9907;
            font-size: 13px;
            font-family: 'Roboto', Arial, sans-serif;
            font-weight: 700;
            line-height: 18px;
            letter-spacing: 0;
            text-align: right;
            text-decoration: none;
            display: inline-block;
            align-self: center;
        }

        .custom-checkbox {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
            position: relative;
            height: 24px;
        }

        .custom-checkbox input[type="checkbox"] {
            opacity: 0;
            width: 0;
            height: 0;
            position: absolute;
        }

        .checkmark {
            width: 18px;
            height: 18px;
            background-color: transparent;
            border: 2px solid #fff;
            border-radius: 3px;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, border-color 0.2s;
        }

        .custom-checkbox input[type="checkbox"]:checked~.checkmark {
            background-color: #F99E00;
            border-color: #F99E00;
        }

        .checkmark svg {
            display: none;
        }

        .custom-checkbox input[type="checkbox"]:checked~.checkmark svg {
            display: block;
        }

        .custom-checkbox-label {
            color: #C0C0C0;
            font-size: 13px;
            font-family: 'Roboto', Arial, sans-serif;
            font-weight: 400;
            line-height: 18px;
            letter-spacing: 0;
            cursor: pointer;
        }

        .login-btn {
            margin-top: 20px;
            width: calc(100% - 72px);
            margin-left: 36px;
            margin-right: 36px;
            height: 40px;
            border: none;
            border-radius: 4px;
            font-family: 'Montserrat', Arial, sans-serif;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            letter-spacing: 0;
            color: #1A191B;
            background: linear-gradient(90deg, #F99E00 0%, #FDE734 100%);
            cursor: pointer;
            transition: box-shadow 0.2s;
            box-shadow: none;
            text-align: center;
        }

        .login-btn:active,
        .login-btn:focus {
            outline: none;
            box-shadow: 0 0 0 2px #FDE73455;
        }

        .divider-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 40px;
            margin-left: 51px;
            /* 36px + 15px */
            margin-right: 51px;
            /* 36px + 15px */
            width: calc(100% - 102px);
        }

        .divider-line {
            width: 60px;
            min-width: 60px;
            max-width: 60px;
            height: 1px;
            background-color: #C0C0C0;
            opacity: 0.5;
        }

        .divider-text {
            color: #aaaaaa;
            font-size: 16px;
            font-family: 'Exo 2', Arial, sans-serif;
            font-weight: 500;
            margin: 0;
            white-space: nowrap;
            text-align: center;
            flex: 1;
        }

        .external-login-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px 30px;
            margin-top: 20px;
            margin-left: 36px;
            margin-right: 36px;
        }

        .external-login-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            color: #fff;
            background: transparent;
            border: 1px solid #555555 !important;
            border-radius: 4px;
            padding: 7px 13px;
            cursor: pointer;
            transition: border-color 0.2s, background 0.2s;
            height: 38px;
            min-width: 186.5px;
            box-sizing: border-box;
        }

        .external-login-btn img {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: inline-block;
        }

        .external-login-btn span {
            display: inline-block;
            vertical-align: middle;
        }

        .external-login-btn.google,
        .external-login-btn.facebook,
        .external-login-btn.microsoft,
        .external-login-btn.clever,
        .external-login-btn.d2l,
        .external-login-btn.logingov {
            border-color: #555555 !important;
        }

        .external-login-btn:hover {
            background: #232323;
            border-color: #F99E00;
        }

        .signup-prompt {
            margin-top: 20px;
            text-align: center;
        }

        .signup-text-normal {
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 16px;
            font-weight: 400;
            color: #cccccc;
        }

        .signup-link {
            font-family: 'Roboto', Arial, sans-serif;
            font-size: 16px;
            font-weight: 600;
            color: #F99E00;
            text-decoration: none;
            margin-left: 5px;
        }

        .signup-link:hover {
            text-decoration: underline;
        }
    </style>


    @await Html.PartialAsync("_ValidationSummary")

    @if (Model.EnableLocalLogin)
    {
        <img class="logo" src="~/images/EFLogo_White.png" />
        <div class="outer-container">
            <div class="main-container">
                <h2>Login</h2>
                <form asp-route="Login">
                    <input type="hidden" asp-for="ReturnUrl" />

                    <div class="InputBox">
                        <span class="InputLabel">Username/Email</span>
                        <input class="Username"
                            placeholder="@Localizer[LoginPolicyResolutionLocalizer.GetUserNameLocalizationKey(Model.LoginResolutionPolicy)]"
                            asp-for="Username" aria-label="Username" aria-describedby="input-username" autofocus>
                    </div>

                    <div class="InputBox PasswordBox">
                        <span class="InputLabel">Password</span>
                        <input style="padding-right:30px" id="password" type="password" class="Password"
                            placeholder="@Localizer["Password"]" aria-label="Password" aria-describedby="input-password"
                            asp-for="Password" autocomplete="off">
                        <span class="material-icons-outlined" id="togglePassword">visibility_off</span>
                        <script>
                            const togglePassword = document.querySelector('#togglePassword');
                            const password = document.querySelector('#password');

                            togglePassword.addEventListener('click', function (e) {
                                // toggle the type attribute
                                const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
                                password.setAttribute('type', type);
                                // toggle the material icon (outlined)
                                if (type === 'text') {
                                    this.textContent = 'visibility';
                                } else {
                                    this.textContent = 'visibility_off';
                                }
                            });
                        </script>
                    </div>

                    @if (Model.AllowRememberLogin)
                    {
                        <div class="login-options">
                            <label class="custom-checkbox">
                                <input type="checkbox" id="rememberMe" name="RememberLogin">
                                <span class="checkmark">
                                    <svg width="12" height="10" viewBox="0 0 12 10" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M1 5L4.5 8.5L11 2" stroke="#fff" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </span>
                                <span class="custom-checkbox-label">@Localizer["Remember"]</span>
                            </label>
                            <a href="/Account/ForgotPassword" class="forgot-password-link">@Localizer["Forgot"]?</a>
                        </div>
                    }

                    <button class="login-btn" name="button" value="login">@Localizer["Login"]</button>

                    <div class="divider-container">
                        <div class="divider-line"></div>
                        <span class="divider-text">or login with</span>
                        <div class="divider-line"></div>
                    </div>

                    @if (Model.VisibleExternalProviders.Any())
                    {
                        <div class="external-login-grid">
                            @foreach (var provider in Model.VisibleExternalProviders)
                            {
                                <button type="button" class="external-login-btn" onclick="location.href='@Url.Action("ExternalLogin", new { provider = provider.AuthenticationScheme, returnUrl = Model.ReturnUrl })'">
                                    <img src="~/images/@provider.DisplayName-logo.png" alt="@provider.DisplayName" />
                                    <span>
                                        @if (provider.DisplayName == "D2l")
                                        {
                                            @:D2L | Brightspace
                                        }
                                        else
                                        {
                                            @provider.DisplayName
                                        }
                                    </span>
                                </button>
                            }
                        </div>
                        <div class="signup-prompt">
                            <span class="signup-text-normal">Don't have an account ?</span>
                            <a href="/Account/Register" class="signup-link">SIGN UP</a>
                        </div>
                    }
                </form>
            </div>
        </div>


    }

    @if (!Model.EnableLocalLogin && !Model.VisibleExternalProviders.Any())
    {
        <div class="alert alert-warning">
            <strong>@Localizer["InvalidRequest"]</strong>
            @Localizer["NoSchema"]
        </div>
    }

</div>
