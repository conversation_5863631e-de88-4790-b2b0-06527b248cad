using EdgeFactor.Common.DB.Models;

namespace EdgeFactor.Common.Models.Template
{
    public record ComponentType(Guid Id, string? Name, bool? IsVisibleInRepositoryBuilder, bool IsDefault, bool IsInstance, Guid? AssetId, string? ParentTypeName)
    {
        public ComponentType(ComponentTypes componentType) : this(componentType.Id, componentType.Name, componentType.IsVisibleInRepositoryBuilder, componentType.IsDefault, componentType.IsInstance, componentType.AssetId, componentType?.Parent?.Name) { }
    }
}
