"use strict";(self.webpackChunkpublic=self.webpackChunkpublic||[]).push([[4793],{61017:(V,D,i)=>{i.d(D,{T:()=>R});var s=i(85359),t=i(90963),b=i(78867),n=i(26110),S=i(93953);i(68512);let R=(()=>{class f{static{this.\u0275fac=function(v){return new(v||f)}}static{this.\u0275mod=S.$C({type:f})}static{this.\u0275inj=S.G2t({imports:[n.G,s.Q,b.u,t.B,s.Q]})}}return f})()},68512:(V,D,i)=>{i.d(D,{A:()=>k});var s=i(93953),t=i(12536),b=i(86737),n=i(60177),S=i(50336),w=i(61271),R=i(69635);const f=(P,U)=>({"side-panel-input-padding":P,"sales-page-padding":U}),F=P=>({"page-margin":P});function T(P,U){if(1&P){const r=s.RV6();s.j41(0,"app-row-instance",5),s.bIt("rowContentSelected",function(g){s.eBV(r);const _=s.XpG(2);return s.Njj(_.selectedRowContent(g))}),s.k0s()}if(2&P){const r=s.XpG(2);s.Y8G("isPlayerSidePanel",r.isPlayerSidePanel)("readingMode",!0)("rowId",r.instanceSectionComponent.component.rowId)("instance",r.instance)("routeParams",r.routeParams)("searchFilter",r.searchFilter)}}function v(P,U){if(1&P){const r=s.RV6();s.j41(0,"app-row-instance",6),s.bIt("rowContentSelected",function(g){s.eBV(r);const _=s.XpG(2);return s.Njj(_.selectedRowContent(g))}),s.k0s()}if(2&P){const r=s.XpG(2);s.Y8G("isPlayerSidePanel",r.isPlayerSidePanel)("readingMode",!0)("rowId",r.instanceSectionComponent.component.rowId)("instance",r.instance)("searchFilter",r.searchFilter)("selectedUserId",r.selectedUserId)("playerSidePanel",r.playerSidePanel)("routeParams",r.routeParams)}}function I(P,U){if(1&P&&s.DNE(0,T,1,6,"app-row-instance",3)(1,v,1,8,"app-row-instance",4),2&P){let r;const y=s.XpG();s.vxM("Default Row"===(r=null==y.instanceSectionComponent||null==y.instanceSectionComponent.component||null==y.instanceSectionComponent.component.componentType?null:y.instanceSectionComponent.component.componentType.name)?0:"Smart Row"===r?1:-1)}}function C(P,U){if(1&P){const r=s.RV6();s.j41(0,"app-row-manager",7),s.bIt("updateInstanceComponentValue",function(g){s.eBV(r);const _=s.XpG();return s.Njj(_.setNewValue(g))})("rowContentSelected",function(g){s.eBV(r);const _=s.XpG();return s.Njj(_.selectedRowContent(g))}),s.k0s()}if(2&P){const r=s.XpG();s.Y8G("isPlayerSidePanel",r.isPlayerSidePanel)("instanceSectionComponent",r.instanceSectionComponent)("readingMode",!0)("searchFilter",r.searchFilter)("instance",r.instance)("componentId",r.instanceSectionComponent.component.id)("routeParams",r.routeParams)("selectedUserId",r.selectedUserId)("playerSidePanel",r.playerSidePanel)}}function E(P,U){if(1&P){const r=s.RV6();s.j41(0,"div",2)(1,"app-component-selector",8),s.bIt("updateInstanceComponentValue",function(g){s.eBV(r);const _=s.XpG();return s.Njj(_.setNewValue(g))}),s.k0s()()}if(2&P){const r=s.XpG();s.Y8G("ngClass",s.eq3(10,F,(null==r.routeParams?null:r.routeParams.viewType)===r.viewTypes.Grid&&!0!==r.instance.feature.isFullWidth)),s.R7$(),s.Y8G("instance",r.instance)("instanceSectionComponent",r.instanceSectionComponent)("instanceSection",r.instanceSection)("routeParams",r.routeParams)("searchFilter",r.searchFilter)("builderPreviewView",r.sidePanelPadding)("selectedUserId",r.selectedUserId)("isEducator",r.isEducator)("continuousFeedback",r.continuousFeedback)}}let k=(()=>{class P{constructor(){this.sidePanelPadding=!1,this.isPlayerSidePanel=!1,this.onlyRows=!1,this.isEducator=!1,this.playerSidePanel=!1,this.updateInstanceComponentValue=new s.bkB,this.rowContentSelected=new s.bkB,this.componentType=t.I,this.viewTypes=b.q}setNewValue(r){this.updateInstanceComponentValue.emit(r)}selectedRowContent(r){this.rowContentSelected.next(r)}static{this.\u0275fac=function(y){return new(y||P)}}static{this.\u0275cmp=s.VBU({type:P,selectors:[["app-component-row-selector"]],inputs:{instance:"instance",instanceSectionComponent:"instanceSectionComponent",instanceSection:"instanceSection",routeParams:"routeParams",searchFilter:"searchFilter",sidePanelPadding:"sidePanelPadding",isPlayerSidePanel:"isPlayerSidePanel",onlyRows:"onlyRows",selectedUserId:"selectedUserId",isEducator:"isEducator",playerSidePanel:"playerSidePanel",continuousFeedback:"continuousFeedback"},outputs:{updateInstanceComponentValue:"updateInstanceComponentValue",rowContentSelected:"rowContentSelected"},decls:4,vars:5,consts:[[1,"full-parent-height",3,"ngClass"],[3,"isPlayerSidePanel","instanceSectionComponent","readingMode","searchFilter","instance","componentId","routeParams","selectedUserId","playerSidePanel"],[3,"ngClass"],[3,"isPlayerSidePanel","readingMode","rowId","instance","routeParams","searchFilter"],[3,"isPlayerSidePanel","readingMode","rowId","instance","searchFilter","selectedUserId","playerSidePanel","routeParams"],[3,"rowContentSelected","isPlayerSidePanel","readingMode","rowId","instance","routeParams","searchFilter"],[3,"rowContentSelected","isPlayerSidePanel","readingMode","rowId","instance","searchFilter","selectedUserId","playerSidePanel","routeParams"],[3,"updateInstanceComponentValue","rowContentSelected","isPlayerSidePanel","instanceSectionComponent","readingMode","searchFilter","instance","componentId","routeParams","selectedUserId","playerSidePanel"],[3,"updateInstanceComponentValue","instance","instanceSectionComponent","instanceSection","routeParams","searchFilter","builderPreviewView","selectedUserId","isEducator","continuousFeedback"]],template:function(y,g){1&y&&(s.j41(0,"div",0),s.DNE(1,I,2,1)(2,C,1,9,"app-row-manager",1)(3,E,2,12,"div",2),s.k0s()),2&y&&(s.Y8G("ngClass",s.l_i(2,f,g.sidePanelPadding&&"Page Banner"!==(null==g.instanceSectionComponent.component.componentType?null:g.instanceSectionComponent.component.componentType.name),!0===g.instance.feature.isFullWidth&&"Page Banner"!==(null==g.instanceSectionComponent.component.componentType?null:g.instanceSectionComponent.component.componentType.name))),s.R7$(),s.vxM(g.instanceSectionComponent.component.rowId?1:"Row Manager"===(null==g.instanceSectionComponent||null==g.instanceSectionComponent.component||null==g.instanceSectionComponent.component.componentType?null:g.instanceSectionComponent.component.componentType.name)?2:(null!=g.instanceSectionComponent&&null!=g.instanceSectionComponent.component&&g.instanceSectionComponent.component.templateField||"Assessment Block"===(null==g.instanceSectionComponent||null==g.instanceSectionComponent.component||null==g.instanceSectionComponent.component.componentType?null:g.instanceSectionComponent.component.componentType.name))&&!1===g.onlyRows?3:-1))},dependencies:[n.YU,S.h,w.$,R.w],styles:[".side-panel-input-padding[_ngcontent-%COMP%]{padding-left:10px;padding-right:10px}.page-margin[_ngcontent-%COMP%]{margin-left:var(--page-margin-left);margin-right:var(--page-margin-right)}.sales-page-padding[_ngcontent-%COMP%]{padding-left:1.25vw;padding-right:1.25vw}"]})}}return P})()},14591:(V,D,i)=>{i.d(D,{X:()=>T});var s=i(87967),t=i(26110),b=i(93953);i(45312),i(63863);let T=(()=>{class v{static{this.\u0275fac=function(E){return new(E||v)}}static{this.\u0275mod=b.$C({type:v})}static{this.\u0275inj=b.G2t({imports:[t.G,s.D]})}}return v})()},16200:(V,D,i)=>{i.d(D,{U:()=>g});var s=i(45312),t=i(21413),b=i(56977),n=i(93953),S=i(54622),w=i(18544),R=i(6255),f=i(58135),F=i(60177),T=i(73465),v=i(68512),I=i(89524);function C(_,G){if(1&_&&(n.j41(0,"ion-row"),n.nrm(1,"app-heading-value",1),n.k0s()),2&_){const m=n.XpG(2);n.R7$(),n.Y8G("inheritedPropertyValue",m.instanceSection.section.title.length>0?m.instanceSection.section.title:"Section")("fontSize",22)}}function E(_,G){if(1&_&&(n.j41(0,"ion-row"),n.nrm(1,"app-text-value",2),n.k0s()),2&_){const m=n.XpG(2);n.R7$(),n.Y8G("defaultValue",m.instanceSection.section.description.length>0?m.instanceSection.section.description:"Description")}}function k(_,G){if(1&_&&n.nrm(0,"div",6),2&_){const m=n.XpG(4);n.Aen("--background-image:url("+m.assetUrl+");")}}function P(_,G){if(1&_&&n.nrm(0,"app-component-row-selector",5),2&_){const m=n.XpG().$implicit,x=n.XpG(3);n.Y8G("instance",x.instance)("instanceSectionComponent",m)("instanceSection",x.instanceSection)("searchFilter",x.searchFilter)("routeParams",x.routeParams)("selectedUserId",x.selectedUserId)("isEducator",x.isEducator)}}function U(_,G){if(1&_&&(n.DNE(0,k,1,2,"div",4),n.j41(1,"ion-col",3),n.DNE(2,P,1,7,"app-component-row-selector",5),n.k0s()),2&_){const m=G.$implicit,x=n.XpG().$index,$=n.XpG(2);n.vxM("Listing Details"===m.component.componentType.name&&0===$.index&&0===x&&!0!==(null==m.component||null==m.component.templateField?null:m.component.templateField.moveToBack)?0:-1),n.R7$(),n.Y8G("size",0!==(null==m.component||null==m.component.templateField?null:m.component.templateField.colspan)?null==m.component||null==m.component.templateField?null:m.component.templateField.colspan:null),n.R7$(),n.vxM(null!=m.component&&null!=m.component.templateField&&m.component.templateField.isFilter?-1:2)}}function r(_,G){if(1&_&&(n.j41(0,"ion-row"),n.Z7z(1,U,3,3,"ion-col",3,n.fX1),n.nI1(3,"orderBy"),n.k0s()),2&_){const m=G.$implicit;n.R7$(),n.Dyx(n.i5U(3,0,m,"component.templateField.colNumber"))}}function y(_,G){if(1&_&&(n.j41(0,"ion-card",0),n.DNE(1,C,2,2,"ion-row")(2,E,2,1,"ion-row"),n.j41(3,"ion-grid"),n.Z7z(4,r,4,3,"ion-row",null,n.fX1),n.nI1(6,"groupBy"),n.nI1(7,"values"),n.k0s()()),2&_){const m=n.XpG();n.Y8G("ngClass",m.instanceSection.section.hideBackground?"section-no-background":"section"),n.R7$(),n.vxM(!0===m.instanceSection.section.showTitleOnPlayer?1:-1),n.R7$(),n.vxM(!0===m.instanceSection.section.showDescOnPlayer?2:-1),n.R7$(2),n.Dyx(n.bMT(7,6,n.i5U(6,3,m.instanceSection.instanceSectionComponents,"component.builderRowNumber")))}}let g=(()=>{class _{constructor(m,x){this.eventsService=m,this.instanceService=x,this.isEducator=!1,this.componentDestroyed$=new t.B}ngOnInit(){this.setGradientImageOverlay(),this.eventsService.subscribe("assessmentReturned",m=>{this.instanceSection.instanceSectionComponents.some(x=>x.component.id===m)&&this.instanceService.checkInstanceSectionIsGraded(this.instance.id,this.instanceSection.id,this.selectedUserId).pipe((0,b.Q)(this.componentDestroyed$)).subscribe(x=>{x&&this.eventsService.publish("sectionIsGraded",this.instanceSection.id)})})}setGradientImageOverlay(){this.assetUrl=this.instance?.coverMediaAssetId?`${s.c.contentUrl}asset/${this.instance.coverMediaAssetId}/content`:"assets/images/defaultbackgroundgradient.png"}ngOnDestroy(){this.eventsService.unsubscribe("assessmentReturned"),this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(x){return new(x||_)(n.rXU(S.s),n.rXU(w.b))}}static{this.\u0275cmp=n.VBU({type:_,selectors:[["app-instance-section"]],inputs:{instanceSection:"instanceSection",instance:"instance",searchFilter:"searchFilter",routeParams:"routeParams",index:"index",selectedUserId:"selectedUserId",isEducator:"isEducator"},decls:1,vars:1,consts:[[3,"ngClass"],[3,"inheritedPropertyValue","fontSize"],[3,"defaultValue"],["col-12","","col-md-6","","col-lg-4","","col-xl-3","",3,"size"],[1,"image-header-gradient",3,"style"],[3,"instance","instanceSectionComponent","instanceSection","searchFilter","routeParams","selectedUserId","isEducator"],[1,"image-header-gradient"]],template:function(x,$){1&x&&n.DNE(0,y,8,8,"ion-card",0),2&x&&n.vxM($.instanceSection.instanceSectionComponents.length>0?0:-1)},dependencies:[R.HeadingValueComponent,f.O,F.YU,T.b_,T.hU,T.lO,T.ln,v.A,I.lU,I.Qe,I.Op],styles:["ion-card[_ngcontent-%COMP%]{box-shadow:none;--background: none;padding:0;margin:0!important;border-radius:0!important}ion-col[_ngcontent-%COMP%]{min-height:0px!important}ion-col[_ngcontent-%COMP%]:not(:last-child){padding-right:16px}.image-header-gradient[_ngcontent-%COMP%]{width:100%;background-image:linear-gradient(to bottom,#1e1e1e59 25%,#232323 75%),var(--background-image);background-size:cover;border-radius:11px;height:180px;top:0;left:0;position:absolute}"]})}}return _})()},23848:(V,D,i)=>{i.d(D,{a:()=>L});var s=i(50453),t=i(93953),b=i(86737),n=i(21413),S=i(56977),w=i(96354),R=i(6165),f=i(18544),F=i(45243),T=i(54622),v=i(15389),I=i(60177),C=i(73465),E=i(16200);const k=["itemRef"],P=h=>({display:h});function U(h,B){if(1&h){const o=t.RV6();t.j41(0,"ion-button",9),t.bIt("click",function(){t.eBV(o);const p=t.XpG(2).$implicit,d=t.XpG(2),O=t.XpG();return t.Njj(O.continue(p.id,d.id))}),t.j41(1,"span",10),t.EFF(2,"Continue"),t.k0s()()}}function r(h,B){1&h&&(t.j41(0,"ion-button",8)(1,"span",10),t.EFF(2,"Complete the previous section before moving on."),t.k0s()())}function y(h,B){1&h&&(t.j41(0,"span",10),t.EFF(1,"Complete"),t.k0s())}function g(h,B){1&h&&(t.j41(0,"span",10),t.EFF(1,"Next"),t.k0s())}function _(h,B){if(1&h){const o=t.RV6();t.j41(0,"ion-button",9),t.bIt("click",function(){t.eBV(o);const p=t.XpG(2).$implicit,d=t.XpG(2),O=t.XpG();return t.Njj(O.continue(p.id,d.id))}),t.DNE(1,y,2,0,"span",10)(2,g,2,0,"span",10),t.k0s()}if(2&h){const o=t.XpG(5);t.R7$(),t.vxM(!0===o.isLastInstanceSection?1:2)}}function G(h,B){if(1&h&&(t.j41(0,"div")(1,"div",7),t.DNE(2,U,3,0,"ion-button")(3,r,3,0,"ion-button",8)(4,_,3,1,"ion-button"),t.k0s()()),2&h){const o=t.XpG(4);t.R7$(2),t.vxM(!0===o.showContinue&&!0===o.canContinue?2:!0!==o.canContinue?3:!0===o.showContinue||!0!==o.canContinue||o.showReturnGrade()?-1:4)}}function m(h,B){if(1&h&&(t.j41(0,"div",5,1),t.nrm(2,"app-instance-section",6),t.DNE(3,G,5,1,"div"),t.k0s()),2&h){const o=B.$implicit,l=B.index,p=t.XpG(2),d=t.XpG();t.Aen(!0!==(null==o||null==o.section?null:o.section.hideBackground)&&(null!=o&&null!=o.section&&o.section.backgroundColor||null!=o&&o.backgroundColor)?"background:"+((null==o||null==o.backgroundColor?null:o.backgroundColor.length)>0?null==o?null:o.backgroundColor:null==o||null==o.section?null:o.section.backgroundColor):"background-color:none"),t.Y8G("id",o.id),t.R7$(2),t.Y8G("ngStyle",t.eq3(12,P,!1===o.show?"none":null))("instanceSection",o)("instance",p)("searchFilter",d.searchFilter)("routeParams",d.routeParams)("selectedUserId",d.selectedUserId)("isEducator",d.isEducator)("index",l),t.R7$(),t.vxM(null!=o.section&&o.section.isCompletable&&d.lastComponent(l)?3:-1)}}function x(h,B){if(1&h){const o=t.RV6();t.j41(0,"cdk-virtual-scroll-viewport",3,0),t.bIt("scrolledIndexChange",function(p){t.eBV(o);const d=t.XpG(2);return t.Njj(d.onScrolledIndexChange(p))}),t.DNE(2,m,4,14,"div",4),t.k0s()}if(2&h){const o=t.XpG(2);t.Y8G("itemSize",1),t.R7$(2),t.Y8G("cdkVirtualForOf",o.instanceSectionsToDisplay())}}function $(h,B){if(1&h&&t.DNE(0,x,3,2,"cdk-virtual-scroll-viewport",2),2&h){const o=t.XpG();t.vxM(o.instanceSectionsToDisplay()?0:-1)}}let L=(()=>{class h{constructor(o,l,p,d,O){this.systemPropertyService=o,this.instanceService=l,this.dataService=p,this.eventsService=d,this.authService=O,this.displayShowMore=!1,this.showInstanceHeader=!0,this.isViewField=!1,this.isHoverField=!1,this.isPreviewField=!1,this.isRequiredField=!1,this.finishedInstance=new t.bkB,this.visibleItems=[],this.instanceSectionsToDisplay=(0,t.vPA)([]),this.componentDestroyed$=new n.B,this.controlBackground="none",this.height=0,this.windowResize=!1,this.shouldScroll=!1,this.showReturnGrade=(0,t.EWP)(()=>this.instanceSectionsToDisplay().some(j=>j.instanceSectionComponents.some(X=>X.component.question))&&!0===this.isEducator),this.isLastInstanceSection=!1,this.canContinue=!1,this.showContinue=!1}set items(o){this.itemRefs=o.toArray()}onResize(){this.windowResize=!0}ngOnChanges(o){o.selectedUserId&&this.setupData()}getGridRouteParams(){return{...this.routeParams,viewType:b.q.Grid}}ngOnInit(){this.getInstance(),this.instanceService.reload$.pipe((0,S.Q)(this.componentDestroyed$)).subscribe(()=>{this.getInstance(),this.getInstanceSections()}),this.eventsService.subscribe("instanceIsGraded",o=>{this.instance.id===o&&(this.isGraded=!0,this.finishedInstance.emit({status:this.status,containsGrading:this.containsGrading,isGraded:this.isGraded,shouldNavigate:!1}))}),this.eventsService.subscribe("isLastSection",()=>{this.isLastInstanceSection=!0})}setupData(){this.getInstanceSections(),this.scrollToIndex(),this.instanceSectionComponentCompleted()}getInstance(){this.instance$=this.instanceService.getInstance(this.routeParams?.instanceSlug??this.routeParams?.featureSlug).pipe((0,w.T)(o=>(this.instance=o,o)))}scrollToIndex(){this.eventsService.subscribe("scrollToIndex",o=>{this.scroll(o)})}lastComponent(o){return o===this.instanceSectionsToDisplay().length-1}instanceSectionComponentCompleted(){this.eventsService.subscribe("instanceSectionComponentCompleted",o=>{const l=this.instanceSectionsToDisplay().find(d=>d.instanceSectionComponents.some(O=>O.id===o))?.id,p=this.instanceSectionsToDisplay().find(d=>d.id===l);if(p){const d=p.instanceSectionComponents.findIndex(j=>j.id===o);-1!==d&&(p.instanceSectionComponents[d].completed=!0);const O=this.instanceSectionsToDisplay().findIndex(j=>j.id===p.id);-1!==O&&(this.setCanContinue(this.instanceSectionsToDisplay()[O].instanceSectionComponents),this.setShowContinue(O))}return!0})}scroll(o){const l=this.instanceSectionsToDisplay().findIndex(d=>d.id===o),p=this.itemRefs[l]?.nativeElement.offsetTop;p&&p>-1&&this.viewport.first.scrollToIndex(p),this.instanceSectionsToDisplay()?.length>0&&-1!=l&&(this.setCanContinue(this.instanceSectionsToDisplay()[l].instanceSectionComponents),this.setShowContinue(l))}onScrolledIndexChange(o){const l=this.viewport.first.measureScrollOffset();let p=Number.MAX_VALUE,d=-1;this.itemRefs.forEach((j,X)=>{const z=Math.abs(l+20-j.nativeElement.offsetTop);z<p&&(p=z,d=X)});const O=this.instanceSectionsToDisplay()[d]?.id;this.eventsService.publish("activeScrolled",O),0===o&&!0===this.shouldScroll&&(this.shouldScroll=!1,setTimeout(()=>{this.scroll(this.instanceSectionsToDisplay()[this.lastItemIndex].id)},500)),-1!=d&&(this.setCanContinue(this.instanceSectionsToDisplay()[d].instanceSectionComponents),this.setShowContinue(d))}getInstanceSections(){this.instanceSectionsToDisplay.set([]),this.instanceService.getInstanceSectionsAndComponents(this.routeParams?.instanceSlug??this.routeParams?.featureSlug??"",this.selectedUserId,this.routeParams.viewType===b.q.Player||this.isViewField,this.isHoverField,this.isPreviewField,this.isRequiredField).pipe((0,S.Q)(this.componentDestroyed$)).subscribe(o=>{o&&(this.status=o.status,this.containsGrading=o.containsGrading,this.isGraded=o.isGraded,this.instanceSectionsResult=o.instanceSections,this.instanceSectionsCount=this.instanceSectionsResult.length,this.instanceSectionsResult[0]?.section&&(this.endIndex=this.instanceSectionsResult.findIndex(l=>!1===l.complete&&!0===l.section.isCompletable),-1===this.endIndex?this.instanceSectionsToDisplay.set(this.instanceSectionsResult):0===this.endIndex?this.instanceSectionsToDisplay.set([...this.instanceSectionsToDisplay(),this.instanceSectionsResult[0]]):0===this.instanceSectionsToDisplay().length&&this.instanceSectionsToDisplay.set([...this.instanceSectionsResult.slice(0,-1!==this.endIndex?this.endIndex+1:this.instanceSectionsCount+1)]),this.setSectionsToShow(this.instanceSectionsToDisplay())),this.finishedInstance.emit({status:this.status,containsGrading:this.containsGrading,isGraded:this.isGraded,shouldNavigate:!1}))})}setSectionsToShow(o,l=!1){this.lastItemIndex=0,this.shouldScroll=l,this.instanceSectionsToDisplay.update(!1===this.shouldScroll?p=>p.map((d,O)=>this.setSectionDisplay(o,d,O)):p=>p.concat(o).map((d,O)=>this.setSectionDisplay(o,d,O)))}setSectionDisplay(o,l,p){return o.find(O=>O.id===l.id)?(l.show=!0,this.lastItemIndex=p):l.show||(l.show=!1),l}getSystemPropertyValue(o){if(o){if(this.isHoverField)return o.value??null;const l=this.systemPropertyService.getSystemPropertyValue(o);if(null!=l)return l}return null}setCanContinue(o){return this.canContinue=!o.some(l=>!0===l.component?.templateField?.isRequiredField)||!0===this.instance.isComplete||o.filter(l=>!0===l.component?.templateField?.isRequiredField).some(l=>!0===l.completed||void 0===l.component?.question||!0===l.component?.question?.userAnswer?.isSubmitted),this.canContinue}continue(o,l){const p=this.instanceSectionsToDisplay().findIndex(d=>d.id===o);this.setCanContinue(this.instanceSectionsToDisplay()[p].instanceSectionComponents)&&(this.instanceSectionsResult[p]&&!0===this.instanceSectionsResult[p].section.isCompletable&&(this.updateInstanceSection(this.instanceSectionsResult[p],l),this.eventsService.publish("instanceInProgress",l)),!1===this.setShowContinue(p)&&(this.status="Completed",this.dataService.checkInstance(this.instance.id,this.selectedUserId??this.authService.userContext?.userId).pipe((0,S.Q)(this.componentDestroyed$)).subscribe(()=>{this.finishedInstance.emit({status:this.status,shouldNavigate:!1}),this.eventsService.publish("instanceCompleted",l)})),this.addNextContinueSections())}setShowContinue(o){return this.showContinue=o<this.instanceSectionsResult.filter(l=>l.instanceSectionComponents?.length>0).length-1,this.showContinue}addNextContinueSections(){const o=this.endIndex;this.endIndex=this.instanceSectionsResult.findIndex((p,d)=>d>o&&!1===p.complete&&!0===p.section.isCompletable);const l=this.instanceSectionsResult.slice(o+1,-1!==this.endIndex?this.endIndex+1:this.instanceSectionsCount+1);this.setSectionsToShow(l,!0)}updateInstanceSection(o,l){this.dataService.updateInstanceSection({id:o?.id,instanceId:l}).subscribe(d=>{d&&this.eventsService.publish("instanceSectionCompleted",o?.id)})}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(l){return new(l||h)(t.rXU(R.a),t.rXU(f.b),t.rXU(F.u),t.rXU(T.s),t.rXU(v.u))}}static{this.\u0275cmp=t.VBU({type:h,selectors:[["app-instance-sections-and-components"]],viewQuery:function(l,p){if(1&l&&(t.GBs(s.d6,5),t.GBs(k,5)),2&l){let d;t.mGM(d=t.lsd())&&(p.viewport=d),t.mGM(d=t.lsd())&&(p.items=d)}},hostBindings:function(l,p){1&l&&t.bIt("resize",function(O){return p.onResize(O)},!1,t.tSv)},inputs:{containerTop:"containerTop",featureName:"featureName",featureDescription:"featureDescription",routeParams:"routeParams",displayShowMore:"displayShowMore",showInstanceHeader:"showInstanceHeader",coverAssetId:"coverAssetId",iconAssetId:"iconAssetId",selectedUserId:"selectedUserId",isEducator:"isEducator",hasAdminAccess:"hasAdminAccess",hideAddButton:"hideAddButton",searchFilter:"searchFilter",isViewField:"isViewField",isHoverField:"isHoverField",isPreviewField:"isPreviewField",isRequiredField:"isRequiredField"},outputs:{finishedInstance:"finishedInstance"},features:[t.OA$],decls:2,vars:3,consts:[["viewport",""],["itemRef",""],[3,"itemSize"],[3,"scrolledIndexChange","itemSize"],[3,"id","style",4,"cdkVirtualFor","cdkVirtualForOf"],[3,"id"],[3,"ngStyle","instanceSection","instance","searchFilter","routeParams","selectedUserId","isEducator","index"],[1,"button-container"],[1,"disable-button"],[3,"click"],[1,"button-text"]],template:function(l,p){if(1&l&&(t.DNE(0,$,1,1),t.nI1(1,"async")),2&l){let d;t.vxM((d=t.bMT(1,1,p.instance$))?0:-1,d)}},dependencies:[s.yg,s.E$,s.d6,I.B3,C.Jm,E.U,I.Jj],styles:["cdk-virtual-scroll-viewport[_ngcontent-%COMP%]{height:100%}.button-container[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:20px 20px 45px;width:100%}.button-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:60%;max-width:600px;min-width:100px;--background: #f99e00;border-color:#aa671d 1px solid;border-radius:4px;text-transform:none}.button-container[_ngcontent-%COMP%]   .button-text[_ngcontent-%COMP%]{color:#000;font-family:Roboto;font-weight:500;font-size:14px;line-height:1;letter-spacing:.7px}.button-container[_ngcontent-%COMP%]   .disable-button[_ngcontent-%COMP%]{--background: #68676c !important}@media (min-width: 480px) and (max-width: 768px){.button-container[_ngcontent-%COMP%]{padding:10px!important}.button-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:80%!important}.button-container[_ngcontent-%COMP%]   .button-text[_ngcontent-%COMP%]{letter-spacing:.1px!important}}@media (max-width: 479px){.button-container[_ngcontent-%COMP%]{padding:10px!important}.button-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{width:100%!important}.button-container[_ngcontent-%COMP%]   .button-text[_ngcontent-%COMP%]{font-size:12px!important;letter-spacing:.1px!important}}  .cdk-virtual-scroll-content-wrapper{position:relative!important}"]})}}return h})()},28228:(V,D,i)=>{i.d(D,{j:()=>F});var s=i(50453),t=i(26110),b=i(61017),n=i(36885),S=i(93953);i(23848),i(16200),i(58135);let F=(()=>{class T{static{this.\u0275fac=function(C){return new(C||T)}}static{this.\u0275mod=S.$C({type:T})}static{this.\u0275inj=S.G2t({imports:[s.E9,t.G,n.h,b.T,b.T]})}}return T})()},94862:(V,D,i)=>{i.d(D,{y:()=>_n});var s=i(10467),t=i(49969),b=i(60177),n=i(93953),S=i(83084),w=i(86737),R=i(17763),f=i(73465),F=i(21413),T=i(14823),v=i(34833),I=i(7838),C=i(18544),E=i(74710);const k=c=>({"no-header-container":c}),P=(c,M)=>({"back-btn":c,"back-btn-large":M}),U=c=>({"no-header-breadcrumbs":c}),r=c=>({"header-included":c});function y(c,M){if(1&c){const e=n.RV6();n.j41(0,"ion-fab-button",3),n.bIt("click",function(){n.eBV(e);const u=n.XpG(2);return n.Njj(u.goBack())}),n.nrm(1,"ion-icon",4),n.k0s()}if(2&c){const e=n.XpG(2);n.Y8G("ngClass",n.l_i(2,P,!0!==e.onlyContent,!0===e.onlyContent))("color","dark")}}function g(c,M){1&c&&n.nrm(0,"ion-icon",7)}function _(c,M){if(1&c){const e=n.RV6();n.j41(0,"ion-breadcrumb",5),n.bIt("click",function(u){const A=n.eBV(e),W=A.$implicit,K=A.$index,H=n.XpG(3);return n.Njj(H.goToInstance(W,K,u))}),n.j41(1,"span",6),n.EFF(2),n.k0s()(),n.DNE(3,g,1,0,"ion-icon",7)}if(2&c){const e=M.$implicit,a=M.$index,u=n.XpG(3);n.Y8G("active",a+1===u.breadcrumbs.length)("ngClass",n.eq3(5,r,u.hasPageTitle)),n.R7$(),n.Y8G("matTooltip",e.name),n.R7$(),n.JRh(e.name),n.R7$(),n.vxM(a>=u.breadcrumbs.length-3&&a+1!==u.breadcrumbs.length?3:-1)}}function G(c,M){if(1&c&&(n.j41(0,"ion-breadcrumbs",2),n.Z7z(1,_,4,7,null,null,n.fX1),n.k0s()),2&c){const e=n.XpG(2);n.Y8G("ngClass",n.eq3(4,U,!e.hasPageTitle))("maxItems",3)("itemsBeforeCollapse",0)("itemsAfterCollapse",2),n.R7$(),n.Dyx(e.breadcrumbs)}}function m(c,M){if(1&c&&(n.j41(0,"div",0),n.DNE(1,y,2,5,"ion-fab-button",1)(2,G,3,6,"ion-breadcrumbs",2),n.k0s()),2&c){const e=n.XpG();n.Y8G("ngClass",n.eq3(3,k,!e.hasPageTitle)),n.R7$(),n.vxM(e.breadcrumbs.length>0||e.routeParams.viewType===e.viewTypes.Player||!0===e.onlyContent?1:-1),n.R7$(),n.vxM(e.breadcrumbs&&e.breadcrumbs.length>0?2:-1)}}let x=(()=>{class c{constructor(e,a,u,A){this.layoutService=e,this.breadcrumbService=a,this.instanceService=u,this.router=A,this.onlyContent=!1,this.viewTypes=w.q,this.isMobile=!1}ngOnInit(){"xs"===this.layoutService.currentScreenSize&&(this.isMobile=!0)}goToInstance(e,a,u){u.stopPropagation(),a+1!==this.breadcrumbs.length||0===a||null!==e.orgId?this.openInstance(e):this.goBack()}openInstance(e){this.breadcrumbService.removeBreadCrumb(e.id),this.instanceService.openInstance(e.url,null,null,null,null,this.breadcrumbService.hardRefresh,!0)}goBack(){if(this.routeParams.viewType===w.q.Grid){if(this.breadcrumbs.length>0){let a=this.breadcrumbs[this.breadcrumbs.length-1];return this.breadcrumbService.removeBreadCrumb(a.id),void this.instanceService.openInstance(a.url,null,null,null,null,this.breadcrumbService.hardRefresh,!0)}return void(location.pathname.split("/").length<3?this.router.navigate(["/"]):this.instanceService.location.back())}this.routeParams.viewType=w.q.Grid;const e=this.instanceService.getPrevInstanceSlug();this.routeParams.instanceSlug=e?.length>0?e:this.routeParams.instanceSlug,this.instanceService.openInstance(this.routeParams.featureSlug,e?.length>0?e:this.routeParams.instanceSlug,this.routeParams.tabName,"grid")}static{this.\u0275fac=function(a){return new(a||c)(n.rXU(v.Y),n.rXU(I.b),n.rXU(C.b),n.rXU(E.Ix))}}static{this.\u0275cmp=n.VBU({type:c,selectors:[["app-breadcrumbs"]],inputs:{breadcrumbs:"breadcrumbs",isScorm:"isScorm",routeParams:"routeParams",onlyContent:"onlyContent",hasPageTitle:"hasPageTitle"},standalone:!0,features:[n.aNF],decls:1,vars:1,consts:[[1,"breadcrumb-container",3,"ngClass"],["size","small","aria-label","Go Back",3,"ngClass","color"],["color","light",1,"breadcrum-margin",3,"ngClass","maxItems","itemsBeforeCollapse","itemsAfterCollapse"],["size","small","aria-label","Go Back",3,"click","ngClass","color"],["name","arrow-back-outline"],[3,"click","active","ngClass"],[1,"breadcrumb-text",3,"matTooltip"],["name","chevron-forward-outline",1,"forward-slash"]],template:function(a,u){1&a&&n.DNE(0,m,3,5,"div",0),2&a&&n.vxM(u.isScorm?-1:0)},dependencies:[f.bv,f.a6,f.dQ,f.YW,f.iq,T.oV,b.MD,b.YU],styles:[".breadcrumb-container[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:flex-start}.breadcrumb-container[_ngcontent-%COMP%]   ion-breadcrumbs[_ngcontent-%COMP%]{padding-left:12px;background-color:#333;border-radius:6px;padding-right:12px;font-size:.8em!important;letter-spacing:.03em;border:.5px solid #555555;margin-top:10px;margin-left:2px;color:#ccc;padding-top:1px}.breadcrumb-container[_ngcontent-%COMP%]   ion-breadcrumb[_ngcontent-%COMP%]{cursor:pointer;line-height:1!important}.breadcrumb-container[_ngcontent-%COMP%]   ion-breadcrumb[_ngcontent-%COMP%]::part(separator){display:none!important}.breadcrumb-container[_ngcontent-%COMP%]   ion-breadcrumb[_ngcontent-%COMP%]::part(native){padding-inline:0px!important}.breadcrumb-container[_ngcontent-%COMP%]   .header-included[_ngcontent-%COMP%]::part(native){padding-inline:0px!important;padding-top:5px;padding-bottom:5px}.breadcrumb-container[_ngcontent-%COMP%]   .forward-slash[_ngcontent-%COMP%]{margin:4px 7.5px}.breadcrumb-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{border:2px solid #848484;margin-left:0;height:40px;width:40px;border-radius:25px}.breadcrumb-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:20px;height:20px}.breadcrumb-container[_ngcontent-%COMP%]   .back-btn-large[_ngcontent-%COMP%]{margin-left:0;width:40px;height:40px;border:2px solid #848484;border-radius:25px}.breadcrumb-container[_ngcontent-%COMP%]   .back-btn-large[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:20px;height:20px}@media (max-width: 960px){.breadcrumb-container[_ngcontent-%COMP%]   .breadcrum-margin[_ngcontent-%COMP%]{margin-top:10px}.breadcrumb-container[_ngcontent-%COMP%]   ion-breadcrumb[_ngcontent-%COMP%]{font-size:.8em!important}}@media (max-width: 500px){.breadcrumb-container[_ngcontent-%COMP%]   .breadcrumb-text[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100px}}@media (max-width: 400px){.breadcrumb-container[_ngcontent-%COMP%]   .breadcrumb-text[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:50px}}@media (max-width: 960px){.breadcrumb-container[_ngcontent-%COMP%]{margin-top:10px}}.no-header-container[_ngcontent-%COMP%]{align-items:center;margin-top:10px}.no-header-breadcrumbs[_ngcontent-%COMP%]{margin-top:0!important}"]})}}return c})();var $=i(65423),L=i(11751),h=i(84111),B=i(18121),o=i(38694),l=i(14310),p=i(16372),d=i(45243),O=i(6165),j=i(15389),X=i(94821);const N=(c,M,e,a)=>({sticky:c,minimized:M,"video-header":e,"video-header-min":a}),z=c=>({"--background-image":c}),Y=c=>({"grid-view-color":c});function Q(c,M){if(1&c&&(n.j41(0,"video",1),n.nrm(1,"source",8),n.k0s()),2&c){const e=n.XpG(2);n.R7$(),n.Y8G("src",e.coverPhotoUrl,n.B4B)}}function J(c,M){if(1&c&&(n.j41(0,"ion-col",9),n.nrm(1,"img",13),n.k0s()),2&c){const e=n.XpG(3);n.Y8G("ngClass",e.minimized?"icon-col-min-org":"icon-col-org"),n.R7$(),n.Y8G("src",e.iconUrl,n.B4B)}}function Z(c,M){if(1&c&&n.nrm(0,"app-join-code",10),2&c){const e=n.XpG(3);n.Y8G("joinCode",e.instance.joinCode)("featureTypeName",null==e.instance||null==e.instance.feature||null==e.instance.feature.featureType?null:e.instance.feature.featureType.name)}}function q(c,M){if(1&c&&(n.j41(0,"span"),n.EFF(1),n.k0s()),2&c){let e;const a=n.XpG(3);n.R7$(),n.SpI(" ",null!==(e=null==a.authService||null==a.authService.guestUserContext?null:a.authService.guestUserContext.browsingAs)&&void 0!==e?e:null==a.authService.userContext?null:a.authService.userContext.fullName,"'s ")}}function nn(c,M){if(1&c&&(n.nrm(0,"span",12),n.nI1(1,"parsePipe"),n.nI1(2,"async")),2&c){let e;const a=n.XpG(3);n.Y8G("innerHTML",n.bMT(2,6,n.ii3(1,1,null!==(e=null==a.instance||null==a.instance.feature?null:a.instance.feature.descriptors)&&void 0!==e?e:"",a.instance.id,null,!0)),n.npT)}}function en(c,M){if(1&c&&(n.j41(0,"h6"),n.EFF(1),n.nI1(2,"parsePipe"),n.nI1(3,"async"),n.k0s()),2&c){let e;const a=n.XpG(3);n.R7$(),n.JRh(n.bMT(3,4,n.i5U(2,1,null!==(e=null==a.instance?null:a.instance.description)&&void 0!==e?e:"",null==a.instance?null:a.instance.id)))}}function tn(c,M){if(1&c&&(n.j41(0,"ion-row",4),n.DNE(1,J,2,2,"ion-col",9),n.j41(2,"ion-col"),n.DNE(3,Z,1,2,"app-join-code",10),n.j41(4,"div",11)(5,"div")(6,"h1"),n.DNE(7,q,2,1,"span"),n.EFF(8),n.nI1(9,"parsePipe"),n.nI1(10,"async"),n.k0s()(),n.DNE(11,nn,3,8,"span",12)(12,en,4,6,"h6"),n.k0s()()()),2&c){let e;const a=n.XpG(2);n.R7$(),n.vxM(a.isOrgManager&&a.iconUrl?1:-1),n.R7$(2),n.vxM(!0===a.instance.isJoinCodeInstance&&!0!==a.instance.isDefault&&"Landing Pages"!==(null==a.instance||null==a.instance.feature||null==a.instance.feature.featureType?null:a.instance.feature.featureType.name)?3:-1),n.R7$(),n.Y8G("ngClass",a.isOrgManager?"header-container org":"header-container"),n.R7$(3),n.vxM("Favorites"===a.instance.feature.featureType.name||"Portfolio"===a.instance.feature.featureType.name?7:-1),n.R7$(),n.SpI(" ",n.bMT(10,10,n.i5U(9,7,null!==(e=null==a.instance?null:a.instance.title)&&void 0!==e?e:"",null==a.instance?null:a.instance.id))," "),n.R7$(3),n.vxM(!0!==a.minimized&&!0===a.isOrgManager?11:-1),n.R7$(),n.vxM(!0!==a.minimized&&!0!==a.isOrgManager?12:-1)}}function on(c,M){if(1&c){const e=n.RV6();n.j41(0,"div",5)(1,"ion-button",14),n.bIt("click",function(){n.eBV(e);const u=n.XpG(2);return n.Njj(u.openBuilder(u.instance.id))}),n.nrm(2,"ion-icon",15),n.EFF(3," Edit "),n.k0s()()}}function an(c,M){1&c&&n.eu8(0)}function cn(c,M){if(1&c){const e=n.RV6();n.j41(0,"div",0)(1,"div"),n.DNE(2,Q,2,1,"video",1),n.j41(3,"div",2),n.nrm(4,"app-breadcrumbs",3),n.DNE(5,tn,13,12,"ion-row",4)(6,on,4,0,"div",5),n.k0s()()(),n.j41(7,"ion-content",6),n.bIt("ionScroll",function(u){n.eBV(e);const A=n.XpG();return n.Njj(A.onWindowScroll(u))}),n.DNE(8,an,1,0,"ng-container",7),n.k0s()}if(2&c){const e=n.XpG();n.Y8G("@fade",e.minimized)("ngClass",n.ziG(18,N,!0===e.minimized&&!0!==e.isCoverVideo&&!0!==e.isPlayerView,!0===e.minimized,!0===e.isCoverVideo&&!0!==e.isPlayerView,!0===e.minimized&&!0===e.isCoverVideo&&!0!==e.isPlayerView))("ngStyle",n.eq3(23,z,!e.isPlayerView&&e.coverPhotoUrl||e.isCoverVideo?"url("+e.coverPhotoUrl+")":"none")),n.R7$(),n.AVh("page-margins-player",e.viewType===e.viewTypes.Player)("page-margins",e.viewType!==e.viewTypes.Player),n.R7$(),n.vxM(e.isCoverVideo&&!0!==e.isPlayerView?2:-1),n.R7$(2),n.Y8G("breadcrumbs",e.breadcrumbService.getBreadcrumbs(e.viewType))("hasPageTitle",e.viewType!==e.viewTypes.Player)("isScorm",e.isScorm)("routeParams",e.routeParams)("onlyContent",e.onlyContent),n.R7$(),n.vxM(e.viewType!==e.viewTypes.Player?5:-1),n.R7$(),n.vxM((e.hasEditAccess()||e.hasAdminAccess())&&("Instance Builder"===(null==e.featureTab||null==e.featureTab.type?null:e.featureTab.type.name)&&e.viewType!==e.viewTypes.Builder||"ASSIGNMENTS"===(null==e.featureTab||null==e.featureTab.tab?null:e.featureTab.tab.name)&&e.viewType!==e.viewTypes.Builder||("Accredited Learning Container Pages"===e.instance.feature.featureType.name||"Modifiable Learning Container Pages"===e.instance.feature.featureType.name)&&!0!==e.instance.isDefault)?6:-1),n.R7$(),n.Y8G("ngClass",n.eq3(25,Y,e.isGridView))("scrollEvents",!0),n.R7$(),n.Y8G("ngTemplateOutlet",e.content)}}function sn(c,M){if(1&c&&n.nrm(0,"app-breadcrumbs",17),2&c){const e=n.XpG(2);n.Y8G("hasPageTitle",e.viewType!==e.viewTypes.Player)("onlyContent",e.onlyContent)("breadcrumbs",e.breadcrumbService.getBreadcrumbs(e.viewType))("isScorm",e.isScorm)("routeParams",e.routeParams)}}function rn(c,M){if(1&c){const e=n.RV6();n.j41(0,"ion-segment-button",22),n.bIt("click",function(){const u=n.eBV(e).$index,A=n.XpG(3);return n.Njj(A.tabChanged(u))}),n.j41(1,"ion-label"),n.EFF(2),n.k0s()()}if(2&c){const e=M.$implicit;n.Y8G("value",M.$index),n.R7$(2),n.JRh(e.tab.name)}}function ln(c,M){if(1&c&&(n.j41(0,"div",18)(1,"ion-segment",20),n.Z7z(2,rn,3,2,"ion-segment-button",21,n.Vm6),n.k0s()()),2&c){const e=n.XpG(2);n.Y8G("ngClass",e.viewType!==e.viewTypes.Builder?"center-row-fab":""),n.R7$(),n.Y8G("value",e.selectedIndex()),n.R7$(),n.Dyx(e.featureTabs)}}function dn(c,M){if(1&c){const e=n.RV6();n.j41(0,"div",5)(1,"ion-button",14),n.bIt("click",function(){n.eBV(e);const u=n.XpG(2);return n.Njj(u.openBuilder(u.instance.id))}),n.nrm(2,"ion-icon",15),n.EFF(3," Edit "),n.k0s()()}}function pn(c,M){1&c&&n.eu8(0)}function mn(c,M){if(1&c&&(n.j41(0,"div",16),n.DNE(1,sn,1,5,"app-breadcrumbs",17)(2,ln,4,2,"div",18)(3,dn,4,0,"div",5),n.k0s(),n.qex(4,19),n.DNE(5,pn,1,0,"ng-container",7),n.bVm()),2&c){const e=n.XpG();n.R7$(),n.vxM(e.viewType!==e.viewTypes.Builder?1:-1),n.R7$(),n.vxM(e.featureTabs.length>1?2:-1),n.R7$(),n.vxM((e.hasEditAccess()||e.hasAdminAccess())&&("Instance Builder"===(null==e.featureTab||null==e.featureTab.type?null:e.featureTab.type.name)&&e.viewType!==e.viewTypes.Builder||"ASSIGNMENTS"===(null==e.featureTab||null==e.featureTab.tab?null:e.featureTab.tab.name)&&e.viewType!==e.viewTypes.Builder)?3:-1),n.R7$(2),n.Y8G("ngTemplateOutlet",e.content)}}let _n=(()=>{class c extends $.d{constructor(e,a,u,A,W,K,H,gn,un,hn){super(a,e,u,A),this.modalController=W,this.authService=K,this.rolesService=H,this.instanceService=gn,this.breadcrumbService=un,this.router=hn,this.onlyContent=!1,this.selectedIndex=(0,n.vPA)(0),this.minimized=!1,this.refresh=new F.B,this.timeLeft=1,this.scrollHeight=0,this.viewTypes=w.q,this.isScorm=!1}get isGridView(){return this.viewType===w.q.Grid}get isPlayerView(){return this.viewType===w.q.Player}ngOnInit(){if(this.isScorm=-1!==this.router.url.indexOf("scorm"),this.setIconAndCoverUrl(),this.routeParams?.viewType===w.q.Player){const e=document.getElementById("ionContent")?.scrollTop||0;this.minimized=e>0}else this.minimized=!1}ngOnChanges(e){(e.routeParams||e.viewType)&&this.routeParams?.viewType!==w.q.Player&&(this.minimized=!1)}hasAdminAccess(){return this.rolesService.hasRoleAccess([S.Q.Manage,S.Q.Publish])}hasEditAccess(){return this.rolesService.hasRoleAccess(0===this.featureTab?.featureTabEditActions?.length?[S.Q.Publish,S.Q.Assign,S.Q.Manage]:this.featureTab?.featureTabEditActions?.map(e=>e.actionBw))}openBuilder(e){var a=this;return(0,s.A)(function*(){if("Accredited Learning Container Pages"!==a.instance.feature.featureType.name&&"Modifiable Learning Container Pages"!==a.instance.feature.featureType.name||a.viewType===a.viewTypes.Player)a.instanceService.openInstance("instance",e,null,"builder",null,!1);else{const u=yield a.modalController.create({component:B.u,componentProps:{selectedInstance:a.instance,instanceId:a.instance.id,featureTypeName:a.instance?.feature?.featureType?.name,actionType:"Accredited Learning Container Pages"===a.instance.feature.featureType.name?o.w.EditAssignment:o.w.EditClass,reload:!0},cssClass:"my-instances-dialog"});u.onDidDismiss().then(A=>{!0===A.data&&a.dataService.reload$.next(null)}),yield u.present()}})()}onWindowScroll(e){var a=this;return(0,s.A)(function*(){a.minimized=e.detail.scrollTop>0&&a.viewType!==w.q.Player})()}tabChanged(e){this.selectedIndex.set(e)}static{this.\u0275fac=function(a){return new(a||c)(n.rXU(l.V),n.rXU(p.V),n.rXU(d.u),n.rXU(O.a),n.rXU(h.W3),n.rXU(j.u),n.rXU(X.P),n.rXU(C.b),n.rXU(I.b),n.rXU(E.Ix))}}static{this.\u0275cmp=n.VBU({type:c,selectors:[["app-page-header"]],inputs:{content:"content",scrollPosition:"scrollPosition",viewType:"viewType",onlyContent:"onlyContent",routeParams:"routeParams",featureTab:"featureTab",featureTabs:"featureTabs",selectedIndex:"selectedIndex"},standalone:!0,features:[n.Jv_([h.W3]),n.Vt3,n.OA$,n.aNF],decls:2,vars:2,consts:[[1,"image-container",3,"ngClass","ngStyle"],["id","autoplay-vid-cover","autoplay","","loop","","muted","","preload","metadata","playsinline","",1,"bg-video"],[1,"container"],[3,"breadcrumbs","hasPageTitle","isScorm","routeParams","onlyContent"],[1,"header-container-parent"],[1,"edit-button"],["id","ionContent",3,"ionScroll","ngClass","scrollEvents"],[4,"ngTemplateOutlet"],["type","video/mp4",3,"src"],[1,"icon-col-main-org",3,"ngClass"],[3,"joinCode","featureTypeName"],[3,"ngClass"],[1,"sub-heading",3,"innerHTML"],["alt","Logo Icon","onerror","this.src=''; this.style.display='none'; this.style.backgroundColor='white';",1,"icon-org",3,"src"],["fill","clear",1,"inner-container",3,"click"],["name","pencil"],[1,"container-absolute"],[3,"hasPageTitle","onlyContent","breadcrumbs","isScorm","routeParams"],[1,"center-row",3,"ngClass"],["id","ionContent",1,"content-no-header"],["mode","ios","color","dark",3,"value"],[3,"value"],[3,"click","value"]],template:function(a,u){1&a&&n.DNE(0,cn,9,27)(1,mn,6,4),2&a&&(n.vxM("V7 Landing Page"!==u.instance.feature.featureType.name&&!0!==u.onlyContent?0:-1),n.R7$(),n.vxM("V7 Landing Page"===u.instance.feature.featureType.name||u.onlyContent?1:-1))},dependencies:[b.YU,b.B3,x,f.bv,f.Jm,f.hU,f.W9,f.iq,f.he,f.ln,f.Gp,f.eP,f.Je,L.H,b.T3,b.Jj,R.F],styles:['[_nghost-%COMP%]{height:100%;background-color:#181818;color:#fff;position:relative}[_nghost-%COMP%]   .video-header-min[_ngcontent-%COMP%]{aspect-ratio:18/1!important}[_nghost-%COMP%]   .video-header[_ngcontent-%COMP%]{aspect-ratio:3/1}[_nghost-%COMP%]   .video-header[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{max-height:100%!important}[_nghost-%COMP%]   .video-header[_ngcontent-%COMP%]   .bg-video[_ngcontent-%COMP%]{position:absolute;width:100%;min-width:100%;left:0;top:0}[_nghost-%COMP%]   .video-header[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{position:absolute!important;bottom:10px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]{border-bottom:2px solid rgb(68,68,68);overflow:hidden;position:relative}@media (min-width: 959px){[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#1e1e1e00,#1e1e1ecc 75%,#1e1e1ef2 88.5148948598%,#1e1e1e),var(--background-image);background-color:#000;background-size:cover;background-color:#111;background-position:center}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .page-margins[_ngcontent-%COMP%]{height:100%;margin-left:var(--page-margin-left-header);margin-right:var(--page-margin-right-header);display:flex;justify-content:center}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .page-margins-player[_ngcontent-%COMP%]{height:100%;margin-left:var(--page-margin-left-player-header);margin-right:var(--page-margin-right-player-header);display:flex;justify-content:center}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column;position:relative;max-height:250px;min-height:25px;overflow:hidden;margin-top:8px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-main-org[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background-color:#fff;border-radius:3px;margin-right:2vw;margin-bottom:15px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-org[_ngcontent-%COMP%]{min-width:8vw;min-height:8vw;max-height:8vw;max-width:8vw;padding:10px;aspect-ratio:1/7}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-org[_ngcontent-%COMP%]   .icon-org[_ngcontent-%COMP%]{object-fit:contain;border-radius:2px;max-height:100%}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min-org[_ngcontent-%COMP%]{min-width:4vw;min-height:4vw;max-height:5vw;max-width:5vw}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min-org[_ngcontent-%COMP%]   .icon-org[_ngcontent-%COMP%]{object-fit:contain;border-radius:2px;max-height:100%}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-main[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background-color:#fff;border-radius:3px;margin-right:2vw;margin-bottom:15px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col[_ngcontent-%COMP%]{min-width:8vw;min-height:8vw;max-height:8vw;max-width:8vw}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{object-fit:contain;border-radius:2px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min[_ngcontent-%COMP%]{min-width:4vw;min-height:4vw;max-height:5vw;max-width:5vw}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{object-fit:contain;border-radius:2px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .header-container-parent[_ngcontent-%COMP%]{margin-left:calc(3.5vw - 15px)}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .header-container[_ngcontent-%COMP%]{margin-top:auto;padding-bottom:5px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .header-container[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{color:#ccc;font-family:Roboto;font-weight:400;font-size:16px;font-style:italic;line-height:var(--general-line-height)}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .org[_ngcontent-%COMP%]{display:flex;height:100%;justify-content:flex-end;padding-bottom:15px;flex-direction:column}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-breadcrumb[_ngcontent-%COMP%]::part(native){padding-inline:0px!important}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:35px;bottom:55px;left:20px;font-family:"Exo 2";font-weight:800}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:18px;left:20px;margin:0;font-family:Roboto;font-weight:500;line-height:1.4;color:#ccc;width:80%}[_nghost-%COMP%]   .content-no-header[_ngcontent-%COMP%]{height:calc(100vh - 92px)}}@media (max-width: 960px){[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#1e1e1e00,#1e1e1ecc 75%,#1e1e1ef2 88.5148948598%,#1e1e1e),var(--background-image);background-color:#000;background-size:cover;background-color:#111;min-height:50px!important;background-position:center}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .page-margins[_ngcontent-%COMP%]{height:100%;margin-left:var(--page-margin-left-header);margin-right:var(--page-margin-right-header);display:flex;justify-content:center}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .page-margins-player[_ngcontent-%COMP%]{height:100%;margin-left:var(--page-margin-left-player-header);margin-right:var(--page-margin-right-player-header);display:flex;justify-content:center}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{width:100%;height:100%;display:flex;flex-direction:column;position:relative;max-height:220px;min-height:25px;overflow:hidden;margin-top:8px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-main-org[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background-color:#fff;border-radius:3px;margin-right:2vw;margin-bottom:15px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-org[_ngcontent-%COMP%]{min-width:75px;min-height:75px;max-height:75px;max-width:75px;padding:10px;aspect-ratio:1/7}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-org[_ngcontent-%COMP%]   .icon-org[_ngcontent-%COMP%]{object-fit:contain;border-radius:2px;max-height:100%}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min-org[_ngcontent-%COMP%]{min-width:75px;min-height:75px;max-height:75px;max-width:75px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min-org[_ngcontent-%COMP%]   .icon-org[_ngcontent-%COMP%]{object-fit:contain;border-radius:2px;max-height:100%}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-main[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background-color:#fff;border-radius:3px;margin-right:2vw;margin-bottom:15px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col[_ngcontent-%COMP%]{min-width:8vw;min-height:8vw;max-height:8vw;max-width:8vw}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{border-radius:5px;background-size:contain}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min[_ngcontent-%COMP%]{min-width:60px;min-height:60px;max-height:8vw;max-width:8vw}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .icon-col-min[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{object-fit:contain;border-radius:2px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .header-container-parent[_ngcontent-%COMP%]{margin-left:calc(3.5vw - 15px)}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .header-container[_ngcontent-%COMP%]{margin-top:auto;padding-bottom:5px}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .header-container[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{color:#ccc;font-family:Roboto;font-weight:400;font-size:16px;font-style:italic;line-height:var(--general-line-height)}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .org[_ngcontent-%COMP%]{display:flex;height:100%;justify-content:flex-end;padding-bottom:15px;flex-direction:column}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   ion-breadcrumb[_ngcontent-%COMP%]::part(native){padding-inline:0px!important}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:24px;bottom:55px;left:20px;font-family:"Exo 2";font-weight:800}[_nghost-%COMP%]   .image-container[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:12px;left:20px;margin:0;font-family:Roboto;font-weight:500;line-height:1.4;color:#ccc}[_nghost-%COMP%]   .content-no-header[_ngcontent-%COMP%]{height:calc(100vh - 92px)}}.grid-view-color[_ngcontent-%COMP%]{background-color:#1e1e1e}.forward-slash[_ngcontent-%COMP%]{margin-left:15px}ion-content[_ngcontent-%COMP%]::part(scroll){overflow-y:hidden!important}ion-breadcrumb[_ngcontent-%COMP%]{cursor:pointer}.start-now-button[_ngcontent-%COMP%]{display:flex;align-items:center;margin-left:10px}.edit-button[_ngcontent-%COMP%]{position:absolute;right:25px;top:8px;z-index:9999}.edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{margin:0;color:#000;text-transform:none;border-radius:3px;font-weight:500;font-size:18px;background-color:#f99e00}.edit-button[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{width:18px;height:18px;padding-left:10px;margin-right:10px;color:#000}.edit-button[_ngcontent-%COMP%]   ion-button.inner-container[_ngcontent-%COMP%]::part(native){line-height:normal;--padding-start: 0 !important;--padding-end: 10px !important}@media screen and (max-width: 960px){.edit-button[_ngcontent-%COMP%]{margin-top:15px}.edit-button[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{min-height:36px;--padding-top: 0px;--padding-bottom: 0px}}.container-absolute[_ngcontent-%COMP%]{position:absolute;width:100%;z-index:999;padding-top:10px;padding-left:10px;display:grid}@media screen and (max-width: 960px){.container-absolute[_ngcontent-%COMP%]{padding-right:15px}}.center-row[_ngcontent-%COMP%]{position:absolute;display:flex;top:0;align-items:center;justify-content:center;z-index:9999;height:100%;width:fit-content;justify-self:center;max-width:100%}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]{display:flex;width:fit-content;border-radius:20px;background:#444!important;font-size:16px;line-height:1.1;padding:5px 11px 3px;border:.5px solid #333}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]{width:fit-content!important;color:#ddd}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:16px;color:#ddd}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]:nth-child(n+2){border-left:1px solid #333333;border-bottom-left-radius:0;border-top-left-radius:0}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   .segment-button-checked[_ngcontent-%COMP%]::part(native){background:#444!important}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   .segment-button-checked[_ngcontent-%COMP%]::part(indicator){background:#444!important}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   .segment-button-checked[_ngcontent-%COMP%]{--background: rgba(68, 68, 68) !important;--indicator-box-shadow: none !important}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   .segment-button-checked[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-weight:900;color:var(--ion-color-primary)}.center-row[_ngcontent-%COMP%]   ion-segment[_ngcontent-%COMP%]   ion-segment-button[_ngcontent-%COMP%]:hover   ion-label[_ngcontent-%COMP%]{color:#fff!important}@media screen and (max-width: 960px){.center-row[_ngcontent-%COMP%]{max-width:90%}.center-row-fab[_ngcontent-%COMP%]{max-width:75%}}'],data:{animation:[(0,t.hZ)("fade",[(0,t.wk)("false",(0,t.iF)({height:"fit-content"})),(0,t.wk)("true",(0,t.iF)({minHeight:"75px;",height:"fit-content"})),(0,t.kY)("* => false",[(0,t.i0)("0.2s")]),(0,t.kY)("* => true",[(0,t.i0)("0.2s")])])]}})}}return c})()}}]);