"use strict";(self.webpackChunkpublic=self.webpackChunkpublic||[]).push([[873],{90873:(b,c,r)=>{r.r(c),r.d(c,{AuthCallbackModule:()=>d});var g=r(89417),l=r(74710),p=r(26110),h=r(21413),s=r(56977),o=r(93953),u=r(15389),m=r(70600),i=r(73465);const C=[{path:"",component:(()=>{class n{constructor(t,e){this.authService=t,this.nav=e,this.componentDestroyed$=new h.B,this.returnUrl="/my-journey"}ngOnInit(){sessionStorage.getItem("returnUrl")&&"/"!==sessionStorage.getItem("returnUrl")&&"/splash"!==sessionStorage.getItem("returnUrl")&&(this.returnUrl=sessionStorage.getItem("returnUrl")??this.returnUrl),this.authService.init(),this.authService.completeAuthentication().pipe((0,s.Q)(this.componentDestroyed$)).subscribe(()=>{this.nav.navigateRoot(this.returnUrl).then(()=>{sessionStorage.setItem("logoutWindow","true"),new BroadcastChannel("refresh").postMessage("refresh")})})}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(e){return new(e||n)(o.rXU(u.u),o.rXU(m.q9))}}static{this.\u0275cmp=o.VBU({type:n,selectors:[["app-auth-callback"]],decls:7,vars:0,consts:[[1,"auth-callback"],[1,"ion-align-items-center"],["size","12"],["src","assets/images/EdgeFactor-EF_rings-2018-white_small_png.png",1,"img"],["color","primary","name","dots"]],template:function(e,a){1&e&&(o.j41(0,"div",0)(1,"ion-grid")(2,"ion-row",1)(3,"ion-col",2),o.nrm(4,"img",3)(5,"br")(6,"ion-spinner",4),o.k0s()()()())},dependencies:[i.hU,i.lO,i.ln,i.w2],styles:[".auth-callback[_ngcontent-%COMP%]{height:100%}.auth-callback[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{background-color:#111;height:100%}.auth-callback[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{height:100%}.auth-callback[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{text-align:center;color:#f99e00}"]})}}return n})()},{path:"external",component:(()=>{class n{constructor(t,e){this.route=t,this.authService=e,this.componentDestroyed$=new h.B,this.returnUrl="/my-journey"}ngOnInit(){const t=this.route.snapshot.queryParamMap.get("joinCode");t?sessionStorage.setItem("returnUrl","/user/joincode/"+t):sessionStorage.setItem("returnUrl",this.returnUrl),this.authService.setUserContext(!1).pipe((0,s.Q)(this.componentDestroyed$)).subscribe(()=>{this.authService.init(),this.authService.startAuthenticationExternal().pipe((0,s.Q)(this.componentDestroyed$)).subscribe(()=>{})})}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(e){return new(e||n)(o.rXU(l.nX),o.rXU(u.u))}}static{this.\u0275cmp=o.VBU({type:n,selectors:[["app-external-callback"]],decls:7,vars:0,consts:[[1,"auth-callback"],[1,"ion-align-items-center"],["size","12"],["src","assets/images/EdgeFactor-EF_rings-2018-white_small_png.png",1,"img"],["color","primary","name","dots"]],template:function(e,a){1&e&&(o.j41(0,"div",0)(1,"ion-grid")(2,"ion-row",1)(3,"ion-col",2),o.nrm(4,"img",3)(5,"br")(6,"ion-spinner",4),o.k0s()()()())},dependencies:[i.hU,i.lO,i.ln,i.w2],styles:[".auth-callback[_ngcontent-%COMP%]{height:100%}.auth-callback[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]{background-color:#111;height:100%}.auth-callback[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]{height:100%}.auth-callback[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{text-align:center;color:#f99e00}"]})}}return n})()}];let d=(()=>{class n{static{this.\u0275fac=function(e){return new(e||n)}}static{this.\u0275mod=o.$C({type:n})}static{this.\u0275inj=o.G2t({imports:[p.G,l.iI.forChild(C),g.YN]})}}return n})()}}]);