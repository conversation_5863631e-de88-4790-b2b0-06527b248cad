"use strict";(self.webpackChunkpublic=self.webpackChunkpublic||[]).push([[8782],{58782:(u,s,t)=>{t.r(s),t.d(s,{startStatusTap:()=>r});var i=t(10467),o=t(54261),_=t(20909),l=t(84920);const r=()=>{const n=window;n.addEventListener("statusTap",()=>{(0,o.e)(()=>{const d=document.elementFromPoint(n.innerWidth/2,n.innerHeight/2);if(!d)return;const e=(0,_.f)(d);e&&new Promise(E=>(0,l.c)(e,E)).then(()=>{(0,o.w)((0,i.A)(function*(){e.style.setProperty("--overflow","hidden"),yield(0,_.s)(e,300),e.style.removeProperty("--overflow")}))})})})}}}]);