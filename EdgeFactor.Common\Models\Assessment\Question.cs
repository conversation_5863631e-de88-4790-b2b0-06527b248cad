using EdgeFactor.Common.DB.Models;

namespace EdgeFactor.Common.Models.Assessment
{
    public record Question(
        Guid Id,
        string? Title,
        double? Runtime,
        QuestionType QuestionType,
        string? QuestionText,
        string? QuestionDescription,
        int? Weighting,
        bool? Required,
        int? SortOrder,
        bool? IsDynamicOptions,
        bool? IsAuthorEditable,
        bool? IsRandom,
        bool? IsOtherOption,
        bool? IsLimitTo,
        int? Limit,
        bool? IsOptionPhoto,
        int? ScaleStart,
        int? ScaleEnd,
        string? ScaleLabelOne,
        string? ScaleLabelTwo,
        string? ScaleLabelThree,
        string? Purpose,
        Guid? BackgroundImageAssetId,
        Guid? DynamicOptionsParentId,
        IEnumerable<QuestionAnswer> QuestionAnswers,
        UserAnswer? UserAnswer,
        Guid? FeatureId,
        bool? CorrectAnswersExists,
        string? AnswerText,
        Guid? MeasuredTagId)
    {
        public Question(Questions question, Guid? instanceId = null) : this(
            question.Id,
            question.Title,
            question.Runtime,
            new QuestionType(question.QuestionType),
            question.QuestionText,
            question.QuestionDescription,
            question.Weighting,
            question.Required,
            question.SortOrder,
            question.IsDynamicOptions,
            question.IsAuthorEditable,
            question.IsRandom,
            question.IsOtherOption,
            question.IsLimitTo,
            question.Limit,
            question.IsOptionPhoto,
            question.ScaleStart,
            question.ScaleEnd,
            question.ScaleLabelOne,
            question.ScaleLabelTwo,
            question.ScaleLabelThree,
            question.Purpose,
            question.BackgroundImageAssetId,
            question.DynamicOptionsParentId,
            instanceId != null && question.QuestionType.Name != "True/False" && question.QuestionType.Name != "Yes/No" ? question.QuestionAnswers.Where(x => x.InstanceId == instanceId).Select(x => new QuestionAnswer(x)).OrderBy(x => x.SortOrder) : question.QuestionAnswers.Select(x => new QuestionAnswer(x)).OrderBy(x => x.SortOrder),
            null,
            question.FeatureId,
            null,
            question.AnswerText,
            question.MeasuredTagId
            )
        { }

        public Question(Questions question, Guid? instanceId, IEnumerable<Tags> tags, bool isBuilder) : this(
            question.Id,
            question.Title,
            question.Runtime,
            new QuestionType(question.QuestionType),
            question.QuestionText,
            question.QuestionDescription,
            question.Weighting,
            question.Required,
            question.SortOrder,
            question.IsDynamicOptions,
            question.IsAuthorEditable,
            question.IsRandom,
            question.IsOtherOption,
            question.IsLimitTo,
            question.Limit,
            question.IsOptionPhoto,
            question.ScaleStart,
            question.ScaleEnd,
            question.ScaleLabelOne,
            question.ScaleLabelTwo,
            question.ScaleLabelThree,
            question.Purpose,
            question.BackgroundImageAssetId,
            question.DynamicOptionsParentId,

            // This gets the dynamic tags as question answers if dynamicOptionId is set....
            // Should not get dynamic options if question type is Yes/No or True/False
            question.DynamicOptionsParentId != null && question.IsDynamicOptions == true && question.QuestionType.Name != "True/False" && question.QuestionType.Name != "Yes/No" && tags != null && tags.Any()
            ?
            question.QuestionAnswers.Where(x => x.InstanceId == instanceId).Select(x => new QuestionAnswer(x))
            .Concat(
                tags.Where(x => !question.QuestionAnswers.Where(x => x.InstanceId == instanceId).Any(q => q.Value.ToGUIDNull() == x.Id) && x.ParentId == question.DynamicOptionsParentId)
                .Select(x => new QuestionAnswer(x, question.Id, false)))
                .Distinct()
            .OrderBy(x => x.SortOrder)
            // If in builder view do not limit the amount of items returned from tags
            .Take(isBuilder == false && question.Limit != null
                // If questionanswers lenght is more than the limit bring back all the question answers....
                ? question.QuestionAnswers.Count <= question.Limit ? question.Limit ?? 0 : question.QuestionAnswers.Count
                : (question.QuestionAnswers.Count + tags.Count())).ToList()
            :
            // If Yes/No or True/False use feature question answers
            question.QuestionType.Name != "True/False" && question.QuestionType.Name != "Yes/No" ? question.QuestionAnswers.Where(x => x.InstanceId == instanceId).Select(x => new QuestionAnswer(x)).OrderBy(x => x.SortOrder) : question.QuestionAnswers.Select(x => new QuestionAnswer(x)).OrderBy(x => x.SortOrder)

            ,
            null,
            question.FeatureId,
            question.QuestionAnswers?.Any(x => x.IsCorrect == true),
            question.AnswerText,
            question.MeasuredTagId
            )
        { }

        public Question(Questions question, UserAnswers userAnswer, Guid? instanceId, IEnumerable<Tags> tags, bool isBuilder) : this(
            question.Id,
            question.Title,
            question.Runtime,
            new QuestionType(question.QuestionType),
            question.QuestionText,
            question.QuestionDescription,
            question.Weighting,
            question.Required,
            question.SortOrder,
            question.IsDynamicOptions,
            question.IsAuthorEditable,
            question.IsRandom,
            question.IsOtherOption,
            question.IsLimitTo,
            question.Limit,
            question.IsOptionPhoto,
            question.ScaleStart,
            question.ScaleEnd,
            question.ScaleLabelOne,
            question.ScaleLabelTwo,
            question.ScaleLabelThree,
            question.Purpose,
            question.BackgroundImageAssetId,
            question.DynamicOptionsParentId,

            // This gets the dynamic tags as question answers if dynamicOptionId is set....
            // Should not get dynamic options if question type is Yes/No or True/False
            question.DynamicOptionsParentId != null && question.IsDynamicOptions == true && question.QuestionType.Name != "True/False" && question.QuestionType.Name != "Yes/No" && tags != null && tags.Any()
            ?
            question.QuestionAnswers.Where(x => x.InstanceId == instanceId).Select(x => new QuestionAnswer(x, userAnswer != null && userAnswer.UserQuestionAnswers?.Any(y => y.QuestionAnswerId != null && y.QuestionAnswerId == x.Id) == true))
            .Concat(
                tags.Where(x => !question.QuestionAnswers.Where(x => x.InstanceId == instanceId).Any(q => q.Value.ToGUIDNull() == x.Id) && x.ParentId == question.DynamicOptionsParentId)
                .Select(x => new QuestionAnswer(x, question.Id, userAnswer != null && userAnswer.UserQuestionAnswers?.Any(y => y.QuestionAnswerId == null && y.TagId == x.Id) == true)))
                .Distinct()
            .OrderBy(x => x.SortOrder)
            // If in builder view do not limit the amount of items returned from tags
            .Take(isBuilder == false && question.Limit != null
                // If questionanswers lenght is more than the limit bring back all the question answers....
                ? question.Limit ?? 5
                : (question.QuestionAnswers.Count + tags.Count())).OrderBy(_ => Guid.NewGuid())
            :
            // If Yes/No or True/False use feature question answers
            question.QuestionAnswers.Any(x => x.InstanceId != null && x.InstanceId == instanceId)
            ? question.QuestionAnswers.Where(x => x.InstanceId == instanceId).Select(x => new QuestionAnswer(x, userAnswer != null && userAnswer.UserQuestionAnswers?.Any(y => y.QuestionAnswerId != null && y.QuestionAnswerId == x.Id) == true)).OrderBy(x => x.SortOrder)
            : question.QuestionAnswers.Where(x => x.InstanceId == null).Select(x => new QuestionAnswer(x, userAnswer != null && userAnswer.UserQuestionAnswers?.Any(y => y.QuestionAnswerId != null && y.QuestionAnswerId == x.Id) == true)).OrderBy(x => x.SortOrder)
            ,
            userAnswer != null ? new UserAnswer(userAnswer) : null,
            question.FeatureId,
            question.QuestionAnswers?.Any(x => x.IsCorrect == true),
            question.AnswerText,
            question.MeasuredTagId
            )
        { }
    }
}
