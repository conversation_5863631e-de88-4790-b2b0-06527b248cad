<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <location path="index.html">
    <system.webServer>
      <httpProtocol>
        <customHeaders>
          <add name="Cache-Control" value="no-cache" />
	        <add name="X-Frame-Options" value="AllowAll" />
        </customHeaders>
      </httpProtocol>
    </system.webServer>
  </location>
    <system.webServer>
        <httpProtocol>
            <customHeaders>
                <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains; preload" />
            </customHeaders>
        </httpProtocol>
        <rewrite>
            <rules>
                <clear />
                <rule name="Clever" stopProcessing="true">
                    <match url="(rostering/clever)" />
                    <conditions logicalGrouping="MatchAll" trackAllCaptures="false" />
                    <action type="Redirect" url="https://api.edgefactor.com/v1/rostering/clever" />
                </rule>
                <rule name="d2l" stopProcessing="true">
                    <match url="(rostering/d2l)" />
                    <conditions logicalGrouping="MatchAll" trackAllCaptures="false" />
                    <action type="Redirect" url="https://api.edgefactor.com/v1/rostering/d2l" redirectType="Temporary" />
                </rule>
                <rule name="classlink" stopProcessing="true">
                    <match url="(rostering/classlink)" />
                    <conditions logicalGrouping="MatchAll" trackAllCaptures="false" />
                    <action type="Redirect" url="https://api.edgefactor.com/v1/rostering/classlink" redirectType="Temporary" />
                </rule>
                <rule name="V7 slugs" stopProcessing="true">
                    <match url="(.*)" />
                    <conditions logicalGrouping="MatchAny" trackAllCaptures="false">
                        <add input="{HTTP_URL}" pattern="\/browse$" />
                        <add input="{HTTP_URL}" pattern="\/V5\/pages\/Welcome" />
                        <add input="{HTTP_URL}" pattern="\/edufactor$" />
                        <add input="{HTTP_URL}" pattern="\/theater$" />
                    </conditions>
                    <action type="Redirect" url="https://{HTTP_HOST}" appendQueryString="false" />
                </rule>
                <rule name="HTTPS">
                    <match url="(.*)" />
                    <conditions logicalGrouping="MatchAll" trackAllCaptures="false">
                        <add input="{HTTPS}" pattern="^OFF$" />
                    </conditions>
                    <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" />
                </rule>
                <rule name="Remove www" stopProcessing="true">
                  <match url="(.*)" ignoreCase="true" />
                  <conditions logicalGrouping="MatchAll">
                    <add input="{HTTP_HOST}" pattern="^www\.(.+)$" />
                  </conditions>
                  <action type="Redirect" url="https://{C:1}/{R:0}" />
                </rule>
                <rule name="SPA" stopProcessing="true">
                    <match url="^(.*)$" />
                    <conditions logicalGrouping="MatchAll" trackAllCaptures="false">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="/index.html" />
                </rule>
            </rules>
        </rewrite>
        <security>
            <requestFiltering>
                <requestLimits maxAllowedContentLength="**********" />
            </requestFiltering>
        </security>
        <staticContent>
            <mimeMap fileExtension=".data" mimeType="application/octet-stream" />
            <mimeMap fileExtension=".bank" mimeType="application/octet-stream" />
            <mimeMap fileExtension="." mimeType="application/json" />
        </staticContent>
    </system.webServer>
</configuration>
