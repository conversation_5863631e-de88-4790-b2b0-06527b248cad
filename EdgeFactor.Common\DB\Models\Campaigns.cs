namespace EdgeFactor.Common.DB.Models;

public partial class Campaigns
{
    public Guid Id { get; set; }

    public string Title { get; set; } = null!;

    public Guid FeatureId { get; set; }

    public string CampaignCode { get; set; } = null!;

    public Guid? CampaignTagId { get; set; }

    public Guid? InstanceId { get; set; }

    public Guid? ProductOrganizationId { get; set; }

    public Guid? ProductId { get; set; }

    public Guid? IconAssetId { get; set; }

    public DateTime? ProductExpirationDate { get; set; }

    public Guid? RoleId { get; set; }

    public DateTime? CampaignExpirationDate { get; set; }

    public int? MaxUsers { get; set; }

    public int? UserCount { get; set; }

    public string? Status { get; set; }

    public virtual Tags? CampaignTag { get; set; }

    public virtual ICollection<CampaignTags> CampaignTags { get; set; } = new List<CampaignTags>();

    public virtual Features Feature { get; set; } = null!;

    public virtual Assets? IconAsset { get; set; }

    public virtual Instances? Instance { get; set; }

    public virtual Products? Product { get; set; }

    public virtual ProductOrganizations? ProductOrganization { get; set; }

    public virtual Roles? Role { get; set; }
}
