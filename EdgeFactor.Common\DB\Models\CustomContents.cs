namespace EdgeFactor.Common.DB.Models;

public partial class CustomContents
{
    public Guid Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string? ButtonText { get; set; }

    public string? ButtonUrl { get; set; }

    public Guid UserId { get; set; }

    public Guid? BackgroundImageAssetId { get; set; }

    public bool? SameUrlNavigation { get; set; }

    public virtual ICollection<RowContents> RowContents { get; set; } = new List<RowContents>();
}
