"use strict";(self.webpackChunkpublic=self.webpackChunkpublic||[]).push([[6548],{56548:(zo,N,d)=>{d.r(N),d.d(N,{FeatureRepositoryBuilderModule:()=>Lo});var T=d(1875),S=d(74710),fe=d(2863),Ce=d(33863),be=d(85359),ye=d(26110),V=d(96359),A=d(19897),U=d(62351),L=d(48787),z=d(52497),v=d(47347),Y=d(28323),J=d(45303),Q=d(23719),F=d(27233),_=d(10467),e=d(93953),x=d(74805),M=d(50019),s=d(73465),k=d(99213);function Te(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",6),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.changeStatus("Publish"))}),e.EFF(1,"Publish"),e.k0s()}if(2&o){const t=e.XpG(2);e.Y8G("disabled",!t.isPublishEnabled)}}function ve(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",7),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.changeStatus("private"))}),e.EFF(1,"Unpublish"),e.k0s()}}function xe(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-col",5)(1,"mat-icon",8),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.delete())}),e.k0s()()}}function Fe(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-col",1)(1,"app-select-option-control",2),e.bIt("valueChanged",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.updateStatus(i))}),e.k0s()(),e.j41(2,"ion-col",3),e.DNE(3,Te,2,1,"ion-button",4)(4,ve,2,0,"ion-button",0),e.k0s(),e.DNE(5,xe,2,0,"ion-col",5)}if(2&o){let t;const n=e.XpG();e.R7$(),e.Y8G("toolTip","Select Status")("placeHolder","Select Status")("label","")("backgroundColor",n.controlBackground)("textValue",null!==(t=n.columnValue)&&void 0!==t?t:"")("options",n.statusTypes),e.R7$(2),e.vxM(n.isUnpublishEnabled?-1:3),e.R7$(),e.vxM(n.isUnpublishEnabled?4:-1),e.R7$(),e.vxM("deleted"!=n.columnValue?5:-1)}}function Me(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",7),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.changeStatus("private"))}),e.EFF(1,"Restore"),e.k0s()}}let H=(()=>{class o{constructor(t,n){this.popOver=t,this.modalController=n,this.statusChanged=new e.bkB,this.statusTypes=[{id:"AwaitingReview",value:"AwaitingReview"},{id:"Approved",value:"Approved"},{id:"EdgeFactorRejected",value:"EdgeFactorRejected"},{id:"CustomerRejected",value:"CustomerRejected"},{id:"Deleted",value:"Deleted"}],this.isPublishEnabled=!1,this.isUnpublishEnabled=!1,this.controlBackground="#181818"}ngOnChanges(t){t.columnValue&&(this.setPublishEnabled(this.columnValue),this.setUnpublishEnabled(this.columnValue))}setPublishEnabled(t){this.isPublishEnabled="Approved"===t||"private"===t}setUnpublishEnabled(t){this.isUnpublishEnabled="organization"===t||"public"===t||"network"===t}updateStatus(t){t===this.statusTypes.find(n=>"Deleted"===n.value)?.id&&this.delete(),"Publish"!==t?(this.columnValue=t,this.statusChanged.next(t??""),this.setPublishEnabled(this.columnValue),this.setUnpublishEnabled(this.columnValue)):this.openPublishOptions(null,t)}changeStatus(t){"Publish"!==t?(this.columnValue=t,this.statusChanged.next(t??""),this.setPublishEnabled(this.columnValue),this.setUnpublishEnabled(this.columnValue)):this.openPublishOptions(null,t)}delete(){var t=this;return(0,_.A)(function*(){const n=yield t.modalController.create({component:x.D,cssClass:"confirm-dialog",componentProps:{headerText:"Delete your page",bodyText:"You're about to delete this instance. Once you delete your work it will no longer be visible to enrolled people and other users at your Organization.",buttonText:"Delete"}});n.onDidDismiss().then(i=>{"confirm"===i.role&&t.changeStatus("deleted")}),yield n.present()})()}openPublishOptions(t,n){var i=this;return(0,_.A)(function*(){if(n&&"Publish"===n){if("Modifiable Learning Container Pages"===i.feature.featureType.name)return void(yield i.openPublishDialog("organization"));const a=[{key:"organization",value:"To my organization"},{key:"public",value:"Publicly"},{key:"network",value:"To my network"}],l=yield i.popOver.create({component:M.u,cssClass:"question-type-popover",componentProps:{options:a},event:t,side:"bottom"});l.onDidDismiss().then(function(){var p=(0,_.A)(function*(m){m.data&&(yield i.openPublishDialog(m.data.key))});return function(m){return p.apply(this,arguments)}}()),yield l.present()}else i.changeStatus("private")})()}openPublishDialog(t){var n=this;return(0,_.A)(function*(){const i=yield n.modalController.create({component:x.D,cssClass:"confirm-dialog",componentProps:{headerText:"Update your page",bodyText:`You're about to update your ${n.feature?.title}. Once you publish your work it will be visible to enrolled people and other users at your Organization.`,buttonText:"Publish"}});i.onDidDismiss().then(a=>{"confirm"===a.role&&n.changeStatus(t)}),yield i.present()})()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(s.IE),e.rXU(s.W3))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-instance-publish-button"]],inputs:{columnValue:"columnValue",feature:"feature"},outputs:{statusChanged:"statusChanged"},features:[e.OA$],decls:4,vars:1,consts:[["fill","solid","color","primary"],["size","5"],[3,"valueChanged","toolTip","placeHolder","label","backgroundColor","textValue","options"],["size","5",1,"button-col"],["fill","solid","color","primary",3,"disabled"],["size","2",1,"trash-col"],["fill","solid","color","primary",3,"click","disabled"],["fill","solid","color","primary",3,"click"],["svgIcon","trash",3,"click"]],template:function(n,i){1&n&&(e.j41(0,"ion-grid")(1,"ion-row"),e.DNE(2,Fe,6,9)(3,Me,2,0,"ion-button",0),e.k0s()()),2&n&&(e.R7$(2),e.vxM("deleted"!=i.columnValue?2:3))},dependencies:[v.c,s.Jm,s.hU,s.lO,s.ln,k.An],styles:[".button-col[_ngcontent-%COMP%]{padding-top:10px}.trash-col[_ngcontent-%COMP%]{display:flex!important;align-items:center!important}.trash-col[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-left:15px}"]})}}return o})();var W=function(o){return o[o.AwaitingReview=1]="AwaitingReview",o[o.Approved=2]="Approved",o[o.EdgeFactorRejected=4]="EdgeFactorRejected",o[o.CustomerRejected=8]="CustomerRejected",o[o.Published=16]="Published",o[o.Unpublished=32]="Unpublished",o}(W||{});function ke(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",6),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.changeStatus(i.orgStatusTypeEnum.Published))}),e.EFF(1,"Publish"),e.k0s()}if(2&o){const t=e.XpG(2);e.Y8G("disabled",!t.isPublishEnabled)}}function Oe(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",7),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.changeStatus(i.orgStatusTypeEnum.Unpublished))}),e.EFF(1,"Unpublish"),e.k0s()}}function Pe(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-col",5)(1,"mat-icon",8),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.delete())}),e.k0s()()}}function De(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-col",1)(1,"app-select-option-control",2),e.bIt("valueChanged",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.updateStatus(i))}),e.k0s()(),e.j41(2,"ion-col",3),e.DNE(3,ke,2,1,"ion-button",4)(4,Oe,2,0,"ion-button",0),e.k0s(),e.DNE(5,Pe,2,0,"ion-col",5)}if(2&o){let t;const n=e.XpG();e.R7$(),e.Y8G("toolTip","Select Status")("placeHolder","Select Status")("label","")("backgroundColor",n.controlBackground)("textValue",null!==(t=n.columnValue)&&void 0!==t?t:"")("options",n.orgStatusTypes),e.R7$(2),e.vxM(n.isUnpublishEnabled?-1:3),e.R7$(),e.vxM(n.isUnpublishEnabled?4:-1),e.R7$(),e.vxM(n.orgStatus&&"Deleted"==n.orgStatus.name?-1:5)}}function we(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",7),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.changeStatus(i.orgStatusTypeEnum.Unpublished))}),e.EFF(1,"Restore"),e.k0s()}}let Z=(()=>{class o{constructor(t){this.modalController=t,this.statusChanged=new e.bkB,this.orgStatusTypeEnum=W,this.isPublishEnabled=!1,this.isUnpublishEnabled=!1,this.controlBackground="#181818"}ngOnChanges(t){(t.orgStatuses||t.columnValue)&&this.updateData()}updateData(){this.setOrgStatusTypes(this.columnValue),this.setPublishEnabled(this.columnValue),this.setUnpublishEnabled(this.columnValue)}delete(){var t=this;return(0,_.A)(function*(){const n=yield t.modalController.create({component:x.D,cssClass:"confirm-dialog",componentProps:{headerText:"Delete your page",bodyText:"You're about to delete this organization. Once you delete your work it will no longer be visible to enrolled people and other users at your Organization.",buttonText:"Delete"}});n.onDidDismiss().then(i=>{t.changeStatus("confirm"===i.role?64:t.orgStatus.typeBw)}),yield n.present()})()}setOrgStatusTypes(t){this.orgStatuses||(this.orgStatusTypes=[]);const n=this.orgStatuses.map(a=>({id:a.id,value:a.name})),i=this.orgStatuses.find(a=>a.id===t);this.orgStatus=i,this.orgStatusTypes=i?.typeBw===this.orgStatusTypeEnum.Approved||(i?.typeBw||0)>=this.orgStatusTypeEnum.Published?n:this.orgStatuses.filter(a=>a.typeBw<this.orgStatusTypeEnum.Published).map(a=>({id:a.id,value:a.name}))}setPublishEnabled(t){if(!this.orgStatuses)return void(this.isPublishEnabled=!1);const n=this.orgStatuses.find(i=>i.id===t);this.isPublishEnabled=n?.typeBw===this.orgStatusTypeEnum.Approved||n?.typeBw===this.orgStatusTypeEnum.Unpublished}setUnpublishEnabled(t){if(!this.orgStatuses)return void(this.isUnpublishEnabled=!1);const n=this.orgStatuses.find(i=>i.id===t);this.isUnpublishEnabled=n?.typeBw===this.orgStatusTypeEnum.Published}updateStatus(t){this.columnValue=t,t===this.orgStatuses.find(n=>"Deleted"===n.name)?.id?this.delete():(this.updateData(),this.statusChanged.next(t??""))}changeStatus(t){const n=this.orgStatuses.find(i=>i.typeBw===t)?.id;n&&(this.columnValue=n,this.updateData(),this.statusChanged.next(n??""))}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(s.W3))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-org-publish-button"]],inputs:{orgStatuses:"orgStatuses",columnValue:"columnValue"},outputs:{statusChanged:"statusChanged"},features:[e.OA$],decls:4,vars:1,consts:[["fill","solid","color","primary"],["size","5"],[3,"valueChanged","toolTip","placeHolder","label","backgroundColor","textValue","options"],["size","5",1,"button-col"],["fill","solid","color","primary",3,"disabled"],["size","2",1,"trash-col"],["fill","solid","color","primary",3,"click","disabled"],["fill","solid","color","primary",3,"click"],["svgIcon","trash",3,"click"]],template:function(n,i){1&n&&(e.j41(0,"ion-grid")(1,"ion-row"),e.DNE(2,De,6,9)(3,we,2,0,"ion-button",0),e.k0s()()),2&n&&(e.R7$(2),e.vxM(i.orgStatus&&"Deleted"==i.orgStatus.name?3:2))},dependencies:[v.c,s.Jm,s.hU,s.lO,s.ln,k.An],styles:[".button-col[_ngcontent-%COMP%]{padding-top:10px}.trash-col[_ngcontent-%COMP%]{display:flex!important;align-items:center!important}.trash-col[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-left:15px}"]})}}return o})(),K=(()=>{class o{constructor(){}static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-analytics"]],inputs:{feature:"feature"},decls:0,vars:0,template:function(n,i){}})}}return o})();var c=d(89417),g=d(21413),u=d(56977),C=d(45243),Ie=d(30450);function Re(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",8),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.saveCommunication())}),e.EFF(1,"Save"),e.k0s()}}let q=(()=>{class o{constructor(t){this.dataService=t,this.componentDestroyed$=new g.B,this.showSave=!1}ngOnInit(){this.createFormControls(),this.createForm(),this.communicationsForm.valueChanges.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.communicationsForm.valid&&(this.showSave=!0)})}createFormControls(){this.authorInstanceCompletion=new c.hs(this.feature.featureCommunication.authorInstanceCompletion,[c.k0.required]),this.authorInstanceFeedback=new c.hs(this.feature.featureCommunication.authorInstanceFeedback,[c.k0.required]),this.authorInstanceLive=new c.hs(this.feature.featureCommunication.authorInstanceLive,[c.k0.required]),this.userInstanceCompletion=new c.hs(this.feature.featureCommunication.userInstanceCompletion,[c.k0.required]),this.userAchievement=new c.hs(this.feature.featureCommunication.userAchievement,[c.k0.required])}createForm(){this.communicationsForm=new c.J3({authorInstanceCompletion:this.authorInstanceCompletion,authorInstanceFeedback:this.authorInstanceFeedback,authorInstanceLive:this.authorInstanceLive,userInstanceCompletion:this.userInstanceCompletion,userAchievement:this.userAchievement})}saveCommunication(){this.communicationsForm.valid&&this.dataService.putFeatureCommunications(this.feature.id,this.communicationsForm.value).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.showSave=!1})}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-communications"]],inputs:{feature:"feature"},decls:36,vars:2,consts:[[3,"formGroup"],["size","11",1,"ion-padding-start"],["fill","clear","color","primary"],["color","primary","formControlName","authorInstanceCompletion",2,"width","100%"],["color","primary","formControlName","authorInstanceFeedback",2,"width","100%"],["color","primary","formControlName","authorInstanceLive",2,"width","100%"],["color","primary","formControlName","userInstanceCompletion",2,"width","100%"],["color","primary","formControlName","userAchievement",2,"width","100%"],["fill","clear","color","primary",3,"click"]],template:function(n,i){1&n&&(e.j41(0,"form",0)(1,"ion-grid")(2,"ion-row")(3,"ion-col",1)(4,"p"),e.EFF(5,"Manage the transactional communications that users receive about his feature."),e.k0s()(),e.j41(6,"ion-col"),e.DNE(7,Re,2,0,"ion-button",2),e.k0s()(),e.j41(8,"ion-row")(9,"ion-col")(10,"ion-card")(11,"ion-card-header")(12,"ion-card-title"),e.EFF(13,"Author Communications"),e.k0s(),e.j41(14,"ion-card-subtitle"),e.EFF(15,"Manage the notifications an author or publisher will receive"),e.k0s()(),e.j41(16,"ion-card-content")(17,"mat-slide-toggle",3),e.EFF(18,"Reminder to finish completing an instance."),e.k0s(),e.j41(19,"mat-slide-toggle",4),e.EFF(20,"Notify when someone provides feedback on an instance."),e.k0s(),e.j41(21,"mat-slide-toggle",5),e.EFF(22,"Notify when the instance is live on the platform."),e.k0s()()()()(),e.j41(23,"ion-row")(24,"ion-col")(25,"ion-card")(26,"ion-card-header")(27,"ion-card-title"),e.EFF(28,"User Communications"),e.k0s(),e.j41(29,"ion-card-subtitle"),e.EFF(30,"Manage the notifications an user will receive"),e.k0s()(),e.j41(31,"ion-card-content")(32,"mat-slide-toggle",6),e.EFF(33,"Remind the user to finish completing this instance"),e.k0s(),e.j41(34,"mat-slide-toggle",7),e.EFF(35,"Notify the user when they earn an achievement from this instance"),e.k0s()()()()()()()),2&n&&(e.Y8G("formGroup",i.communicationsForm),e.R7$(7),e.vxM(i.showSave?7:-1))},dependencies:[s.Jm,s.b_,s.I9,s.ME,s.HW,s.tN,s.hU,s.lO,s.ln,c.qT,c.BC,c.cb,c.j4,c.JD,Ie.sG],styles:["[_nghost-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:stretch;align-content:stretch}[_nghost-%COMP%]   ion-card[_ngcontent-%COMP%]{margin-inline:0px}[_nghost-%COMP%]   p[_ngcontent-%COMP%]{color:#aaa}[_nghost-%COMP%]   ion-card[_ngcontent-%COMP%]{background-color:#444}[_nghost-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{color:#fff}[_nghost-%COMP%]   ion-card[_ngcontent-%COMP%]   ion-card-subtitle[_ngcontent-%COMP%]{color:#aaa}[_nghost-%COMP%]   ion-card[_ngcontent-%COMP%]   .mat-mdc-slide-toggle[_ngcontent-%COMP%]{margin:4px}[_nghost-%COMP%]   .ion-padding-start[_ngcontent-%COMP%]{padding-left:unset;padding-inline-start:var(--ion-padding, 0px)}"]})}}return o})();var ee=d(96896),te=d(98823);let j=(()=>{class o{constructor(t){this.modalController=t,this.componentDestroyed$=new g.B}ngOnInit(){this.createForm()}createForm(){const t={},n={};n[this.component.id]=new c.hs({value:this.textValue,disabled:!this.component?.templateField?.isBuilderEnabled}),t.parentGroup=new c.J3(n),this.formGroup=new c.J3(t)}save(){let t=this.formGroup.get(["parentGroup",this.component.id])?.value;"Image Upload Field"===this.component.componentType.name&&null===t&&(t="imageRemoved"),"Dropdown"===this.component.componentType.name&&"User Tags"===this.component.templateField.dropDownLinkType?.title&&(t="userTagsUpdated"),this.modalController.dismiss(t)}cancel(){this.modalController.dismiss()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(s.W3))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-repository-dashboard-editor-dialog"]],inputs:{component:"component",textValue:"textValue",id:"id"},decls:11,vars:5,consts:[[1,"parent-dialog-container",2,"margin-bottom","auto"],[3,"formGroup"],[1,"component-selector"],[3,"formGroup","formGroupName","id","component"],[1,"button-container"],["fill","clear",1,"move-left",3,"click"],[1,"move-right",3,"click"]],template:function(n,i){1&n&&(e.j41(0,"div",0)(1,"form",1)(2,"div",2),e.nrm(3,"app-form-control-selector",3),e.k0s()()(),e.j41(4,"div",4)(5,"div")(6,"ion-button",5),e.bIt("click",function(){return i.cancel()}),e.EFF(7,"Cancel"),e.k0s()(),e.j41(8,"div")(9,"ion-button",6),e.bIt("click",function(){return i.save()}),e.EFF(10,"Save"),e.k0s()()()),2&n&&(e.R7$(),e.Y8G("formGroup",i.formGroup),e.R7$(2),e.Y8G("formGroup",i.formGroup)("formGroupName","parentGroup")("id",i.id)("component",i.component))},dependencies:[s.Jm,c.qT,c.cb,c.j4,c.$R,te.c],styles:[".parent-dialog-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between}.parent-dialog-container[_ngcontent-%COMP%]   .component-selector[_ngcontent-%COMP%]{margin-bottom:15px}.button-container[_ngcontent-%COMP%]{margin-top:auto;padding:5px}.move-left[_ngcontent-%COMP%]{float:left}.move-right[_ngcontent-%COMP%]{float:right}"]})}}return o})();var h=d(26450),Ge=d(59583),Ee=d(45312),Xe=d(31549),ne=d(61307),b=d(60177),O=d(2042),Ne=d(17763);const P=()=>({color:"gray","font-style":"italic"}),D=()=>({});function Ve(o,r){if(1&o&&(e.j41(0,"mat-header-cell",8),e.EFF(1),e.k0s()),2&o){const t=e.XpG(2).$implicit;e.R7$(),e.JRh(t)}}function Ae(o,r){1&o&&e.DNE(0,Ve,2,1,"mat-header-cell",11)}function Ue(o,r){if(1&o&&(e.j41(0,"mat-header-cell",9),e.EFF(1),e.k0s()),2&o){const t=e.XpG(2).$implicit;e.R7$(),e.JRh(t)}}function Le(o,r){1&o&&e.DNE(0,Ue,2,1,"mat-header-cell",12)}function ze(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",15),e.bIt("click",function(){e.eBV(t);const i=e.XpG().$implicit,a=e.XpG(2);return e.Njj(a.routeOut(i.tableId))}),e.EFF(1),e.k0s()}if(2&o){const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",t.tableId," ")}}function Ye(o,r){if(1&o&&(e.j41(0,"div"),e.EFF(1),e.k0s()),2&o){const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",t.name," ")}}function Je(o,r){if(1&o&&(e.j41(0,"div",18),e.EFF(1),e.nI1(2,"parsePipe"),e.nI1(3,"async"),e.k0s()),2&o){let t;const n=e.XpG(2).$implicit,i=e.XpG().$implicit;e.Y8G("ngStyle",n.value?e.lJ4(8,D):e.lJ4(7,P)),e.R7$(),e.SpI(" ",null!==(t=e.bMT(3,5,e.i5U(2,2,n.value,i.tableId)))&&void 0!==t?t:n.component.componentType.name," ")}}function Qe(o,r){if(1&o&&(e.j41(0,"div",22),e.nrm(1,"img",24),e.k0s()),2&o){const t=e.XpG(3).$implicit,n=e.XpG(3);e.R7$(),e.Y8G("src",n.setImageUrl(t.value),e.B4B)("alt","")}}function He(o,r){if(1&o&&(e.j41(0,"div",23),e.EFF(1),e.k0s()),2&o){const t=e.XpG(3).$implicit;e.R7$(),e.JRh(t.component.componentType.name)}}function We(o,r){if(1&o&&e.DNE(0,Qe,2,2,"div",22)(1,He,2,1,"div",23),2&o){const t=e.XpG(2).$implicit;e.vxM(t.value?0:1)}}function Ze(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-chip-list",29),e.bIt("selectedToRemoveOut",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.removeTagById(i,l,a.component))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit;e.Y8G("type",null==t.component||null==t.component.componentType?null:t.component.componentType.name)("listItems",t.listItems)}}function Ke(o,r){if(1&o&&e.nrm(0,"app-tag-popover",26),2&o){const t=e.XpG(3).$implicit,n=e.XpG().$implicit;e.Y8G("viewType",2)("selectedUserId",n.userId)("dropDownLinkType","User Tags")("component",t.component)}}function qe(o,r){if(1&o&&(e.j41(0,"div",18),e.EFF(1),e.k0s()),2&o){let t;const n=e.XpG(3).$implicit;e.Y8G("ngStyle",n.value?e.lJ4(3,D):e.lJ4(2,P)),e.R7$(),e.JRh(null!==(t=n.value)&&void 0!==t?t:n.component.componentType.name)}}function et(o,r){if(1&o&&e.nrm(0,"app-tag-popover",26),2&o){const t=e.XpG(3).$implicit,n=e.XpG().$implicit;e.Y8G("viewType",2)("selectedUserId",n.userId)("dropDownLinkType","User Tags")("component",t.component)}}function tt(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-org-publish-button",30),e.bIt("statusChanged",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.updateComponent(l.tableId,i,a.component,a.id))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit,n=e.XpG(3);e.Y8G("orgStatuses",n.orgStatuses)("columnValue",t.value)}}function nt(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-instance-publish-button",31),e.bIt("statusChanged",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.updateComponent(l.tableId,i,a.component,a.id))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit,n=e.XpG(3);e.Y8G("feature",n.feature)("columnValue",t.value)}}function ot(o,r){if(1&o&&e.DNE(0,Ze,1,2,"app-chip-list",25)(1,Ke,1,4,"app-tag-popover",26)(2,qe,2,4,"div",18)(3,et,1,4,"app-tag-popover",26)(4,tt,1,2,"app-org-publish-button",27)(5,nt,1,2,"app-instance-publish-button",28),2&o){const t=e.XpG(2).$implicit,n=e.XpG(3);e.vxM((null==t.listItems?null:t.listItems.length)>0&&"User Tags"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Organization Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Instance Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?0:(null==t.listItems?null:t.listItems.length)>0&&"User Tags"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?1:0===(null==t.listItems?null:t.listItems.length)&&"User Tags"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Organization Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Instance Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?2:0===(null==t.listItems?null:t.listItems.length)&&"User Tags"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?3:"Organization Status"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&n.orgStatuses?4:"Instance Status"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?5:-1)}}function it(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-chip-list",29),e.bIt("selectedToRemoveOut",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.removeTagById(i,l,a.component))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit;e.Y8G("type","Dropdown")("listItems",t.listItems)}}function at(o,r){if(1&o&&e.nrm(0,"app-tag-popover",26),2&o){const t=e.XpG(3).$implicit,n=e.XpG().$implicit;e.Y8G("viewType",2)("selectedUserId",n.userId)("dropDownLinkType","User Tags")("component",t.component)}}function rt(o,r){if(1&o&&(e.j41(0,"div",18),e.EFF(1),e.k0s()),2&o){let t;const n=e.XpG(3).$implicit;e.Y8G("ngStyle",n.value?e.lJ4(3,D):e.lJ4(2,P)),e.R7$(),e.JRh(null!==(t=n.value)&&void 0!==t?t:n.component.componentType.name)}}function st(o,r){if(1&o&&e.nrm(0,"app-tag-popover",26),2&o){const t=e.XpG(3).$implicit,n=e.XpG().$implicit;e.Y8G("viewType",2)("selectedUserId",n.userId)("dropDownLinkType","User Tags")("component",t.component)}}function lt(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-org-publish-button",30),e.bIt("statusChanged",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.updateComponent(l.tableId,i,a.component,a.id))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit,n=e.XpG(3);e.Y8G("orgStatuses",n.orgStatuses)("columnValue",t.value)}}function ct(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-instance-publish-button",31),e.bIt("statusChanged",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.updateComponent(l.tableId,i,a.component,a.id))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit,n=e.XpG(3);e.Y8G("feature",n.feature)("columnValue",t.value)}}function pt(o,r){if(1&o&&e.DNE(0,it,1,2,"app-chip-list",25)(1,at,1,4,"app-tag-popover",26)(2,rt,2,4,"div",18)(3,st,1,4,"app-tag-popover",26)(4,lt,1,2,"app-org-publish-button",27)(5,ct,1,2,"app-instance-publish-button",28),2&o){const t=e.XpG(2).$implicit,n=e.XpG(3);e.vxM((null==t.listItems?null:t.listItems.length)>0&&"User Tags"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Organization Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Instance Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?0:(null==t.listItems?null:t.listItems.length)>0&&"User Tags"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?1:0===(null==t.listItems?null:t.listItems.length)&&"User Tags"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Organization Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Instance Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?2:0===(null==t.listItems?null:t.listItems.length)&&"User Tags"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?3:"Organization Status"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&n.orgStatuses?4:"Instance Status"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?5:-1)}}function dt(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-chip-list",29),e.bIt("selectedToRemoveOut",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.removeTagById(i,l,a.component))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit;e.Y8G("type",null==t.component||null==t.component.componentType?null:t.component.componentType.name)("listItems",t.listItems)}}function ut(o,r){if(1&o&&e.nrm(0,"app-tag-popover",26),2&o){const t=e.XpG(3).$implicit,n=e.XpG().$implicit;e.Y8G("viewType",2)("selectedUserId",n.userId)("dropDownLinkType","User Tags")("component",t.component)}}function mt(o,r){if(1&o&&(e.j41(0,"div",18),e.EFF(1),e.k0s()),2&o){let t;const n=e.XpG(3).$implicit;e.Y8G("ngStyle",n.value?e.lJ4(3,D):e.lJ4(2,P)),e.R7$(),e.JRh(null!==(t=n.value)&&void 0!==t?t:n.component.componentType.name)}}function _t(o,r){if(1&o&&e.nrm(0,"app-tag-popover",26),2&o){const t=e.XpG(3).$implicit,n=e.XpG().$implicit;e.Y8G("viewType",2)("selectedUserId",n.userId)("dropDownLinkType","User Tags")("component",t.component)}}function ht(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-org-publish-button",30),e.bIt("statusChanged",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.updateComponent(l.tableId,i,a.component,a.id))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit,n=e.XpG(3);e.Y8G("orgStatuses",n.orgStatuses)("columnValue",t.value)}}function gt(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-instance-publish-button",31),e.bIt("statusChanged",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.updateComponent(l.tableId,i,a.component,a.id))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit,n=e.XpG(3);e.Y8G("feature",n.feature)("columnValue",t.value)}}function ft(o,r){if(1&o&&e.DNE(0,dt,1,2,"app-chip-list",25)(1,ut,1,4,"app-tag-popover",26)(2,mt,2,4,"div",18)(3,_t,1,4,"app-tag-popover",26)(4,ht,1,2,"app-org-publish-button",27)(5,gt,1,2,"app-instance-publish-button",28),2&o){const t=e.XpG(2).$implicit,n=e.XpG(3);e.vxM((null==t.listItems?null:t.listItems.length)>0&&"User Tags"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Organization Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Instance Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?0:(null==t.listItems?null:t.listItems.length)>0&&"User Tags"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?1:0===(null==t.listItems?null:t.listItems.length)&&"User Tags"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Organization Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&"Instance Status"!==(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?2:0===(null==t.listItems?null:t.listItems.length)&&"User Tags"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?3:"Organization Status"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)&&n.orgStatuses?4:"Instance Status"===(null==t.component||null==t.component.templateField||null==t.component.templateField.dropDownLinkType?null:t.component.templateField.dropDownLinkType.title)?5:-1)}}function Ct(o,r){if(1&o&&e.nrm(0,"app-persona-chip-list",19),2&o){const t=e.XpG(2).$implicit;e.Y8G("existingTags",t.listItems)("component",t.component)("selectedUserId",t.id)}}function bt(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-chip-list",29),e.bIt("selectedToRemoveOut",function(i){e.eBV(t);const a=e.XpG(3).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.removeTagById(i,l,a.component))}),e.k0s()}if(2&o){const t=e.XpG(3).$implicit;e.Y8G("type",null==t.component||null==t.component.componentType?null:t.component.componentType.name)("listItems",t.listItems)}}function yt(o,r){if(1&o&&(e.j41(0,"div",23),e.EFF(1),e.k0s()),2&o){const t=e.XpG(3).$implicit;e.R7$(),e.JRh(t.component.componentType.name)}}function Tt(o,r){if(1&o&&e.DNE(0,bt,1,2,"app-chip-list",25)(1,yt,2,1,"div",23),2&o){const t=e.XpG(2).$implicit;e.vxM((null==t.listItems?null:t.listItems.length)>0?0:1)}}function vt(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",20)(1,"ion-button",32),e.bIt("click",function(i){e.eBV(t);const a=e.XpG(2).$implicit;return e.XpG(3).resetPassword(a.id),e.Njj(i.stopPropagation())}),e.EFF(2,"Reset Password"),e.k0s()()}}function xt(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",20)(1,"ion-button",32),e.bIt("click",function(i){e.eBV(t);const a=e.XpG(3).$implicit;return e.XpG(2).updateProductOrgUserRole(a),e.Njj(i.stopPropagation())}),e.EFF(2,"User Organizations"),e.k0s()()}}function Ft(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-checkbox-value",33),e.nI1(1,"parsePipe"),e.nI1(2,"async"),e.bIt("checkboxChanged",function(i){e.eBV(t);const a=e.XpG(2).$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(p.updateComponent(l.tableId,i,a.component,a.id))}),e.k0s()}if(2&o){let t;const n=e.XpG(2).$implicit,i=e.XpG().$implicit;e.Y8G("inheritedPropertyValue",e.bMT(2,6,e.i5U(1,3,n.value,i.tableId)))("value",n.value)("label",null!==(t=null==n||null==n.component||null==n.component.templateField?null:n.component.templateField.label)&&void 0!==t?t:"")}}function Mt(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",17),e.bIt("click",function(){let i;e.eBV(t);const a=e.XpG().$implicit,l=e.XpG().$implicit,p=e.XpG(2);return e.Njj(null!==(i="Password"!==(null==a.component||null==a.component.componentType?null:a.component.componentType.name))&&void 0!==i?i:p.editDashboardComponent(l.tableId,a.value,a.component,a.id))}),e.DNE(1,Je,4,9,"div",18)(2,We,2,1)(3,ot,6,1)(4,pt,6,1)(5,ft,6,1)(6,Ct,1,3,"app-persona-chip-list",19)(7,Tt,2,1)(8,vt,3,0,"div",20)(9,xt,3,0,"div",20)(10,Ft,3,8,"app-checkbox-value",21),e.k0s()}if(2&o){let t;const n=e.XpG().$implicit;e.R7$(),e.vxM((t=null==n.component||null==n.component.componentType?null:n.component.componentType.name)===("Text"===(null==n.component||null==n.component.componentType?null:n.component.componentType.name)||"Text Area"===(null==n.component||null==n.component.componentType?null:n.component.componentType.name)||"WYSIWYG"===(null==n.component||null==n.component.componentType?null:n.component.componentType.name)?null==n.component||null==n.component.componentType?null:n.component.componentType.name:"")?1:"Image Upload Field"===t?2:"Dropdown"===t?3:"Icon & Dropdown"===t?4:"Icon"===t?5:"Persona Selector"===t?6:"Organization Networks"===t?7:"Password"===t?8:"Organization"===t?9:"Checkbox"===t?10:-1)}}function kt(o,r){if(1&o&&e.DNE(0,Mt,11,1,"div",16),2&o){const t=r.$implicit,n=e.XpG(2).$implicit;e.vxM(t.columnName===n?0:-1)}}function Ot(o,r){if(1&o&&(e.j41(0,"mat-cell",13),e.DNE(1,ze,2,1,"div",14)(2,Ye,2,1,"div"),e.Z7z(3,kt,1,1,null,null,e.fX1),e.k0s()),2&o){let t;const n=r.$implicit,i=e.XpG().$implicit;e.R7$(),e.vxM("Id"===(t=i)?1:"Name"===t?2:-1),e.R7$(2),e.Dyx(n.columns)}}function Pt(o,r){if(1&o&&(e.qex(0,4),e.DNE(1,Ae,1,0,"mat-header-cell",8)(2,Le,1,0,"mat-header-cell",9)(3,Ot,5,1,"mat-cell",10),e.bVm()),2&o){const t=r.$implicit,n=e.XpG();e.Y8G("matColumnDef",t)("sticky","Id"===t||"Name"===t),e.R7$(),e.vxM(n.checkColumnIsSystemProperty(t)||"Name"===t?1:2)}}function Dt(o,r){1&o&&e.nrm(0,"mat-header-row",34)}function wt(o,r){1&o&&e.nrm(0,"mat-row",35)}function It(o,r){1&o&&(e.j41(0,"div",37)(1,"h3"),e.EFF(2,"Loading..."),e.k0s()())}function Rt(o,r){1&o&&(e.j41(0,"div",7),e.DNE(1,It,3,0,"ng-template",null,0,e.C5r),e.nrm(3,"ion-spinner",36),e.k0s())}let $=(()=>{class o{constructor(t,n,i){this.modalController=t,this.dataService=n,this.globalToast=i,this.routeOnClick=new e.bkB,this.loadMoreData=new e.bkB,this.refreshData=new e.bkB,this.sortChange=new e.bkB,this.dataSource=new h.I6,this.componentDestroyed$=new g.B,this.previousScrollTop=0}ngOnInit(){this.initData()}checkColumnIsSystemProperty(t){const n=this.dataSourceIn[0].columns.find(i=>i.columnName===t);return!(!n||!n?.component?.templateField?.systemProperty)}onSortChange(t){const n=this.dataSourceIn[0].columns.find(i=>i.columnName===t.active);if(n&&n?.component?.templateField?.systemProperty){const i=n.component.templateField.systemProperty.property;t.active=i.substring(i.indexOf(".")+1)}this.sortChange.next(t)}routeOut(t){this.routeOnClick.emit(t)}loadMoreEmit(t){this.loadMoreData.emit(t)}initData(){this.dataService.getOrganizationStatusTypes().pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.orgStatuses=t})}scrolled(t){t.target.scrollTop+t.target.offsetHeight>=t.target.scrollHeight&&this.previousScrollTop!=t.target.scrollTop&&this.loadMoreEmit(t),this.previousScrollTop=t.target.scrollTop}editDashboardComponent(t,n,i,a){var l=this;return(0,_.A)(function*(){if("Organization Status"!==i?.templateField?.dropDownLinkType?.title&&i?.templateField.isBuilderEnabled){const p=yield l.modalController.create({component:j,componentProps:{component:i,textValue:n,id:t},cssClass:"repo-dashboard-dialog"});p.onDidDismiss().then(m=>{m.data&&("imageRemoved"===m.data||"userTagsUpdated"===m.data?l.refreshData.emit():l.updateComponent(t,m.data,i,a))}),yield p.present()}})()}updateComponent(t,n,i,a){if(null!=n?.value&&(n=n.value),i?.templateField?.systemProperty){const l=i.templateField.systemProperty;this.dataService.setSystemPropertyContextValues(t,l.type.typeBw,[{id:l.id,key:l.property,value:n}]).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(m=>{m&&this.refreshData.emit()})}"Campaign User Tags"===i?.templateField?.dropDownLinkType?.title&&this.dataService.updateCampaignTags(n,a).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(l=>{l&&this.refreshData.emit()}),i?.templateField?.isTag&&this.refreshData.emit()}updateProductOrgUserRole(t){var n=this;return(0,_.A)(function*(){const i=t.columns.filter(m=>"First Name (*)"===m.columnName)[0].value,a=t.columns.filter(m=>"Last Name (*)"===m.columnName)[0].value,l=t.userId;yield(yield n.modalController.create({component:Ge.Q,cssClass:"user-organizations-modal",componentProps:{userId:l,organizationId:"00000000-0000-0000-0000-000000000000",featureType:"User Repository",name:`${i} ${a}`},backdropDismiss:!1})).present()})()}setImageUrl(t){return`${Ee.c.contentUrl}asset/${t}/content`}resetPassword(t){const n=JSON.stringify(t);this.dataService.specificUserPasswordReset(n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.globalToast.presentNotificationToast("Password Reset Successfully",!1,!1)})}removeTagById(t,n,i){var a=this;return(0,_.A)(function*(){if("Campaign User Tags"===i?.templateField?.dropDownLinkType?.title||"User Tags"===i?.templateField?.dropDownLinkType?.title){const l=yield a.modalController.create({component:x.D,cssClass:"confirm-dialog",componentProps:{headerText:"Remove User Tag",bodyText:`Are you sure you would like to remove ${t.name}?`,buttonText:"Remove"}});l.onDidDismiss().then(p=>{"confirm"===p.role&&a.dataService.deleteUserCampaignTag(t.id,n.userId).pipe((0,u.Q)(a.componentDestroyed$)).subscribe(m=>{m&&a.refreshData.emit()})}),yield l.present()}else if("Tags"===i?.templateField?.dropDownLinkType?.title){const l=yield a.modalController.create({component:x.D,cssClass:"confirm-dialog",componentProps:{headerText:"Remove Instance Tag",bodyText:`Are you sure you would like to remove ${t.name}?`,buttonText:"Remove"}});l.onDidDismiss().then(p=>{"confirm"===p.role&&a.dataService.deleteInstanceTag(t.id,n.tableId,i.id).pipe((0,u.Q)(a.componentDestroyed$)).subscribe(m=>{m&&a.refreshData.emit()})}),yield l.present()}})()}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(s.W3),e.rXU(C.u),e.rXU(Xe.A))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-repository-dashboard-table"]],inputs:{dataSourceIn:"dataSourceIn",columnsToDisplayIn:"columnsToDisplayIn",feature:"feature",loading:"loading"},outputs:{routeOnClick:"routeOnClick",loadMoreData:"loadMoreData",refreshData:"refreshData",sortChange:"sortChange"},decls:8,vars:5,consts:[["customLoadingTemplate",""],[1,"scroll-box",3,"scroll"],[1,"table-container"],["matSort","",1,"mat-elevation-z8",3,"matSortChange","dataSource"],[3,"matColumnDef","sticky"],["mat-header-row","",4,"matHeaderRowDef","matHeaderRowDefSticky"],["mat-row","","style","cursor: pointer",4,"matRowDef","matRowDefColumns"],[1,"scroll-container"],["mat-header-cell","","mat-sort-header",""],["mat-header-cell",""],["mat-cell","",4,"matCellDef"],["mat-header-cell","","mat-sort-header","",4,"matHeaderCellDef"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell",""],["aria-hidden","true",1,"id-route"],["aria-hidden","true",1,"id-route",3,"click"],["aria-hidden","true"],["aria-hidden","true",3,"click"],[1,"inner-container",3,"ngStyle"],[3,"existingTags","component","selectedUserId"],[1,"inner-container"],[3,"inheritedPropertyValue","value","label"],[1,"inner-container","image-container"],[1,"inner-container",2,"color","'gray'","font-style","'italic'"],[3,"src","alt"],[3,"type","listItems"],[3,"viewType","selectedUserId","dropDownLinkType","component"],[3,"orgStatuses","columnValue"],[3,"feature","columnValue"],[3,"selectedToRemoveOut","type","listItems"],[3,"statusChanged","orgStatuses","columnValue"],[3,"statusChanged","feature","columnValue"],[1,"resetPassword",3,"click"],[3,"checkboxChanged","inheritedPropertyValue","value","label"],["mat-header-row",""],["mat-row","",2,"cursor","pointer"],["name","lines"],[1,"custom-class"]],template:function(n,i){1&n&&(e.j41(0,"div",1),e.bIt("scroll",function(l){return i.scrolled(l)}),e.j41(1,"div",2)(2,"mat-table",3),e.bIt("matSortChange",function(l){return i.onSortChange(l)}),e.Z7z(3,Pt,4,3,"ng-container",4,e.fX1),e.DNE(5,Dt,1,0,"mat-header-row",5)(6,wt,1,0,"mat-row",6),e.k0s()()(),e.DNE(7,Rt,4,0,"div",7)),2&n&&(e.R7$(2),e.Y8G("dataSource",i.dataSourceIn),e.R7$(),e.Dyx(i.columnsToDisplayIn),e.R7$(2),e.Y8G("matHeaderRowDef",i.columnsToDisplayIn)("matHeaderRowDefSticky",!0),e.R7$(),e.Y8G("matRowDefColumns",i.columnsToDisplayIn),e.R7$(),e.vxM(i.loading?7:-1))},dependencies:[V.M,Y.L,L.i,ne.k,b.B3,s.Jm,s.w2,h.Zl,h.tL,h.ji,h.cC,h.YV,h.iL,h.KS,h.$R,h.YZ,h.NB,O.B4,O.aE,Z,H,b.Jj,Ne.F],styles:[".table-container[_ngcontent-%COMP%]{height:100%;width:fit-content}.table-container[_ngcontent-%COMP%]   .id-route[_ngcontent-%COMP%]:hover{color:orange;text-decoration:underline}.table-container[_ngcontent-%COMP%]   mat-table[_ngcontent-%COMP%]{background-color:#181818!important}.table-container[_ngcontent-%COMP%]   mat-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%]{color:#fff;background-color:#222!important;padding-left:15px;font-size:16px;border-width:0px 1px 1px 0px;border-right-style:solid;border-right-color:#000;border-bottom-color:gray;border-left-color:#222;max-width:400px;min-width:290px}.table-container[_ngcontent-%COMP%]   mat-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]{background-color:#2a2a2a;color:#fff;padding:10px;border-width:0px 1px 1px 0px;border-style:solid solid;border-color:#000}.table-container[_ngcontent-%COMP%]   mat-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{max-height:150px;min-height:30px;display:flex;align-items:center;overflow:auto}.table-container[_ngcontent-%COMP%]   mat-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{width:100%;height:150px}.table-container[_ngcontent-%COMP%]   mat-table[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%}.table-container[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:gray}.scroll-container[_ngcontent-%COMP%]{position:absolute;bottom:0;width:100px;height:100px;right:0}.scroll-box[_ngcontent-%COMP%]{height:calc(100% - 60px);overflow-y:scroll;white-space:nowrap;position:relative}"]})}}return o})();var w=d(49524),St=d(56375),f=d(96354),B=d(70152),I=d(64985);function jt(o,r){1&o&&(e.j41(0,"div",15),e.EFF(1,"Update the status of synced badges in the Credential Engine."),e.k0s(),e.j41(2,"div",16)(3,"span"),e.EFF(4,"Status will be auto-saved when modified."),e.k0s()())}function $t(o,r){1&o&&(e.j41(0,"div",15),e.EFF(1,"What badges do you want to sync with Credential Engine?"),e.k0s(),e.j41(2,"div",16)(3,"span"),e.EFF(4,"Select the badge(s) you want to sync"),e.k0s()())}function Bt(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",17),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.toggleSyncButton())}),e.EFF(1," Sync Badges "),e.k0s()}}function Gt(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",17),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.toggleSyncButton())}),e.EFF(1," Edit Badge Status"),e.k0s()}}function Et(o,r){1&o&&e.eu8(0)}function Xt(o,r){if(1&o&&e.DNE(0,Et,1,0,"ng-container",18),2&o){e.XpG(2);const t=e.sdS(28);e.Y8G("ngTemplateOutlet",t)}}function Nt(o,r){1&o&&e.eu8(0)}function Vt(o,r){if(1&o&&e.DNE(0,Nt,1,0,"ng-container",18),2&o){e.XpG(2);const t=e.sdS(30);e.Y8G("ngTemplateOutlet",t)}}function At(o,r){if(1&o&&e.DNE(0,Xt,1,1,"ng-container")(1,Vt,1,1,"ng-container"),2&o){const t=e.XpG();e.vxM(t.showSyncedBadges?0:1)}}function Ut(o,r){1&o&&(e.j41(0,"div",19)(1,"span",20),e.EFF(2,"No Badges"),e.k0s()())}function Lt(o,r){if(1&o&&e.DNE(0,Ut,3,0,"div",19),2&o){const t=e.XpG();e.vxM(!0===t.noBadgesInTable?0:-1)}}function zt(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-badge"),e.EFF(1),e.k0s(),e.j41(2,"ion-button",17),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.syncSelectedBadges())}),e.EFF(3," Sync "),e.k0s()}if(2&o){const t=e.XpG();e.R7$(),e.JRh(t.selectedBadges.length)}}function Yt(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-item")(1,"ion-label")(2,"span",21),e.EFF(3),e.k0s(),e.j41(4,"app-select-option-control",22),e.bIt("valueChanged",function(i){const a=e.eBV(t).$implicit,l=e.XpG(2);return e.Njj(l.updateStatus(i,a.id))}),e.k0s()()()}if(2&o){let t;const n=r.$implicit,i=e.XpG(2);e.R7$(3),e.JRh(n.name),e.R7$(),e.Y8G("toolTip","Select Status")("placeHolder","Select Status")("label","Badge Status")("backgroundColor",i.controlBackground)("textValue",null!==(t=n.status)&&void 0!==t?t:"")("options",i.badgeStatusTypes)}}function Jt(o,r){if(1&o&&(e.j41(0,"ion-list"),e.Z7z(1,Yt,5,7,"ion-item",null,e.fX1),e.k0s()),2&o){const t=e.XpG();e.R7$(),e.Dyx(t.badges)}}function Qt(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-item",26),e.bIt("ionChange",function(i){const a=e.eBV(t).$implicit,l=e.XpG(2);return e.Njj(l.addBadgeToList(i,a))}),e.nrm(1,"ion-checkbox",27),e.j41(2,"ion-label")(3,"div",28)(4,"span",21),e.EFF(5),e.k0s(),e.j41(6,"span",29),e.EFF(7),e.k0s()()()()}if(2&o){const t=r.$implicit;e.R7$(),e.Y8G("checked",t.selected),e.R7$(4),e.JRh(t.name),e.R7$(2),e.JRh(t.published?"Updated - Sync Required":"Not Synced")}}function Ht(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-list")(1,"ion-item",23),e.bIt("ionChange",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.addAllBadgesToList(i,a.badges))}),e.j41(2,"div",24),e.nrm(3,"ion-checkbox",25),e.j41(4,"ion-label")(5,"span",21),e.EFF(6,"Select All"),e.k0s()()()(),e.Z7z(7,Qt,8,3,"ion-item",null,e.fX1),e.k0s()}if(2&o){const t=e.XpG();e.R7$(7),e.Dyx(t.badges)}}let Wt=(()=>{class o{constructor(t,n,i){this.modalController=t,this.dataService=n,this.alertService=i,this.showSyncedBadges=!1,this.badges=[],this.selectedBadges=[],this.controlBackground="#181818",this.badgeStatusTypes=[{id:"Active",value:"Active"},{id:"Deprecated",value:"Deprecated"}],this.componentDestroyed$=new g.B}ngOnInit(){this.createFormControls(),this.createForm(),this.setBadges()}createFormControls(){this.searchValue=new c.hs("")}createForm(){this.searchForm=new c.J3({searchValue:this.searchValue})}searchRepoValue(){this.setBadges()}toggleSyncButton(){this.searchForm.patchValue({searchValue:""}),this.showSyncedBadges=!this.showSyncedBadges,this.setBadges()}updateStatus(t,n){this.dataService.upsertBadge(n,t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe()}setBadges(){this.badges=[],this.getBadges().pipe((0,u.Q)(this.componentDestroyed$),(0,B.B)(1e3)).subscribe(t=>{t&&(this.noBadgesInTable=0===t.length,this.badges=t)})}getBadges(){return this.showSyncedBadges?this.dataService.getSyncedBadges(this.searchValue.value):this.dataService.getUnsyncedOrNewBadges(this.searchValue.value)}addAllBadgesToList(t,n){t.detail.checked?n.map(i=>{i.selected||(i.selected=!0,this.selectedBadges.push(i))}):(n.map(i=>{i.selected=!1}),this.selectedBadges=[])}addBadgeToList(t,n){if(t.detail.checked)n.selected=!0,this.selectedBadges.push(n);else{const i=this.selectedBadges.findIndex(a=>a.id===n.id);n.selected=!0,this.selectedBadges.splice(i,1)}}close(){this.modalController.dismiss()}syncSelectedBadges(){0!=this.selectedBadges.length&&this.dataService.bulkUpsertBadges(this.selectedBadges).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{if(0==t.length)this.alertService.presentAlert("Success","Badge(s) synced."),this.modalController.dismiss(this.selectedBadges);else{const n=t.join("\n\n");this.alertService.presentAlert("Badge Sync Errors",n)}})}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(s.W3),e.rXU(C.u),e.rXU(I.u))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-sync-badge-to-credential-engine-modal"]],standalone:!0,features:[e.aNF],decls:31,vars:6,consts:[["syncedBadges",""],["unsyncedBadges",""],[1,"badge-search-container"],[3,"formGroup"],[1,"header-col"],[1,"search-bar-row"],["size","7"],["color","dark","formControlName","searchValue","type","search","placeholder","Search Badge","showCancelButton","focus","debounce","600",3,"ionChange"],["size","5",1,"end-col-buttons"],[1,"buttons"],[1,"footer-content"],[1,"cancel-col"],["fill","clear",3,"click"],[1,"add-col"],[1,"inner-container"],[1,"top-heading"],[1,"sub-heading"],[3,"click"],[4,"ngTemplateOutlet"],[1,"no-badge-container"],[1,"no-badge-text"],[1,"heading"],[3,"valueChanged","toolTip","placeHolder","label","backgroundColor","textValue","options"],[1,"select-container",3,"ionChange"],[1,"select-all"],["slot","start"],[3,"ionChange"],["slot","start",3,"checked"],[1,"text-container"],[1,"newBadge"]],template:function(n,i){if(1&n){const a=e.RV6();e.j41(0,"div",2)(1,"form",3)(2,"ion-grid")(3,"ion-row")(4,"ion-col",4),e.DNE(5,jt,5,0)(6,$t,5,0),e.k0s()(),e.j41(7,"ion-row",5)(8,"ion-col",6)(9,"ion-searchbar",7),e.bIt("ionChange",function(){return e.eBV(a),e.Njj(i.searchRepoValue())}),e.k0s()(),e.j41(10,"ion-col",8)(11,"div",9),e.DNE(12,Bt,2,0,"ion-button")(13,Gt,2,0,"ion-button"),e.k0s()()()()(),e.j41(14,"ion-content")(15,"ion-grid"),e.DNE(16,At,2,1)(17,Lt,1,1),e.k0s()(),e.j41(18,"ion-footer")(19,"div",10)(20,"ion-row")(21,"ion-col",11)(22,"ion-button",12),e.bIt("click",function(){return e.eBV(a),e.Njj(i.close())}),e.EFF(23,"Cancel"),e.k0s()(),e.j41(24,"ion-col",13)(25,"div",14),e.DNE(26,zt,4,1),e.k0s()()()()(),e.DNE(27,Jt,3,0,"ng-template",null,0,e.C5r)(29,Ht,9,0,"ng-template",null,1,e.C5r),e.k0s()}2&n&&(e.R7$(),e.Y8G("formGroup",i.searchForm),e.R7$(4),e.vxM(i.showSyncedBadges?5:i.showSyncedBadges?-1:6),e.R7$(7),e.vxM(i.showSyncedBadges?12:i.showSyncedBadges?-1:13),e.R7$(4),e.vxM(i.badges&&i.badges.length>0?16:-1),e.R7$(),e.vxM(i.badges&&0===i.badges.length?17:-1),e.R7$(9),e.vxM(i.showSyncedBadges?-1:26))},dependencies:[c.YN,c.qT,c.BC,c.cb,c.X1,c.j4,c.JD,s.bv,s.In,s.Jm,s.eY,s.hU,s.W9,s.M0,s.lO,s.uz,s.he,s.nf,s.ln,s.S1,s.hB,s.Gw,b.MD,b.T3,v.c],styles:[".badge-search-container[_ngcontent-%COMP%]{padding:10px;width:100%;height:100%}.badge-search-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]{text-align:center}.badge-search-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]   .step-heading[_ngcontent-%COMP%]{font-style:italic;color:#aaa}.badge-search-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]   .top-heading[_ngcontent-%COMP%]{font-weight:700;font-size:20px;color:#fff}.badge-search-container[_ngcontent-%COMP%]   .header-col[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{margin-bottom:10px;color:#aaa}.badge-search-container[_ngcontent-%COMP%]   .search-bar-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:16px;height:60px}.badge-search-container[_ngcontent-%COMP%]   .search-bar-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-searchbar[_ngcontent-%COMP%]{border-radius:5px;border:2px solid rgba(0,0,0,.3);padding:0}.badge-search-container[_ngcontent-%COMP%]   .search-bar-row[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{border-radius:5px;color:#000;--background:#f99e00}.badge-search-container[_ngcontent-%COMP%]   .search-bar-row[_ngcontent-%COMP%]   .end-col-buttons[_ngcontent-%COMP%]{justify-content:flex-end}.badge-search-container[_ngcontent-%COMP%]   .search-bar-row[_ngcontent-%COMP%]   .end-col-buttons[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{display:flex;gap:10px}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]{background-color:#292929;height:65%;border-radius:5px}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]{background-color:#292929;padding:0}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .select-container[_ngcontent-%COMP%]{position:sticky;z-index:1000;top:0;margin:0;border-radius:0}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .select-container[_ngcontent-%COMP%]   .select-all[_ngcontent-%COMP%]{margin:0 10px;display:flex;align-items:center}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   .select-container[_ngcontent-%COMP%]   .select-all[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{margin-left:10px}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--background-hover: var(--background);border-radius:8px;margin:10px}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .text-container[_ngcontent-%COMP%]{display:flex}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-weight:700;font-size:16px;color:#fff;flex:1}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .newBadge[_ngcontent-%COMP%]{display:flex;justify-content:end;align-items:center;color:#f99e00;font-size:14px;font-weight:700;margin-left:20px}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{font-style:italic;color:#aaa}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .load-more[_ngcontent-%COMP%]{font-size:16px;color:#fff;margin-top:20px;text-align:center;display:flex;justify-content:center;cursor:pointer}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .no-badge-container[_ngcontent-%COMP%]{width:100%;height:110px;display:flex;justify-content:center;align-items:center}.badge-search-container[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ion-grid[_ngcontent-%COMP%]   .no-badge-container[_ngcontent-%COMP%]   .no-badge-text[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#f99e00}.badge-search-container[_ngcontent-%COMP%]   ion-footer[_ngcontent-%COMP%]{margin-top:15px}.badge-search-container[_ngcontent-%COMP%]   ion-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{padding:0}.badge-search-container[_ngcontent-%COMP%]   ion-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .cancel-col[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{color:#aaa}.badge-search-container[_ngcontent-%COMP%]   ion-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .add-col[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.badge-search-container[_ngcontent-%COMP%]   ion-footer[_ngcontent-%COMP%]   .footer-content[_ngcontent-%COMP%]   .add-col[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%]{background-color:#ea8629;z-index:999;position:absolute;right:-2px;top:-9px}"]})}}return o})();var Zt=d(15389),G=d(18544);function Kt(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",11),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.openSyncBadgeModal())}),e.EFF(1," Sync Badges "),e.k0s()}}function qt(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",11),e.bIt("click",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.add(i))}),e.nrm(1,"mat-icon",12),e.EFF(2," Add "),e.k0s()}}function en(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-repository-dashboard-table",13),e.bIt("routeOnClick",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.openInstanceBuilder(i))})("loadMoreData",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.getRepositoryDashboard(i))})("refreshData",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.refreshData())})("sortChange",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.sortChanged(i))}),e.k0s()}if(2&o){const t=e.XpG();e.Y8G("feature",t.feature)("columnsToDisplayIn",t.displayedColumns)("dataSourceIn",t.dataSource)("loading",t.loading)}}let oe=(()=>{class o{constructor(t,n,i,a,l,p){this.dataService=t,this.popOver=n,this.alertController=i,this.authService=a,this.instanceService=l,this.modalController=p,this.componentDestroyed$=new g.B,this.staticColumns=["Name","Id"],this.currentAmount=0,this.getAmount=25,this.showAdd=!0,this.loading=!1,this.searchQuery="",this.controlBackground="#222428"}ngOnInit(){this.feature.featureType?this.name=this.feature.featureType.name:this.showAdd=!1,this.name&&("Internal"===this.name||this.name.includes("User Manager")||this.name.includes("Search"))&&(this.showAdd=!1),this.createFormControls(),this.createForm(),this.getRepositoryDashboard(!1),this.initData()}initData(){this.dataService.getOrganizationStatusTypes().pipe((0,f.T)(t=>t.map(n=>({id:n.id,value:n.name}))),(0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.orgStatusTypes=t})}createFormControls(){this.featureRepoSearchValue=new c.hs("")}createForm(){this.searchForm=new c.J3({featureRepoSearchValue:this.featureRepoSearchValue})}searchRepoValue(t){this.currentAmount=0,this.featureRepoSearchValue.setValue(t.target.value),this.getRepositoryDashboard(!1)}filterValues(t){this.statusTypeId=t,this.currentAmount=0,this.refreshData()}getRepositoryDashboard(t){if(!this.name||this.loading)return;const n=encodeURIComponent(this.featureRepoSearchValue.value);this.loading=!0,this.dataService.getFeatureRepositoryDashboard(this.feature.id,this.name,{repoSearchValue:n,currentAmount:this.currentAmount,getAmount:this.getAmount,sortBy:this.sortBy,sortDir:this.sortDir,filterField:"Status",filterValue:this.statusTypeId}).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{if(null!=i.rows&&i.rows.length>0){if(t)i.rows.forEach(a=>{this.dataSource=[...this.dataSource,a]});else{if(this.dataSource=i.rows,i.columnNames.some(a=>"Status"===a)){const a=i.columnNames.indexOf("Status");if(-1!==a){const l=i.columnNames.splice(a,1);i.columnNames.push(l[0])}}this.displayedColumns=i.columnNames,this.setStaticColumns()}this.currentAmount+=i.rows.length}this.loading=!1})}refreshData(){this.getRepositoryDashboard(!1)}setStaticColumns(){this.staticColumns.forEach(t=>{this.displayedColumns.includes(t)||this.displayedColumns.unshift(t)})}openSyncBadgeModal(){var t=this;return(0,_.A)(function*(){yield(yield t.modalController.create({component:Wt,componentProps:{}})).present()})()}add(t){switch(this.name){case"Product Manager":this.addProduct();break;case"Question Manager":this.openSelectQuestionType(t);break;case"Organization Manager":this.addOrganization();break;case"Communication Manager":this.addCommunication();break;case"Media Manager":this.addMedia(t);break;case"Network Manager":this.addNetwork();break;case"Campaign Manager":this.addCampaign();break;case"Feature Manager":this.addFeature(t);break;case"User Manager":break;default:this.addInstance(t)}}openSelectQuestionType(t){var n=this;return(0,_.A)(function*(){n.dataService.getQuestionTypes().subscribe(function(){var i=(0,_.A)(function*(a){if(a){const l=yield n.popOver.create({component:M.u,cssClass:"question-type-popover",componentProps:{options:a.map(p=>({key:p.id,value:p.name}))},event:t,side:"bottom"});l.onDidDismiss().then(p=>{p.data&&n.createQuestion(p.data.key)}),yield l.present()}});return function(a){return i.apply(this,arguments)}}())})()}createQuestion(t){this.dataService.createQuestion({id:null,questionTypeId:t}).subscribe(n=>{n&&this.openInstanceBuilder(n)})}createInstances(t){const n=[];for(const i of t)n.push(this.populateNewInstanceIn(i));this.dataService.createInstances(n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{})}openInstanceBuilder(t){if(null!=this.feature.featureType)if("Feature Manager"===this.feature.featureType.name)this.instanceService.openInstance("repository/builder",t);else if("Favorites"!==this.name&&"Portfolio"!==this.name)if("Product Manager"===this.name||"Organization Manager"===this.name||"Question Manager"===this.name||"Network Manager"===this.name||"Communication Manager"===this.name||"Campaign Manager"===this.name)this.instanceService.openInstance(this.feature.featureSlug,t,"default","builder");else if("User Manager"===this.name)this.dataService.getUserFullName(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{this.authService.setGuestContext({id:t,browsingAs:n.fullName,instanceId:"",impersonate:!0}),this.instanceService.openInstance(this.feature.featureSlug)});else if("Media Manager"===this.name){if(!t||0===t.length)return;this.dataService.getAssetDetailsById(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{let i=w.o.Upload;switch(n.mediaUploadType){case"Url":i=w.o.Url;break;case"Embed":i=w.o.Embed;break;default:i=w.o.Upload}this.instanceService.openInstance(this.feature.featureSlug,t,"default","builder",{uploadType:+i})})}else this.instanceService.openInstance("instance",t,"default","builder")}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}addCampaign(){const t={title:this.feature.title+Math.floor(1e3+9e3*Math.random()),featureId:this.feature.id};this.dataService.addCampaign(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{n&&this.openInstanceBuilder(n)})}addProduct(){const t={name:this.feature.title+Math.floor(1e3+9e3*Math.random()),description:this.feature.description};this.dataService.addProduct(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{this.openInstanceBuilder(n.id)})}addOrganization(){const t={name:this.feature.title+Math.floor(1e3+9e3*Math.random())};this.dataService.addOrganization(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{n&&this.openInstanceBuilder(n.id)})}addInstance(t){var n=this;this.dataService.getMyOrganizations(this.feature.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(function(){var i=(0,_.A)(function*(a){a.length<1?yield n.presentAlert():a.length>1||!0===n.authService.userContext?.canManage?n.addInstanceToOrganization(t):n.saveInstance(a[0].id)});return function(a){return i.apply(this,arguments)}}())}addMedia(t){var n=this;return(0,_.A)(function*(){yield(yield n.popOver.create({component:ee.Z,cssClass:"add-media-popover",componentProps:{onClose:()=>{}},event:t,side:"bottom"})).present()})()}addNetwork(){var t=this;return(0,_.A)(function*(){const n={name:t.feature.title+Math.floor(1e3+9e3*Math.random())};t.dataService.addNetwork(n).pipe((0,u.Q)(t.componentDestroyed$)).subscribe(i=>{i&&t.openInstanceBuilder(i.id)},i=>{console.error(i)})})()}addCommunication(){var t=this;return(0,_.A)(function*(){const n={name:t.feature.title+Math.floor(1e3+9e3*Math.random())};t.dataService.addCommunincations(n).pipe((0,u.Q)(t.componentDestroyed$)).subscribe(i=>{i&&t.openInstanceBuilder(i.id)})})()}addInstanceToOrganization(t){var n=this;return(0,_.A)(function*(){const i=yield n.popOver.create({component:St.e,cssClass:"add-search-modal",componentProps:{linkTypeName:"Organizations",criteriaType:null,options:null},event:t,side:"bottom"});i.onDidDismiss().then(a=>{a.data&&n.saveInstance(a.data.id)}),yield i.present()})()}saveInstance(t){const n=this.populateNewInstanceIn(t);this.dataService.createInstance(n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{this.openInstanceBuilder(i.id)})}presentAlert(){var t=this;return(0,_.A)(function*(){const n=yield t.alertController.create({cssClass:"",header:"Please Note:",message:"User not linked to an organization.",buttons:["OK"]});yield n.present(),yield n.onDidDismiss()})()}populateNewInstanceIn(t){return{title:this.feature.title,description:this.feature.description,featureId:this.feature.id,organizationId:t,isDefault:!1,status:"Modifiable Learning Container Pages"===this.name?"public":"private"}}addFeature(t){var n=this;return(0,_.A)(function*(){n.dataService.getFeatureTypes().pipe((0,u.Q)(n.componentDestroyed$),(0,f.T)(i=>i.map(a=>({key:a.id,value:a.name})))).subscribe(function(){var i=(0,_.A)(function*(a){const l=yield n.popOver.create({component:M.u,cssClass:"question-type-popover",componentProps:{header:"Please select a feature type:",options:a},event:t,side:"bottom"});l.onDidDismiss().then(function(){var p=(0,_.A)(function*(m){m.data&&n.dataService.createFeature(m.data.key).pipe((0,u.Q)(n.componentDestroyed$)).subscribe(ge=>{n.instanceService.openInstance("repository/builder",ge)})});return function(m){return p.apply(this,arguments)}}()),yield l.present()});return function(a){return i.apply(this,arguments)}}())})()}sortChanged(t){this.sortBy=t?.active,this.sortDir=t?.direction,this.refreshData()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u),e.rXU(s.IE),e.rXU(s.hG),e.rXU(Zt.u),e.rXU(G.b),e.rXU(s.W3))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-dashboard"]],viewQuery:function(n,i){if(1&n&&e.GBs($,5),2&n){let a;e.mGM(a=e.lsd())&&(i.tableComponent=a.first)}},inputs:{feature:"feature"},decls:13,vars:10,consts:[[1,"feature-repository-dashboard-container"],[1,"top-container"],["size","3",1,"start-col-search"],[3,"formGroup"],["color","dark","formControlName","featureRepoSearchValue","type","search","placeholder","Search","showCancelButton","focus","debounce","800",3,"ionInput"],["size","2",1,"start-col-search","select-option"],[3,"valueChanged","toolTip","placeHolder","label","backgroundColor","textValue","options"],["size","7",1,"end-col-buttons"],[1,"buttons"],[3,"feature","columnsToDisplayIn","dataSourceIn","loading"],[1,"scroll-cap"],[3,"click"],["svgIcon","add"],[3,"routeOnClick","loadMoreData","refreshData","sortChange","feature","columnsToDisplayIn","dataSourceIn","loading"]],template:function(n,i){1&n&&(e.j41(0,"div",0)(1,"ion-row",1)(2,"ion-col",2)(3,"form",3)(4,"ion-searchbar",4),e.bIt("ionInput",function(l){return i.searchRepoValue(l)}),e.k0s()()(),e.j41(5,"ion-col",5)(6,"app-select-option-control",6),e.bIt("valueChanged",function(l){return i.filterValues(l)}),e.k0s()(),e.j41(7,"ion-col",7)(8,"div",8),e.DNE(9,Kt,2,0,"ion-button")(10,qt,3,0,"ion-button"),e.k0s()()(),e.DNE(11,en,1,4,"app-repository-dashboard-table",9),e.nrm(12,"div",10),e.k0s()),2&n&&(e.R7$(3),e.Y8G("formGroup",i.searchForm),e.R7$(3),e.Y8G("toolTip","Filter by Status")("placeHolder","Filter by Status")("label","")("backgroundColor",i.controlBackground)("textValue",i.statusTypeId)("options",i.orgStatusTypes),e.R7$(3),e.vxM("Achievement Completion"===i.name?9:-1),e.R7$(),e.vxM(i.showAdd?10:-1),e.R7$(),e.vxM(i.dataSource?11:-1))},dependencies:[v.c,s.Jm,s.hU,s.ln,s.S1,s.Gw,c.qT,c.BC,c.cb,c.j4,c.JD,k.An,$],styles:[".top-container[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;flex-direction:row}.top-container[_ngcontent-%COMP%]   ion-col[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:16px;height:60px}.top-container[_ngcontent-%COMP%]   .start-col-search[_ngcontent-%COMP%]{justify-content:flex-start}.top-container[_ngcontent-%COMP%]   .start-col-search[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]{width:100%}.top-container[_ngcontent-%COMP%]   .select-option[_ngcontent-%COMP%]{margin-top:-5px}.top-container[_ngcontent-%COMP%]   .end-col-buttons[_ngcontent-%COMP%]{justify-content:flex-end}.top-container[_ngcontent-%COMP%]   .end-col-buttons[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{display:flex;gap:10px}.scroll-cap[_ngcontent-%COMP%]{margin-top:300px}.feature-repository-dashboard-container[_ngcontent-%COMP%]{position:relative;height:calc(100vh - 230px);overflow:hidden}@media (max-width: 959px){.feature-repository-dashboard-container[_ngcontent-%COMP%]{height:calc(100vh - 280px)!important}}"]})}}return o})();d(45024),d(96695),d(57786),d(99172),d(25558),d(82765);var On=d(71985),Pn=d(7838),y=d(96850),R=d(14310),Dn=d(60618),wn=d(7673);function In(o,r){if(1&o&&(e.j41(0,"ion-select-option",10),e.EFF(1),e.k0s()),2&o){const t=r.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.name," ")}}function Rn(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-select",9),e.mxI("ngModelChange",function(i){e.eBV(t);const a=e.XpG();return e.DH7(a.featureTabButton.buttonLinkType,i)||(a.featureTabButton.buttonLinkType=i),e.Njj(i)}),e.Z7z(1,In,2,2,"ion-select-option",10,e.fX1),e.k0s()}if(2&o){const t=e.XpG();e.muq("--background-color:",t.backgroundColor,";"),e.R50("ngModel",t.featureTabButton.buttonLinkType),e.Y8G("compareWith",t.linkCompareWith),e.R7$(),e.Dyx(r)}}function Sn(o,r){if(1&o&&(e.j41(0,"ion-select-option",10),e.EFF(1),e.k0s()),2&o){const t=r.$implicit;e.Y8G("value",t.id),e.R7$(),e.SpI(" ",t.code," ")}}function jn(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-select",11),e.mxI("ngModelChange",function(i){e.eBV(t);const a=e.XpG();return e.DH7(a.featureTabButton.referenceId,i)||(a.featureTabButton.referenceId=i),e.Njj(i)}),e.Z7z(1,Sn,2,2,"ion-select-option",10,e.fX1),e.k0s()}if(2&o){const t=e.XpG();e.muq("--background-color:",t.backgroundColor,";"),e.R50("ngModel",t.featureTabButton.referenceId),e.R7$(),e.Dyx(r)}}function $n(o,r){if(1&o&&(e.j41(0,"ion-select-option",10),e.EFF(1),e.k0s()),2&o){const t=r.$implicit;e.Y8G("value",t),e.R7$(),e.SpI(" ",t.name," ")}}function Bn(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-select",12),e.bIt("ionChange",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.setButtonActions(i))}),e.Z7z(1,$n,2,2,"ion-select-option",10,e.fX1),e.k0s()}if(2&o){const t=e.XpG();e.muq("--background-color:",t.backgroundColor,";"),e.Y8G("compareWith",t.compareWith)("value",t.selectedTabButtonActions),e.R7$(),e.Dyx(r)}}let ae=(()=>{class o{constructor(){this.deleteClicked=new e.bkB,this.backgroundColor="#181818"}ngOnInit(){this.filterFeatures(),this.featureTabButton.featureTabButtonActions&&(this.selectedTabButtonActions=this.featureTabButton.featureTabButtonActions.map(t=>({id:t.actionId,name:"",actionBw:0})))}filterFeatures(){this.features$.pipe((0,f.T)(t=>t.filter(n=>null!=n.code))).subscribe(t=>{this.features$=(0,wn.of)(t)})}delete(){this.deleteClicked.emit()}compareWith(t,n){return t&&n?Array.isArray(n)?n.some(i=>i.id===t.id):t.id===n.id:t===n}linkCompareWith(t,n){return t.id===n.id}setButtonActions(t){this.selectedTabButtonActions=t.detail.value,this.featureTabButton.featureTabButtonActions=t.detail.value.map(n=>({id:"",actionId:n.id}))}static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-tab-button-action"]],inputs:{featureTabButton:"featureTabButton",buttonLinkTypes$:"buttonLinkTypes$",actions$:"actions$",features$:"features$"},outputs:{deleteClicked:"deleteClicked"},decls:24,vars:10,consts:[["size","2.75",1,"item"],["position","stacked"],[3,"ngModelChange","ngModel"],["interface","popover",3,"style","ngModel","compareWith"],["interface","popover",3,"style","ngModel"],["interface","popover","multiple","true",3,"style","compareWith","value"],["size","1",1,"center-col"],["fill","clear","color","light",3,"click"],["name","remove-circle-outline"],["interface","popover",3,"ngModelChange","ngModel","compareWith"],[3,"value"],["interface","popover",3,"ngModelChange","ngModel"],["interface","popover","multiple","true",3,"ionChange","compareWith","value"]],template:function(n,i){if(1&n&&(e.j41(0,"ion-row")(1,"ion-col",0)(2,"ion-label",1),e.EFF(3,"Text"),e.k0s(),e.j41(4,"ion-input",2),e.mxI("ngModelChange",function(l){return e.DH7(i.featureTabButton.buttonText,l)||(i.featureTabButton.buttonText=l),l}),e.k0s()(),e.j41(5,"ion-col",0)(6,"ion-label",1),e.EFF(7,"Action"),e.k0s(),e.DNE(8,Rn,3,5,"ion-select",3),e.nI1(9,"async"),e.k0s(),e.j41(10,"ion-col",0)(11,"ion-label",1),e.EFF(12,"Feature"),e.k0s(),e.qex(13),e.DNE(14,jn,3,4,"ion-select",4),e.nI1(15,"async"),e.bVm(),e.k0s(),e.j41(16,"ion-col",0)(17,"ion-label",1),e.EFF(18,"Action"),e.k0s(),e.DNE(19,Bn,3,5,"ion-select",5),e.nI1(20,"async"),e.k0s(),e.j41(21,"ion-col",6)(22,"ion-button",7),e.bIt("click",function(){return i.delete()}),e.nrm(23,"ion-icon",8),e.k0s()()()),2&n){let a,l,p;e.R7$(4),e.R50("ngModel",i.featureTabButton.buttonText),e.R7$(4),e.vxM((a=e.bMT(9,4,i.buttonLinkTypes$))?8:-1,a),e.R7$(6),e.vxM((l=e.bMT(15,6,i.features$))?14:-1,l),e.R7$(5),e.vxM((p=e.bMT(20,8,i.actions$))?19:-1,p)}},dependencies:[s.Jm,s.hU,s.iq,s.$w,s.he,s.ln,s.Nm,s.Ip,s.Je,s.Gw,c.BC,c.vS,b.Jj],styles:["[_nghost-%COMP%]   ion-select[_ngcontent-%COMP%]{border:1px solid #4e4e4e;border-radius:5px;color:#fff;background-color:#232323;font-size:14px}[_nghost-%COMP%]   ion-input[_ngcontent-%COMP%]{border:1px solid #4e4e4e;border-radius:5px;color:#fff;background-color:#232323;font-size:14px}[_nghost-%COMP%]   ion-row[_ngcontent-%COMP%]{background-color:#181818!important;border-radius:5px;margin-top:4px;margin-bottom:4px}[_nghost-%COMP%]   ion-col[_ngcontent-%COMP%]{padding:4px}[_nghost-%COMP%]   ion-item[_ngcontent-%COMP%]{margin:0;padding:0}[_nghost-%COMP%]   .label-stacked.sc-ion-label-md-h[_ngcontent-%COMP%]{transform:translateY(30%) scale(.75)!important;font-style:italic;color:#fff}[_nghost-%COMP%]   .center-col[_ngcontent-%COMP%]{align-items:center;align-content:center;justify-content:center;display:flex}  .select-interface-option::part(native){background-color:#181818!important;color:#fff}"]})}}return o})();function Gn(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-tab-button-action",3),e.bIt("deleteClicked",function(){const i=e.eBV(t).$implicit,a=e.XpG();return e.Njj(a.removeButton(i))}),e.k0s()}if(2&o){const t=r.$implicit,n=e.XpG();e.Y8G("featureTabButton",t)("buttonLinkTypes$",n.buttonLinkTypes$)("actions$",n.actions$)("features$",n.features$)}}let re=(()=>{class o{constructor(t){this.dataService=t}ngOnInit(){this.initData()}initData(){this.buttonLinkTypes$=this.dataService.getFeatureTabButtonLinkTypes(),this.actions$=this.dataService.getActions(),this.features$=this.dataService.getFeatures()}addButton(){const t={};this.featureTab.featureTabButtons?this.featureTab.featureTabButtons.push(t):this.featureTab.featureTabButtons=[t]}removeButton(t){const n=this.featureTab.featureTabButtons.indexOf(t);-1!==n&&this.featureTab.featureTabButtons.splice(n,1)}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-tab-button-actions"]],inputs:{featureTab:"featureTab"},decls:6,vars:0,consts:[[3,"featureTabButton","buttonLinkTypes$","actions$","features$"],[3,"click"],["svgIcon","add"],[3,"deleteClicked","featureTabButton","buttonLinkTypes$","actions$","features$"]],template:function(n,i){1&n&&(e.j41(0,"ion-grid"),e.Z7z(1,Gn,1,4,"app-tab-button-action",0,e.fX1),e.j41(3,"ion-button",1),e.bIt("click",function(){return i.addButton()}),e.EFF(4," Add Bullet"),e.nrm(5,"mat-icon",2),e.k0s()()),2&n&&(e.R7$(),e.Dyx(i.featureTab.featureTabButtons))},dependencies:[s.Jm,s.lO,k.An,ae]})}}return o})();const se=o=>({disabled:o});function En(o,r){if(1&o&&e.nrm(0,"app-select-option-control",11),2&o){const t=e.XpG(2);e.Y8G("toolTip","Select Function")("placeHolder","Select Function")("label","Function")("formControlName","function")("backgroundColor",t.controlBackground)("options",r)}}function Xn(o,r){if(1&o&&e.nrm(0,"app-select-option-control",13),2&o){const t=e.XpG(2);e.Y8G("toolTip","Select Tab Access")("placeHolder","Select Tab Access")("label","Tab Access")("formControlName","access")("multiple",!0)("backgroundColor",t.controlBackground)("options",r)}}function Nn(o,r){if(1&o&&e.nrm(0,"app-select-option-control",14),2&o){const t=e.XpG(2);e.Y8G("toolTip","Select Persona Tab Access")("placeHolder","Select Persona Tab Access")("label","Persona Access")("formControlName","personaAccess")("backgroundColor",t.controlBackground)("multiple",!0)("options",r)}}function Vn(o,r){if(1&o&&e.nrm(0,"app-select-option-control",13),2&o){const t=e.XpG(2);e.Y8G("toolTip","Select Tab Access")("placeHolder","Select Tab Access")("label","Tab Edit Access")("formControlName","editAccess")("multiple",!0)("backgroundColor",t.controlBackground)("options",r)}}function An(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",23)(1,"div",24)(2,"div",25)(3,"ion-checkbox",26),e.bIt("ionChange",function(i){const a=e.eBV(t).$implicit,l=e.XpG(3);return e.Njj(l.checkboxChanged(i,a))}),e.k0s(),e.j41(4,"div",27)(5,"div",28),e.EFF(6),e.k0s()()()()()}if(2&o){let t;const n=r.$implicit,i=e.XpG(3);e.R7$(3),e.Y8G("checked",i.isSelected(n.id)),e.R7$(3),e.JRh(i.getRowTypeLabel(null!==(t=null==n?null:n.name)&&void 0!==t?t:""))}}function Un(o,r){1&o&&(e.j41(0,"div",21),e.Z7z(1,An,7,2,"div",23,e.fX1),e.k0s()),2&o&&(e.R7$(),e.Dyx(r))}function Ln(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",0)(1,"h1",1),e.EFF(2,"Tab Settings"),e.k0s(),e.j41(3,"ion-label",2),e.EFF(4,"Indicate the function of the tab and who is able to see it."),e.k0s(),e.j41(5,"ion-button",3),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.close())}),e.nrm(6,"ion-icon",4),e.k0s(),e.j41(7,"ion-grid")(8,"ion-row",5)(9,"div",6)(10,"ion-label",7),e.EFF(11,"Show only for Edge Factor Admins"),e.k0s(),e.nrm(12,"ion-checkbox",8),e.k0s()(),e.j41(13,"ion-row",5)(14,"div",6)(15,"ion-label",7),e.EFF(16,"Allow Guest access"),e.k0s(),e.nrm(17,"ion-checkbox",8),e.k0s()(),e.j41(18,"ion-row",9)(19,"ion-col",10),e.DNE(20,En,1,6,"app-select-option-control",11),e.nI1(21,"async"),e.k0s(),e.j41(22,"ion-col",12),e.DNE(23,Xn,1,7,"app-select-option-control",13),e.nI1(24,"async"),e.k0s(),e.j41(25,"ion-col",10),e.DNE(26,Nn,1,7,"app-select-option-control",14),e.nI1(27,"async"),e.k0s(),e.j41(28,"ion-col",12),e.DNE(29,Vn,1,7,"app-select-option-control",13),e.nI1(30,"async"),e.k0s()(),e.j41(31,"ion-row",5)(32,"ion-col",15)(33,"ion-label",2),e.EFF(34,"Set up the action button on the tab."),e.k0s()()(),e.j41(35,"ion-row",9)(36,"ion-col",16),e.nrm(37,"app-text-input-control",17),e.k0s()(),e.j41(38,"ion-row",18)(39,"ion-col",15)(40,"ion-label",2),e.EFF(41,"These are the actions triggered by the page buttons."),e.k0s(),e.nrm(42,"app-tab-button-actions",19),e.k0s()(),e.j41(43,"ion-row")(44,"ion-col",20)(45,"ion-label",2),e.EFF(46,"Choose "),e.j41(47,"span",7),e.EFF(48,"how"),e.k0s(),e.EFF(49," content can be added to a row. Check all that apply."),e.k0s(),e.DNE(50,Un,3,0,"div",21),e.nI1(51,"async"),e.j41(52,"ion-label",22)(53,"span"),e.EFF(54,"NOTE: Users with 'Manage' access are not limited by these controls. They can use any of the methods listed above."),e.k0s()()()()()()}if(2&o){let t,n,i,a,l;const p=e.XpG();e.Y8G("formGroup",p.settingsForm),e.R7$(12),e.Y8G("formControlName","showForEfAdmin"),e.R7$(5),e.Y8G("formControlName","showForGuest"),e.R7$(3),e.vxM((t=e.bMT(21,16,p.featureTabTypes$))?20:-1,t),e.R7$(2),e.Y8G("ngClass",e.eq3(26,se,null==p.featureTab?null:p.featureTab.showForEfAdmin)),e.R7$(),e.vxM((n=e.bMT(24,18,p.actions$))?23:-1,n),e.R7$(3),e.vxM((i=e.bMT(27,20,p.personaSelectOptions$))?26:-1,i),e.R7$(2),e.Y8G("ngClass",e.eq3(28,se,null==p.featureTab?null:p.featureTab.showForEfAdmin)),e.R7$(),e.vxM((a=e.bMT(30,22,p.actions$))?29:-1,a),e.R7$(8),e.Y8G("toolTip","Enter Button Text")("placeHolder","Enter Button Text")("label","Button Text")("formControlName","buttonText")("itemBackgroundColor",p.controlBackground),e.R7$(5),e.Y8G("featureTab",p.featureTab),e.R7$(8),e.vxM((l=e.bMT(51,24,p.rowTypes$))?50:-1,l)}}let le=(()=>{class o{constructor(t,n,i){this.formBuilder=t,this.dataService=n,this.modalController=i,this.controlBackground="#181818"}ngOnInit(){this.createForm(),this.initData(),this.selectedRowTypeIds=this.featureTab.featureTabRowTypes?this.featureTab.featureTabRowTypes.map(t=>t.rowType.id):null}createForm(){this.settingsForm=this.formBuilder.group({function:[this.featureTab?.type?.id,c.k0.required],access:[this.featureTab?.featureTabActions.map(t=>t.id),c.k0.required],personaAccess:[this.featureTab.featureTabPersonas?.map(t=>t.id)],editAccess:[this.featureTab?.featureTabEditActions?.map(t=>t.id)],showForEfAdmin:[this.featureTab?.showForEfAdmin??!1,c.k0.required],showForGuest:[this.featureTab?.showForGuest??!1,c.k0.required],buttonText:[this.featureTab?.buttonText]}),this.subscribeToFormChanges()}subscribeToFormChanges(){this.settingsForm.valueChanges.subscribe(()=>{this.featureTab.typeId=this.settingsForm.controls.function.value,this.featureTab.featureTabActions=this.settingsForm.controls.access.value?.map(t=>({id:t,name:""})),this.featureTab.featureTabPersonas=this.settingsForm.controls.personaAccess.value?.map(t=>({id:t,name:""})),this.featureTab.showForEfAdmin=this.settingsForm.controls.showForEfAdmin.value,this.featureTab.showForGuest=this.settingsForm.controls.showForGuest.value,this.featureTab.featureTabEditActions=this.settingsForm.controls.editAccess.value?this.settingsForm.controls.editAccess.value?.map(t=>({id:t,name:""})):null,this.featureTab.buttonText=this.settingsForm.controls.buttonText.value})}initData(){this.personaSelectOptions$=this.dataService.getTagChildren("E72D1551-DFB6-4F1A-A783-28C8B930AD6C").pipe((0,f.T)(t=>t.map(n=>({id:n.id,value:n.name})))),this.actions$=this.dataService.getActions().pipe((0,f.T)(t=>t.map(n=>({id:n.id,value:n.name})))),this.featureTabTypes$=this.dataService.getFeatureTabTypes().pipe((0,f.T)(t=>t.map(n=>({id:n.id,value:n.name})))),this.rowTypes$=this.dataService.getRowTypes()}checkboxChanged(t,n){if(this.featureTab.featureTabRowTypes||(this.featureTab.featureTabRowTypes=[]),t?.srcElement?.checked)this.selectedRowTypeIds?.push(n.id),this.featureTab.featureTabRowTypes.push({id:"",rowType:n});else{const i=this.selectedRowTypeIds?.indexOf(n.id)??-1;this.selectedRowTypeIds?.splice(i,1);const a=this.featureTab.featureTabRowTypes.findIndex(l=>l.rowType.id===n.id);this.featureTab.featureTabRowTypes.splice(a,1)}}isSelected(t){return!!this.selectedRowTypeIds&&-1!==this.selectedRowTypeIds.findIndex(n=>n===t)}getRowTypeLabel(t){switch(t){case"Manual":return"Manually (If checked, users will be able to add content using the 'add' button)";case"Dynamic":return"Dynamically (If checked, users will see the tagging tab)";default:return`${t} Items`}}close(){this.modalController.dismiss(this.featureTab)}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(c.ze),e.rXU(C.u),e.rXU(s.W3))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-tab-settings"]],inputs:{featureTab:"featureTab"},decls:2,vars:1,consts:[[1,"container",3,"formGroup"],[1,"italic"],[1,"gray-text"],["fill","clear","color","light",1,"close",3,"click"],["name","close"],[1,"text-row"],[1,"text-and-checkbox"],[1,"white-text"],[3,"formControlName"],[1,"input-row"],["size","6"],[3,"toolTip","placeHolder","label","formControlName","backgroundColor","options"],["size","6",3,"ngClass"],[3,"toolTip","placeHolder","label","formControlName","multiple","backgroundColor","options"],[3,"toolTip","placeHolder","label","formControlName","backgroundColor","multiple","options"],["size","12"],["size","4"],[3,"toolTip","placeHolder","label","formControlName","itemBackgroundColor"],[1,"text-row","input-row"],[3,"featureTab"],["size","12",1,"text-row"],[1,"radio-group-container"],[1,"gray-text","italic"],[1,"properties-container"],[1,"property"],[1,"property-left"],[3,"ionChange","checked"],[2,"display","flex","flex-direction","column"],[1,"heading"]],template:function(n,i){1&n&&(e.j41(0,"ion-content"),e.DNE(1,Ln,55,30,"div",0),e.k0s()),2&n&&(e.R7$(),e.vxM(i.settingsForm?1:-1))},dependencies:[v.c,F.U,b.YU,s.Jm,s.eY,s.hU,s.W9,s.lO,s.iq,s.he,s.ln,s.hB,c.BC,c.cb,c.j4,c.JD,re,b.Jj],styles:[".container[_ngcontent-%COMP%]{position:relative;padding:16px;background-color:#333}.container[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#fff}.text-and-checkbox[_ngcontent-%COMP%]{display:flex;align-items:center}.text-and-checkbox[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin:5px 10px}.italic[_ngcontent-%COMP%]{font-style:italic}.gray-text[_ngcontent-%COMP%]{color:gray;font-size:14px}.white-text[_ngcontent-%COMP%]{color:#fff}.disabled[_ngcontent-%COMP%]{pointer-events:none}.input-row[_ngcontent-%COMP%]{padding-bottom:20px;border-bottom:1px solid gray}.text-row[_ngcontent-%COMP%]{margin-top:10px}.close[_ngcontent-%COMP%]{position:absolute;right:0;top:10px;padding:0}.close[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:25px;height:25px}.radio-group-container[_ngcontent-%COMP%]{max-height:200px;padding:10px;border-radius:5px;overflow-y:auto}.radio-group-container[_ngcontent-%COMP%]   .properties-container[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column}.radio-group-container[_ngcontent-%COMP%]   .properties-container[_ngcontent-%COMP%]   .property[_ngcontent-%COMP%]{height:40px;width:100%;border-radius:5px;background-color:#111;display:flex;flex-direction:row;align-items:center;align-content:center;justify-content:space-between;margin-bottom:5px;border:1px solid #111111;padding:0 10px;cursor:pointer}.radio-group-container[_ngcontent-%COMP%]   .properties-container[_ngcontent-%COMP%]   .property[_ngcontent-%COMP%]   .property-left[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;align-content:center}.radio-group-container[_ngcontent-%COMP%]   .properties-container[_ngcontent-%COMP%]   .property[_ngcontent-%COMP%]   .property-left[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-right:10px}.radio-group-container[_ngcontent-%COMP%]   .properties-container[_ngcontent-%COMP%]   .property[_ngcontent-%COMP%]   .property-left[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-weight:700;font-size:14px;color:#fff;white-space:nowrap;overflow:hidden;text-overflow:ellipsis!important}.radio-group-container[_ngcontent-%COMP%]   .properties-container[_ngcontent-%COMP%]   .property[_ngcontent-%COMP%]   .property-left[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{font-style:italic;font-size:small;color:#aaa}.radio-group-container[_ngcontent-%COMP%]   .properties-container[_ngcontent-%COMP%]   .property[_ngcontent-%COMP%]   .property-left[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{margin-left:5px;margin-right:5px}"]})}}return o})();var zn=d(12536),Yn=d(29783),E=d(89524);const Jn=(o,r)=>({components:o,index:r}),Qn=o=>({draggingOver:o}),Hn=o=>({"drag-component":o});function Wn(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-icon",11),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.reorderSection(!0,i.section))}),e.k0s()}}function Zn(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-icon",12),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.reorderSection(!1,i.section))}),e.k0s()}}function Kn(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-icon",29),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2).$implicit,a=e.XpG(3);return e.Njj(a.toggleComponentLocked(a.section.id,i,!1))}),e.k0s()}}function qn(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-icon",30),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2).$implicit,a=e.XpG(3);return e.Njj(a.toggleComponentLocked(a.section.id,i,!0))}),e.k0s()}}function eo(o,r){if(1&o){const t=e.RV6();e.nrm(0,"ion-icon",24),e.j41(1,"div",25)(2,"ion-icon",26),e.bIt("click",function(){e.eBV(t);const i=e.XpG().$implicit,a=e.XpG(3);return e.Njj(a.deleteComponentConfirmation(a.section,i))}),e.k0s(),e.DNE(3,Kn,1,0,"ion-icon",27)(4,qn,1,0,"ion-icon",28),e.k0s()}if(2&o){const t=e.XpG().$implicit,n=e.XpG(3);e.R7$(3),e.vxM(!0===(null==t?null:t.isLocked)&&2===n.section.typeBw?3:-1),e.R7$(),e.vxM(!0!==(null==t?null:t.isLocked)&&2===n.section.typeBw?4:-1)}}function to(o,r){1&o&&e.nrm(0,"ion-icon",31)}function no(o,r){if(1&o&&e.DNE(0,to,1,0,"ion-icon",31),2&o){const t=e.XpG().$implicit,n=e.XpG(3);e.vxM(!0===(null==t?null:t.isLocked)&&2===n.section.typeBw&&n.activeComponentId!==t.id?0:-1)}}function oo(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-col",21)(1,"div",22)(2,"div"),e.DNE(3,eo,5,2)(4,no,1,1),e.j41(5,"app-form-control-selector",23),e.bIt("click",function(){const i=e.eBV(t).$implicit,a=e.XpG(3);return e.Njj(a.componentSelected(i))}),e.k0s()()()()}if(2&o){const t=r.$implicit,n=e.XpG(3);e.R7$(),e.Y8G("ngClass",e.eq3(10,Hn,n.activeComponentId===t.id)),e.R7$(2),e.vxM(n.activeComponentId===t.id?3:-1),e.R7$(),e.vxM(n.activeComponentId!==t.id?4:-1),e.R7$(),e.Y8G("viewType",3)("component",t)("instance",n.instance)("featureId",n.featureId)("formGroup",n.templateForm)("formGroupName",n.section.id)("showPlaceHolder",n.section.typeBw===n.sectionTypeEnums.Dynamic||"Listing Details"===(null==t||null==t.componentType?null:t.componentType.name))}}function io(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-row",20),e.bIt("mouseenter",function(i){e.eBV(t);const a=e.XpG(2);return e.Njj(a.dragEntered(i))})("mouseleave",function(i){e.eBV(t);const a=e.XpG(2);return e.Njj(a.dragEntered(i))}),e.Z7z(1,oo,6,12,"ion-col",21,e.fX1),e.nI1(3,"orderBy"),e.k0s()}if(2&o){const t=r.$implicit;e.R7$(),e.Dyx(e.i5U(3,0,t,"templateField.colNumber"))}}function ao(o,r){if(1&o){const t=e.RV6();e.j41(0,"form",10),e.qex(1,13),e.j41(2,"div",14),e.nrm(3,"app-text-input-control",15),e.k0s(),e.nrm(4,"app-text-input-control",16)(5,"app-text-area-input-control",17),e.bVm(),e.j41(6,"div",18),e.bIt("cdkDropListDropped",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.reorderDropped(a.section.id,i))}),e.Z7z(7,io,4,3,"ion-row",19,e.fX1),e.nI1(9,"groupBy"),e.nI1(10,"values"),e.k0s()()}if(2&o){const t=e.XpG();e.Y8G("formGroup",t.templateForm),e.R7$(),e.Y8G("formGroupName",t.section.id),e.R7$(3),e.Y8G("label","Section title"),e.R7$(),e.Y8G("label","Section description"),e.R7$(),e.Y8G("cdkDropListData",e.l_i(11,Jn,t.section.components,t.index))("ngClass",e.eq3(14,Qn,t.draggingOver)),e.R7$(),e.Dyx(e.bMT(10,9,e.i5U(9,6,t.section.components,"builderRowNumber")))}}let ce=(()=>{class o{constructor(t,n,i){this.dataService=t,this.builderService=n,this.alertService=i,this.templateForm=new c.J3({}),this.sectionRemoved=new e.bkB,this.sectionChanged=new e.bkB,this.componentTypeEnum=zn.I,this.componentDestroyed$=new g.B,this.draggingOver=!1,this.sectionTypeEnums=Yn.F}ngOnInit(){this.builderService.selectedComponent$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.activeComponentId=t?.id??""})}componentSelected(t){this.builderService.selectedComponent$.next(t)}componentHover(t){this.activeComponentId=t}removeSection(t){this.sectionRemoved.emit(t)}isRowComponent(t){return t.componentType.id="4983442B-F094-4B9D-8AD1-645A6485EE11"}reorderDropped(t,n){(0,T.HD)(n.container.data.components,n.previousIndex,n.currentIndex);const i=[];n.container.data.components.forEach((a,l)=>{i.push({id:a.id,sectionId:t,builderSortOrder:l})}),this.dataService.orderComponents(i).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.section.components?.forEach(a=>{a.builderSortOrder=i.find(l=>l.id===a.id)?.builderSortOrder})})}addFieldDropped(t){const n=this.template.sections.indexOf(t.container.data);this.dataService.createComponent({sectionId:t.container.data.id,componentTypeId:t.item.data.id,componentTypeBw:t.item.data.typeBw,templateField:null,builderRowNumber:null,isLocked:null,parentComponentId:null}).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(a=>{this.templateForm.get(this.section.id).addControl(a.id,new c.hs),this.template.sections[n].components?.push(a),this.sectionChanged.emit(null),this.componentSelected(a),this.section.components=this.section.components?.slice()})}deleteComponent(t,n){this.dataService.deleteComponent(n.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{if(i){const a=this.template.sections.indexOf(t),l=this.template.sections[a].components?.indexOf(n);this.template.sections[a].components?.splice(l,1),this.section.components=this.section.components?.slice()}}),this.builderService.selectedComponent$.next(null)}deleteInstanceTags(t){this.instance.id&&this.activeComponentId&&this.dataService.deleteInstanceTag(t,this.instance.id,this.activeComponentId).pipe((0,u.Q)(this.componentDestroyed$)).subscribe()}deleteComponentConfirmation(t,n){this.alertService.presentAlert("Confirm Delete","Are you sure you want to delete this Component?").then(()=>{null==n.rowId&&n.templateField?.isTag&&this.deleteInstanceTags(n.templateField.tagTreeId),this.deleteComponent(t,n)})}reorderSection(t,n){const i=this.template.sections.indexOf(n),a=t?i-1:i+1,l=this.template.sections.length;if(a>=0&&a<=l-1){this.template.sections.splice(i,1),this.template.sections.splice(a,0,n);for(let p=0;p<l;p++){const m=this.template.sections[p];m.sortOrder=p,this.templateForm.get(m.id).controls.sortOrder.patchValue(p),this.updateSectionOrder(m.id,p)}}}updateSectionOrder(t,n){this.dataService.updateSectionOrder(t,n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.builderService.sectionUpdated$.next({...this.section,sortOrder:n})})}toggleComponentLocked(t,n,i){this.dataService.toggleComponentLocked(n.id,i).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{const a=this.template.sections.findIndex(p=>p.id===t),l=this.template.sections[a].components?.findIndex(p=>p.id===n.id);this.template.sections[a].components[l].isLocked=i})}dragEntered(t){this.draggingOver=t.buttons>0}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}selectSection(t){this.builderService.selectedSection$.next(t),this.activeComponentId=null}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u),e.rXU(R.V),e.rXU(I.u))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-builder-section"]],inputs:{template:"template",section:"section",index:"index",instance:"instance",sectionTypes:"sectionTypes",templateForm:"templateForm",featureId:"featureId"},outputs:{sectionRemoved:"sectionRemoved",sectionChanged:"sectionChanged"},decls:12,vars:9,consts:[["cdkDropList","",1,"section",3,"cdkDropListDropped","cdkDropListData"],[1,"controls"],[1,"sectionType"],[1,"remove-section",3,"click"],["color","primary","name","trash-outline",3,"title"],[1,"edit-section",3,"click"],["color","primary","name","pencil-outline",3,"title"],[1,"reorder-section"],["name","chevron-up-outline"],["name","chevron-down-outline"],[3,"formGroup"],["name","chevron-up-outline",3,"click"],["name","chevron-down-outline",3,"click"],[3,"formGroupName"],[2,"display","none"],["formControlName","sortOrder"],["formControlName","title",3,"label"],["formControlName","description",3,"label"],["cdkDropList","",3,"cdkDropListDropped","cdkDropListData","ngClass"],["cdkDrag",""],["cdkDrag","",3,"mouseenter","mouseleave"],["col-12","","col-md-6","","col-lg-4","","col-xl-3",""],[3,"ngClass"],[3,"click","viewType","component","instance","featureId","formGroup","formGroupName","showPlaceHolder"],["name","apps-sharp","cdkDragHandle","",1,"cdk-drag-handle"],[1,"option-buttons"],["color","light","stytle","font-size:larger","name","trash-outline",1,"left-icon",3,"click"],["color","light","stytle","font-size:larger","name","lock-closed-outline"],["color","light","stytle","font-size:larger","name","lock-open-outline"],["color","light","stytle","font-size:larger","name","lock-closed-outline",3,"click"],["color","light","stytle","font-size:larger","name","lock-open-outline",3,"click"],["color","light","stytle","font-size:larger","name","lock-closed-outline",1,"lock-icon"]],template:function(n,i){1&n&&(e.j41(0,"div",0),e.bIt("cdkDropListDropped",function(l){return i.addFieldDropped(l)}),e.j41(1,"div",1)(2,"span",2),e.EFF(3),e.k0s(),e.j41(4,"div",3),e.bIt("click",function(){return i.removeSection(i.section.id)}),e.nrm(5,"ion-icon",4),e.k0s(),e.j41(6,"div",5),e.bIt("click",function(){return i.selectSection(i.section)}),e.nrm(7,"ion-icon",6),e.k0s(),e.j41(8,"div",7),e.DNE(9,Wn,1,0,"ion-icon",8)(10,Zn,1,0,"ion-icon",9),e.k0s()(),e.DNE(11,ao,11,16,"form",10),e.k0s()),2&n&&(e.Aen(i.section.backgroundColor?"background:"+i.section.backgroundColor:"background-color:none"),e.Y8G("cdkDropListData",i.section),e.R7$(3),e.SpI("( ",1===i.section.typeBw?"Static":"Dynamic"," )"),e.R7$(2),e.Y8G("title","Remove Section"),e.R7$(2),e.Y8G("title","Edit Section"),e.R7$(2),e.vxM(0!==i.section.sortOrder?9:-1),e.R7$(),e.vxM(i.section.sortOrder!==i.template.sections.length-1?10:-1),e.R7$(),e.vxM(i.templateForm?11:-1))},dependencies:[F.U,Q.$,b.YU,s.hU,s.iq,s.ln,c.qT,c.BC,c.cb,c.j4,c.JD,c.$R,te.c,T.O7,T.T1,T.Fb,E.lU,E.Qe,E.Op],styles:['[_nghost-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:stretch;align-content:stretch}[_nghost-%COMP%]   h1[_ngcontent-%COMP%], [_nghost-%COMP%]   h2[_ngcontent-%COMP%], [_nghost-%COMP%]   h3[_ngcontent-%COMP%], [_nghost-%COMP%]   h4[_ngcontent-%COMP%], [_nghost-%COMP%]   h5[_ngcontent-%COMP%], [_nghost-%COMP%]   h6[_ngcontent-%COMP%]{font-family:"Exo 2";margin:0;color:#fff}[_nghost-%COMP%]   p[_ngcontent-%COMP%]{font-family:Roboto;color:#aaa;margin:0}[_nghost-%COMP%]   .drag-component[_ngcontent-%COMP%]{border-radius:8px;border:1px #4c3820 solid;border-left:16px #d28227 solid;padding-bottom:8px;margin:16px 0;position:relative}[_nghost-%COMP%]   .cdk-drag-handle[_ngcontent-%COMP%]{font-size:16px;color:#fff;top:45%;left:-14px;z-index:2;background-color:transparent;cursor:move}[_nghost-%COMP%]   .option-buttons[_ngcontent-%COMP%]{padding:0 4px;position:absolute;top:-12px;right:4px;z-index:2;background-color:#d28227;border-radius:4px;cursor:pointer;font-size:large}[_nghost-%COMP%]   .option-buttons[_ngcontent-%COMP%]   .left-icon[_ngcontent-%COMP%]{margin-right:10px}[_nghost-%COMP%]   .section[_ngcontent-%COMP%]{flex:1;border-radius:5px;margin:0 16px 16px;background-color:#111;padding-bottom:16px}[_nghost-%COMP%]   .section[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]{padding-top:20px}[_nghost-%COMP%]   .section[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]   .toggle-col[_ngcontent-%COMP%]{display:flex;flex-direction:row;padding-left:18px;align-items:center}[_nghost-%COMP%]   .section[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]   .toggle-col[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]{vertical-align:middle;padding-right:10px}[_nghost-%COMP%]   .section[_ngcontent-%COMP%]   .toggle-row[_ngcontent-%COMP%]   .toggle-col[_ngcontent-%COMP%]   .toggle-label[_ngcontent-%COMP%]{font-size:15px;color:#fff}[_nghost-%COMP%]   .new-section[_ngcontent-%COMP%]{flex:1;border-radius:5px;border:1px #aaa dashed;margin:16px 32px;padding-top:8px;text-align:center;cursor:pointer}[_nghost-%COMP%]   .new-section[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin:16px 0;font-size:1.5em}[_nghost-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{display:none}[_nghost-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{background-color:#d28227;color:gray;border:1px solid #d28227;border-radius:8px}ion-label[_ngcontent-%COMP%]{cursor:help}.controls[_ngcontent-%COMP%]{position:relative;display:flex;right:0;justify-content:flex-end}.controls[_ngcontent-%COMP%]   .remove-section[_ngcontent-%COMP%]{margin:.5em;display:flex;justify-content:center;align-items:center}.controls[_ngcontent-%COMP%]   .remove-section[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#d28227;cursor:pointer}.controls[_ngcontent-%COMP%]   .edit-section[_ngcontent-%COMP%]{margin:.5em;display:flex;justify-content:center;align-items:center}.controls[_ngcontent-%COMP%]   .edit-section[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#d28227;cursor:pointer}.controls[_ngcontent-%COMP%]   .reorder-section[_ngcontent-%COMP%]{margin:.5em;display:flex;justify-content:center;align-items:center}.controls[_ngcontent-%COMP%]   .reorder-section[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:28px;color:#d28227;cursor:pointer}.controls[_ngcontent-%COMP%]   .reorder-section[_ngcontent-%COMP%]:hover{cursor:pointer}.controls[_ngcontent-%COMP%]   .sectionType[_ngcontent-%COMP%]{color:#565656;margin-top:auto;margin-bottom:auto}.lock-icon[_ngcontent-%COMP%]{right:10px;position:absolute}.draggingOver[_ngcontent-%COMP%]{border:1px solid #d28227!important;border-radius:8px}']})}}return o})();function ro(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-feature-repository-builder-section",4),e.bIt("sectionRemoved",function(){const i=e.eBV(t).$implicit,a=e.XpG(2);return e.Njj(a.deleteSectionConfirmation(i.id))})("sectionChanged",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.sectionUpdated())}),e.k0s()}if(2&o){const t=r.$implicit,n=r.$index,i=e.XpG(2);e.Y8G("template",i.template)("section",t)("index",n)("instance",i.instance)("templateForm",i.templateForm)("featureId",i.featureTab.featureId)("sectionTypes",i.sectionTypes)}}function so(o,r){if(1&o){const t=e.RV6();e.Z7z(0,ro,1,7,"app-feature-repository-builder-section",1,e.fX1),e.j41(2,"div",2),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.openSelectSectionType())}),e.j41(3,"p"),e.EFF(4,"Add section"),e.k0s(),e.nrm(5,"ion-icon",3),e.k0s()}if(2&o){const t=e.XpG();e.Dyx(t.template.sections)}}let pe=(()=>{class o{constructor(t,n,i,a){this.dataService=t,this.alertService=n,this.builderService=i,this.popOver=a,this.sectionChanged=new e.bkB,this.componentDestroyed$=new g.B}ngOnInit(){this.dataService.getTemplate(this.templateId).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.template=t,this.createTemplateFormGroup(this.template)}),this.isDefaultInstanceTab=this.featureTab.isDefaultInstanceTab,this.getSectionTypes(),this.builderService.sectionUpdated$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{const n=this.template.sections.findIndex(i=>i.id===t.id);-1!==n&&(this.template.sections[n]=t,this.createTemplateFormGroup(this.template))})}getSectionTypes(){this.dataService.getSectionTypes().pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{if(t){const n=Object.entries(t).map(([i,a])=>({id:i,value:a}));this.sectionTypes=n}})}openSelectSectionType(){var t=this;return(0,_.A)(function*(){if(t.sectionTypes){const n=yield t.popOver.create({component:M.u,cssClass:"question-type-popover",componentProps:{options:t.sectionTypes},event:void 0,side:"bottom"});n.onDidDismiss().then(i=>{i.data&&t.addSection(i.data.id)}),yield n.present()}})()}addSection(t){this.dataService.createSection(this.templateId,t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{if(null!=n){const i=new c.J3({title:new c.hs,description:new c.hs,hideBackground:new c.hs,sortOrder:new c.hs,typeId:new c.hs});i.get("title")?.disable(),i.get("description")?.disable(),i.get("hideBackground")?.disable(),i.get("typeId")?.disable(),this.templateForm.addControl(n.id,i),this.template.sections.push(n),this.builderService.selectedSection$.next(n)}})}removeSection(t){const n=this.template.sections.findIndex(i=>i.id===t);this.dataService.deleteSection(t).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{i&&(this.template.sections.splice(n,1),this.builderService.selectedSection$.next(null))})}addComponent(t){const n=this.template.sections.indexOf(t.container.data);this.dataService.createComponent({sectionId:t.container.data.id,componentTypeId:t.item.data.id,componentTypeBw:t.item.data.typebw,templateField:null,builderRowNumber:null,isLocked:null,parentComponentId:null}).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(a=>{null!=a&&this.template.sections[n].components?.push(a)})}deleteComponent(t,n){this.dataService.deleteComponent(n.id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{if(i){const a=this.template.sections.indexOf(t),l=this.template.sections[a].components?.indexOf(n);this.template.sections[a].components?.splice(l,1)}})}deleteSectionConfirmation(t){this.alertService.presentAlert("Confirm Delete","Are you sure you want to delete this section").then(()=>{this.removeSection(t)})}createTemplateFormGroup(t){const n={};t.sections.forEach(i=>{const a={};a.title=new c.hs(i?.title),a.title.disable(),a.description=new c.hs(i?.description),a.description.disable(),a.hideBackground=new c.hs(i?.hideBackground),a.hideBackground.disable(),a.sortOrder=new c.hs(i?.sortOrder),a.typeId=new c.hs(i?.typeId),a.typeId.disable(),i.components?.filter(l=>"Assessment Block"!==l.componentType.name).forEach(l=>{a[l.id]=new c.hs({value:"",disabled:!0})}),n[i.id]=new c.J3(a)}),this.templateForm=new c.J3(n),this.builderService.templateFormCreated$.next(this.templateForm)}sectionUpdated(){this.sectionChanged.emit(null)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u),e.rXU(I.u),e.rXU(R.V),e.rXU(s.IE))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-builder-template"]],inputs:{templateId:"templateId",instance:"instance",featureTab:"featureTab"},outputs:{sectionChanged:"sectionChanged"},decls:1,vars:1,consts:[[1,"new-section"],[3,"template","section","index","instance","templateForm","featureId","sectionTypes"],[1,"new-section",3,"click"],["color","primary","name","add-circle-outline"],[3,"sectionRemoved","sectionChanged","template","section","index","instance","templateForm","featureId","sectionTypes"]],template:function(n,i){1&n&&e.DNE(0,so,6,0,"div",0),2&n&&e.vxM(i.template?0:-1)},dependencies:[s.iq,ce],styles:['[_nghost-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:stretch;align-content:stretch}[_nghost-%COMP%]   h1[_ngcontent-%COMP%], [_nghost-%COMP%]   h2[_ngcontent-%COMP%], [_nghost-%COMP%]   h3[_ngcontent-%COMP%], [_nghost-%COMP%]   h4[_ngcontent-%COMP%], [_nghost-%COMP%]   h5[_ngcontent-%COMP%], [_nghost-%COMP%]   h6[_ngcontent-%COMP%]{font-family:"Exo 2";margin:0;color:#fff}[_nghost-%COMP%]   p[_ngcontent-%COMP%]{font-family:Roboto;color:#aaa;margin:0}[_nghost-%COMP%]   .new-section[_ngcontent-%COMP%]{flex:1;border-radius:5px;border:1px #aaa dashed;margin:16px 32px;padding-top:8px;text-align:center;cursor:pointer}[_nghost-%COMP%]   .new-section[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin:16px 0;font-size:1.5em}']})}}return o})();function lo(o,r){1&o&&e.nrm(0,"ion-icon",9)}function co(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",5)(1,"ion-label",6),e.EFF(2),e.k0s(),e.j41(3,"ion-input",7),e.bIt("change",function(i){e.eBV(t);const a=e.XpG().$index,l=e.XpG();return e.Njj(l.updateTab(i,a))}),e.k0s()(),e.j41(4,"ion-tab-button",8),e.bIt("click",function(){e.eBV(t);const i=e.XpG().$index,a=e.XpG();return e.Njj(a.removeTabConfirmation(i))}),e.DNE(5,lo,1,0,"ion-icon",9),e.k0s()}if(2&o){const t=e.XpG(),n=t.$implicit,i=t.$index,a=e.XpG();e.R7$(2),e.JRh(null!=n&&n.isDefaultInstanceTab?"Feature":"Instance"),e.R7$(),e.Y8G("value",null==n||null==n.tab?null:n.tab.name),e.R7$(),e.Y8G("disabled",a.tabIndex!==i),e.R7$(),e.vxM(a.tabIndex===i?5:-1)}}function po(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",10)(1,"ion-grid",11)(2,"ion-row")(3,"ion-col",12)(4,"ion-button",13),e.bIt("click",function(){e.eBV(t);const i=e.XpG().$implicit,a=e.XpG();return e.Njj(a.openSettings(i))}),e.EFF(5," SETTINGS "),e.k0s()()()(),e.j41(6,"ion-content",14),e.bIt("ionScroll",function(i){e.eBV(t);const a=e.XpG(2);return e.Njj(a.logScrolling(i))}),e.j41(7,"app-feature-repository-builder-template",15),e.bIt("sectionChanged",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.scrollToPrevPosition())}),e.k0s()()()}if(2&o){const t=e.XpG().$implicit,n=e.XpG();e.R7$(6),e.Y8G("scrollEvents",!0),e.R7$(),e.Y8G("featureTab",t)("templateId",t.tab.templateId)("instance",n.instance)}}function uo(o,r){1&o&&(e.j41(0,"mat-tab"),e.DNE(1,co,6,4,"ng-template",2)(2,po,8,4,"ng-template",4),e.k0s())}function mo(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",16)(1,"ion-button",13),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.isPopoverOpen=!i.isPopoverOpen)}),e.EFF(2," ADD "),e.nrm(3,"ion-icon",17),e.k0s()()}}function _o(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-list")(1,"ion-item",18),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.addTab(!0,!0))}),e.EFF(2,"FEATURE"),e.k0s(),e.j41(3,"ion-item",18),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.addTab(!0,!1))}),e.EFF(4,"INSTANCE"),e.k0s()()}}let de=(()=>{class o{constructor(t,n,i){this.dataService=t,this.alertService=n,this.modalController=i,this.showDefaultComponents=new e.bkB,this.scrollTop=100,this.componentDestroyed$=new g.B,this.isPopoverOpen=!1,this.tabIndex=0}ngOnChanges(t){var n=this;return(0,_.A)(function*(){t.featureTabs&&n.scrollToPrevPosition()})()}scrollToPrevPosition(){this.content&&this.content.getScrollElement().then(()=>{this.content.scrollByPoint(0,this.scrollTop,500)})}addTab(t,n){this.dataService.createFeatureTab({id:this.featureId,featureId:this.featureId,isDefaultInstanceTab:n,showTab:!0,showForEfAdmin:!1,showForGuest:!1,sortOrder:0,primaryFilter:null,secondaryFilter:null,typeId:null,buttonText:null,featureTabActions:null,featureTabEditActions:null,featureTabButtons:null,featureTabRowTypeIds:null,featureTabPersonas:null}).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(a=>{this.featureTabs||(this.featureTabs=[]),this.featureTabs.push(a),this.isPopoverOpen=!1})}removeTabConfirmation(t){this.alertService.presentAlert("Confirm Delete","Are you sure you want to delete this Tab").then(()=>{this.removeTab(t)})}removeTab(t){this.dataService.deleteFeatureTab(this.featureTabs[t].id).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(i=>{i&&this.featureTabs.splice(t,1)})}updateTab(t,n){const i=this.featureTabs[n].tab;t&&(i.name=t.target.value),this.dataService.updateTab(i).pipe((0,B.B)(5e3)).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{})}updateFeatureTab(t){const n={id:t.id,showTab:t.showTab,showForEfAdmin:t.showForEfAdmin,showForGuest:t.showForGuest,sortOrder:t.sortOrder,isDefaultInstanceTab:t.isDefaultInstanceTab,featureId:t.featureId,primaryFilter:t.primaryFilter?{...t.primaryFilter,componentTypeBw:null,componentTypeId:t?.primaryFilter?.componentType?.id??null,sectionId:null,templateField:{...t.primaryFilter.templateField,dropDownValues:[],systemPropertyId:t.primaryFilter.templateField.systemProperty?.id,parentIdSystemPropertyLink:t.primaryFilter.templateField.parentIdSystemPropertyLink?.id}}:null,secondaryFilter:t.secondaryFilter?{...t.secondaryFilter,componentTypeBw:null,componentTypeId:t?.secondaryFilter?.componentType?.id??null,sectionId:null,templateField:{...t.secondaryFilter.templateField,dropDownValues:[],systemPropertyId:t.secondaryFilter.templateField.systemProperty?.id,parentIdSystemPropertyLink:t.secondaryFilter.templateField.parentIdSystemPropertyLink?.id}}:null,typeId:t.typeId??t.type?.id??null,buttonText:t.buttonText,featureTabActions:t.featureTabActions?.map(i=>({id:null,actionId:i.id,featureTabId:t.id})),featureTabEditActions:t?.featureTabEditActions?.map(i=>({id:null,actionId:i.id,featureTabId:t.id})),featureTabButtons:t.featureTabButtons?.map(i=>({...i,featureTabButtonActions:i.featureTabButtonActions.map(a=>({...a,id:null})),buttonLinkId:i.buttonLinkType?.id})),featureTabRowTypeIds:t.featureTabRowTypes.map(i=>i.rowType.id),featureTabPersonas:t.featureTabPersonas.map(i=>i.id)};this.dataService.updateFeatureTab(n).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{})}tabChanged(t){this.tabIndex=t.index,this.showDefaultComponents.emit(this.featureTabs[t.index].isDefaultInstanceTab)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}logScrolling(t){this.scrollTop=t.detail.scrollTop}selectSection(t){this.selectedSection=t}openSettings(t){var n=this;return(0,_.A)(function*(){n.dataService.getFeatureTab(t.id).subscribe(function(){var i=(0,_.A)(function*(a){const l=yield n.modalController.create({component:le,cssClass:"tab-settings",componentProps:{featureTab:a},backdropDismiss:!1});return l.onDidDismiss().then(p=>{p?.data&&n.updateFeatureTab(p.data)}),yield l.present()});return function(a){return i.apply(this,arguments)}}())})()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u),e.rXU(I.u),e.rXU(s.W3))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-builder-tabs"]],viewQuery:function(n,i){if(1&n&&e.GBs(s.W9,5),2&n){let a;e.mGM(a=e.lsd())&&(i.content=a.first)}},inputs:{featureId:"featureId",featureTabs:"featureTabs",instance:"instance"},outputs:{showDefaultComponents:"showDefaultComponents"},features:[e.OA$],decls:7,vars:1,consts:[["mat-stretch-tabs","true","mat-align-tabs","start","color","primary",1,"tab-builder-container",3,"selectedTabChange"],["disabled",""],["matTabLabel",""],[3,"isOpen"],["matTabContent",""],[1,"tab-header"],[1,"italic-text"],[3,"change","value"],["fill","clear",3,"click","disabled"],["color","medium","name","trash"],[1,"content-container"],[1,"settings-grid"],[1,"align-right"],["color","light","id","default",3,"click"],[2,"flex","1","height","calc(100vh - 390px)",3,"ionScroll","scrollEvents"],[3,"sectionChanged","featureTab","templateId","instance"],[1,"add-tab"],["name","chevron-down-outline"],["button","",3,"click"]],template:function(n,i){1&n&&(e.j41(0,"mat-tab-group",0),e.bIt("selectedTabChange",function(l){return i.tabChanged(l)}),e.Z7z(1,uo,3,0,"mat-tab",null,e.fX1),e.j41(3,"mat-tab",1),e.DNE(4,mo,4,0,"ng-template",2),e.k0s()(),e.j41(5,"ion-popover",3),e.DNE(6,_o,5,0,"ng-template"),e.k0s()),2&n&&(e.R7$(),e.Dyx(i.featureTabs),e.R7$(4),e.Y8G("isOpen",i.isPopoverOpen))},dependencies:[s.Jm,s.hU,s.W9,s.lO,s.iq,s.$w,s.uz,s.he,s.nf,s.ln,s.qW,s.CF,s.Gw,y.$L,y.ES,y.mq,y.T8,pe],styles:[".tab-builder-container{height:100%;display:flex;flex:1}.tab-builder-container .settings-grid{margin:0 16px}.tab-builder-container .italic-text{font-style:italic;font-size:10px;text-transform:uppercase;padding-left:0}.tab-builder-container .tab-header{text-align:start}.tab-builder-container .mdc-tab__content{width:100%!important}.tab-builder-container .mat-mdc-tab-body-wrapper{overflow:hidden}.tab-builder-container .mdc-tab__text-label{color:#aaa!important}.tab-builder-container .mat-mdc-tab-header{background-color:#292929!important}.tab-builder-container .mdc-tab--active{color:#f99e00}.tab-builder-container .mdc-tab--active .mdc-tab__text-label{color:#f99e00!important}.tab-builder-container .mat-mdc-tab{margin-right:0!important;padding:0!important;justify-content:flex-start!important;min-width:0px!important;width:auto!important;color:#fff;border-bottom:#f99e00;font-size:1em}.tab-builder-container .mat-mdc-tab.mat-mdc-tab-disabled{opacity:1!important}.tab-builder-container ion-icon{height:16px}.tab-builder-container ion-input{min-height:0px!important;--padding-top: 0px;--padding-bottom: 0px}.tab-builder-container ion-tab-button{min-width:48px}.tab-builder-container .align-right{display:flex;flex-direction:row;justify-content:flex-end}.tab-builder-container .align-right ion-button{margin-right:16px;margin-bottom:16px;margin-top:16px}.tab-builder-container .content-container{width:100%}.tab-builder-container .add-tab{width:100%;display:flex;flex-direction:row;justify-content:flex-end}.tab-builder-container .add-tab ion-button{margin-right:16px;pointer-events:auto!important}\n"],encapsulation:2})}}return o})();function ho(o,r){if(1&o&&(e.j41(0,"span"),e.EFF(1),e.k0s()),2&o){const t=e.XpG();e.R7$(),e.SpI(" - ",t.componentType.parentTypeName,"")}}let ue=(()=>{class o{constructor(){}static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-template-field"]],inputs:{componentType:"componentType"},decls:10,vars:2,consts:[[1,"row"],[1,"ion-text-center"],["color","medium","name","apps",2,"font-size","0.8em","margin","8px 0 8px 0"],["color","light","name","cube-outline",2,"font-size","2em"]],template:function(n,i){1&n&&(e.j41(0,"ion-card")(1,"div",0)(2,"div",1),e.nrm(3,"ion-icon",2),e.k0s(),e.j41(4,"div",1),e.nrm(5,"ion-icon",3),e.k0s(),e.j41(6,"div",1)(7,"p"),e.EFF(8),e.DNE(9,ho,2,1,"span"),e.k0s()()()()),2&n&&(e.R7$(8),e.SpI(" ",i.componentType.name," "),e.R7$(),e.vxM(null!=i.componentType&&i.componentType.parentTypeName?9:-1))},dependencies:[s.b_,s.iq],styles:["[_nghost-%COMP%]   ion-card[_ngcontent-%COMP%]{padding:4px;background-color:#161616;aspect-ratio:2/3;min-height:40px;max-height:40px;width:100%;min-width:100%;margin-left:0}[_nghost-%COMP%]   ion-card[_ngcontent-%COMP%]:hover{cursor:move}[_nghost-%COMP%]   .row[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;align-content:center;justify-content:flex-start}[_nghost-%COMP%]   .row[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-right:10px}[_nghost-%COMP%]   .row[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}"]})}}return o})();function go(o,r){if(1&o&&(e.j41(0,"ion-row",9)(1,"ion-col",10),e.nrm(2,"app-feature-repository-template-field",11),e.k0s()()),2&o){const t=r.$implicit;e.Y8G("cdkDragData",t),e.R7$(2),e.Y8G("componentType",t)}}function fo(o,r){1&o&&(e.j41(0,"h5"),e.EFF(1,"Template fields"),e.k0s(),e.j41(2,"ion-grid",8),e.Z7z(3,go,3,2,"ion-row",9,e.fX1),e.k0s()),2&o&&(e.R7$(3),e.Dyx(r))}function Co(o,r){if(1&o&&(e.j41(0,"ion-content")(1,"div",7)(2,"p"),e.EFF(3,"These are the fields that a user sees in the builder. Click on a field to customize the field to this feature."),e.k0s(),e.DNE(4,fo,5,0),e.nI1(5,"async"),e.k0s()()),2&o){let t;const n=e.XpG();e.R7$(4),e.vxM((t=e.bMT(5,1,n.componentTypes$))?4:-1,t)}}function bo(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-component-editor",12),e.bIt("componentChanged",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.componentChanged())}),e.k0s()}if(2&o){const t=e.XpG();e.Y8G("component",t.component)}}function yo(o,r){if(1&o&&e.nrm(0,"app-section-edit",4),2&o){const t=e.XpG();e.Y8G("section",t.section)("templateForm",t.templateForm)("persistSortOrder",!0)}}let me=(()=>{class o{constructor(t,n){this.builderService=t,this.dataService=n,this.componentDestroyed$=new g.B,this.showDefaultComponents=!0}ngOnInit(){this.componentTypes$=this.dataService.getComponentTypes(),this.builderService.selectedComponent$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.component=t,this.section=null}),this.builderService.selectedSection$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.section=t,this.component=null}),this.builderService.templateFormCreated$.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{this.templateForm=t})}componentChanged(){this.feature={...this.feature},this.feature.featureTabs=this.feature.featureTabs.map(t=>({...t})),this.builderService.selectedComponent$.next(null)}setComponentList(t){this.showDefaultComponents=t}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(R.V),e.rXU(C.u))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-builder-fields"]],inputs:{feature:"feature"},decls:8,vars:5,consts:[[1,"parent-container"],["cdkDropListGroup","",2,"height","100%"],["size","3",1,"edit-col"],[3,"component"],[3,"section","templateForm","persistSortOrder"],["size","9"],[3,"showDefaultComponents","featureId","featureTabs"],[1,"inner-content"],["cdkDropList","",1,"fields-container"],["cdkDrag","",3,"cdkDragData"],["size","12"],[3,"componentType"],[3,"componentChanged","component"]],template:function(n,i){1&n&&(e.j41(0,"ion-grid",0)(1,"ion-row",1)(2,"ion-col",2),e.DNE(3,Co,6,3,"ion-content")(4,bo,1,1,"app-component-editor",3)(5,yo,1,3,"app-section-edit",4),e.k0s(),e.j41(6,"ion-col",5)(7,"app-feature-repository-builder-tabs",6),e.bIt("showDefaultComponents",function(l){return i.setComponentList(l)}),e.k0s()()()()),2&n&&(e.R7$(3),e.vxM(i.component||i.section?-1:3),e.R7$(),e.vxM(i.component?4:-1),e.R7$(),e.vxM(i.section?5:-1),e.R7$(2),e.Y8G("featureId",i.feature.id)("featureTabs",i.feature.featureTabs))},dependencies:[z.f,s.hU,s.W9,s.lO,s.ln,T.O7,T.RK,T.T1,Dn.O,de,ue,b.Jj],styles:['[_nghost-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:stretch;align-content:stretch}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .edit-col[_ngcontent-%COMP%]{background-color:#333;z-index:101;height:calc(100vh - 263px);max-height:calc(100vh - 263px);overflow:scroll}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .edit-col[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]{padding:10px}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .edit-col[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-family:"Exo 2";margin:0;color:#fff}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .edit-col[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-family:Roboto;color:#aaa}[_nghost-%COMP%]   .parent-container[_ngcontent-%COMP%]   .edit-col[_ngcontent-%COMP%]   .inner-content[_ngcontent-%COMP%]   .fields-container[_ngcontent-%COMP%]{background-color:#1e1e1e;padding:0 10px}']})}}return o})();function To(o,r){if(1&o){const t=e.RV6();e.j41(0,"ion-button",35),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.saveFeature())}),e.EFF(1,"Save"),e.k0s()}}function vo(o,r){1&o&&e.nrm(0,"app-select-option-control",27),2&o&&e.Y8G("options",r)("label","Journey Stage")("placeHolder","--select--")("backgroundColor","#181818")("allowClear",!0)("toolTip","Choose the journey stage")}function xo(o,r){1&o&&e.nrm(0,"app-select-option-control",28),2&o&&e.Y8G("options",r)("label","Role Objective")("placeHolder","--select--")("backgroundColor","#181818")("allowClear",!0)("toolTip","Choose the role objective")}function Fo(o,r){1&o&&e.nrm(0,"app-tag-select-option-control",29),2&o&&e.Y8G("options",r)("label","Grade")("placeHolder","--select--")("backgroundColor","#181818")("allowClear",!0)("limitTo",2)("toolTip","Choose the target audience")}function Mo(o,r){1&o&&e.nrm(0,"app-select-option-control",30),2&o&&e.Y8G("options",r)("label","Feature Type")("placeHolder","--select--")("backgroundColor","#181818")("allowClear",!0)("toolTip","Choose the feature type")}function ko(o,r){1&o&&e.nrm(0,"app-select-option-control",31),2&o&&e.Y8G("options",r)("label","Feature Category")("placeHolder","--select--")("backgroundColor","#181818")("allowClear",!0)("toolTip","Choose the Continuum Timeline")}function Oo(o,r){1&o&&e.nrm(0,"app-select-option-control",32),2&o&&e.Y8G("options",r)("label","Continuum Timeline")("placeHolder","--select--")("backgroundColor","#181818")("allowClear",!0)("toolTip","Choose the Continuum Timeline")}function Po(o,r){if(1&o){const t=e.RV6();e.j41(0,"form",4)(1,"ion-grid")(2,"ion-row")(3,"ion-col",5)(4,"h2"),e.EFF(5,"Feature Overview"),e.k0s(),e.j41(6,"p"),e.EFF(7,"This is the information that users will see when they come across the feature."),e.k0s()(),e.j41(8,"ion-col",6),e.DNE(9,To,2,0,"ion-button",7),e.k0s()(),e.j41(10,"ion-row")(11,"ion-col",8),e.nrm(12,"app-text-input-control",9),e.k0s(),e.j41(13,"ion-col"),e.nrm(14,"app-text-input-control",10),e.k0s()(),e.j41(15,"ion-row")(16,"ion-col"),e.nrm(17,"app-text-input-control",11),e.k0s()(),e.j41(18,"ion-row")(19,"ion-col"),e.nrm(20,"app-dynamic-text-input-control",12),e.k0s()(),e.j41(21,"ion-row")(22,"ion-col"),e.nrm(23,"app-dynamic-text-input-control",13),e.k0s()(),e.j41(24,"ion-row",14)(25,"ion-col",15)(26,"ion-label",16),e.EFF(27,"Display Object Count "),e.k0s(),e.nrm(28,"ion-checkbox",17),e.k0s()(),e.j41(29,"ion-row",18)(30,"ion-col",19),e.nrm(31,"app-text-input-control",20),e.k0s(),e.j41(32,"ion-col")(33,"ion-button",21),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.checkRoute())}),e.EFF(34,"Check"),e.k0s()()(),e.j41(35,"ion-row")(36,"ion-col",8),e.nrm(37,"app-file-upload-control",22),e.k0s(),e.j41(38,"ion-col",23),e.nrm(39,"app-file-upload-control",24),e.k0s()(),e.j41(40,"ion-row")(41,"ion-col",25),e.nrm(42,"app-file-upload-control",26),e.k0s()(),e.j41(43,"ion-row")(44,"ion-col",25),e.DNE(45,vo,1,6,"app-select-option-control",27),e.nI1(46,"async"),e.k0s(),e.j41(47,"ion-col",25),e.DNE(48,xo,1,6,"app-select-option-control",28),e.nI1(49,"async"),e.k0s()(),e.j41(50,"ion-row")(51,"ion-col",25),e.DNE(52,Fo,1,7,"app-tag-select-option-control",29),e.nI1(53,"async"),e.k0s(),e.j41(54,"ion-col",25),e.DNE(55,Mo,1,6,"app-select-option-control",30),e.nI1(56,"async"),e.k0s()(),e.j41(57,"ion-row")(58,"ion-col",25),e.DNE(59,ko,1,6,"app-select-option-control",31),e.nI1(60,"async"),e.k0s(),e.j41(61,"ion-col",25),e.DNE(62,Oo,1,6,"app-select-option-control",32),e.nI1(63,"async"),e.k0s()(),e.j41(64,"ion-row")(65,"ion-col",33)(66,"ion-label",34),e.EFF(67,"Is Full Width Page "),e.k0s(),e.nrm(68,"ion-checkbox",17),e.k0s()()()()}if(2&o){let t,n,i,a,l,p;const m=e.XpG();e.Y8G("formGroup",m.featureForm),e.R7$(9),e.vxM(m.showSave?9:-1),e.R7$(3),e.Y8G("label","Feature Code")("placeHolder","Enter feature code")("toolTip","The code for the feature"),e.R7$(2),e.Y8G("label","Feature Name")("placeHolder","Enter feature Name")("toolTip","The title for the feature"),e.R7$(3),e.Y8G("label","Feature Description")("placeHolder","Enter feature description")("toolTip","The description for the feature"),e.R7$(3),e.Y8G("label","Feature Descriptors")("placeHolder","Enter feature descriptors")("toolTip","The descriptors for the feature"),e.R7$(3),e.Y8G("label","Instance Descriptors")("placeHolder","Enter instance descriptors")("toolTip","The descriptors for the instance"),e.R7$(5),e.Y8G("formControlName","displayObjectCount"),e.R7$(3),e.Y8G("label","Feature Slug")("placeHolder","Add the feature`s slug here...")("toolTip","The feature`s slug"),e.R7$(6),e.Y8G("label","Icon")("toolTip","The icon image for the feature"),e.R7$(2),e.Y8G("label","Feature Cover Media")("toolTip","The cover media for the feature"),e.R7$(3),e.Y8G("label","Feature Texture")("toolTip","The texture image for the feature"),e.R7$(3),e.vxM((t=e.bMT(46,34,m.journeyStage$))?45:-1,t),e.R7$(3),e.vxM((n=e.bMT(49,36,m.roleObjectives$))?48:-1,n),e.R7$(4),e.vxM((i=e.bMT(53,38,m.targetAudience$))?52:-1,i),e.R7$(3),e.vxM((a=e.bMT(56,40,m.featureTypes$))?55:-1,a),e.R7$(4),e.vxM((l=e.bMT(60,42,m.featureCategory$))?59:-1,l),e.R7$(3),e.vxM((p=e.bMT(63,44,m.continuumTimeline$))?62:-1,p),e.R7$(6),e.Y8G("formControlName","isFullWidth")}}let _e=(()=>{class o{constructor(t,n){this.dataService=t,this.instanceService=n,this.featureUpdated=new e.bkB,this.componentDestroyed$=new g.B,this.showSave=!1}ngOnInit(){this.createFormControls(),this.createForm(),this.initData()}createFormControls(){this.title=new c.hs(this.feature?.title,[c.k0.required]),this.code=new c.hs(this.feature?.code),this.description=new c.hs(this.feature?.description),this.featureDescriptors=new c.hs(this.feature?.descriptors),this.instanceDescriptors=new c.hs(this.feature?.instanceDescriptors),this.displayObjectCount=new c.hs(this.feature?.displayObjectCount),this.featureSlug=new c.hs(this.feature?.featureSlug),this.iconAssetId=new c.hs(this.feature?.iconAssetId),this.coverMediaAssetId=new c.hs(this.feature?.coverMediaAssetId),this.textureAssetId=new c.hs(this.feature?.textureAssetId),this.journeyStageId=new c.hs(this.feature?.journeyStageId),this.roleObjectiveId=new c.hs(this.feature?.roleObjectiveId),this.targetAudienceId=new c.hs(this.feature?.targetAudienceId),this.featureTypeId=new c.hs(this.feature?.featureType?.id),this.continuumTimelineId=new c.hs(this.feature?.continuumTimelineId),this.featureCategoryId=new c.hs(this.feature?.featureCategoryId),this.isFullWidth=new c.hs(this.feature.isFullWidth)}createForm(){this.featureForm=new c.J3({title:this.title,code:this.code,description:this.description,featureDescriptors:this.featureDescriptors,instanceDescriptors:this.instanceDescriptors,displayObjectCount:this.displayObjectCount,featureSlug:this.featureSlug,iconAssetId:this.iconAssetId,coverMediaAssetId:this.coverMediaAssetId,textureAssetId:this.textureAssetId,journeyStageId:this.journeyStageId,roleObjectiveId:this.roleObjectiveId,targetAudienceId:this.targetAudienceId,featureTypeId:this.featureTypeId,continuumTimelineId:this.continuumTimelineId,featureCategoryId:this.featureCategoryId,isFullWidth:this.isFullWidth}),this.featureForm.valueChanges.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(()=>{this.featureIn={title:this.title.value,code:this.code.value,description:this.description.value,descriptors:this.featureDescriptors.value,instanceDescriptors:this.instanceDescriptors.value,displayObjectCount:this.displayObjectCount.value,featureSlug:this.featureSlug.value,iconAssetId:this.iconAssetId.value,coverMediaAssetId:this.coverMediaAssetId.value,textureAssetId:this.textureAssetId.value,journeyStageId:this.journeyStageId.value,roleObjectiveId:this.roleObjectiveId.value,targetAudienceId:this.targetAudienceId.value,featureTypeId:this.featureTypeId.value,continuumTimelineId:this.continuumTimelineId.value,featureCategoryId:this.featureCategoryId.value,isFullWidth:this.isFullWidth.value},this.featureForm.valid&&this.featureForm.dirty&&(this.showSave=!0)})}initData(){this.journeyStage$=this.dataService.getTagChildren("ED64B075-33C5-4BB7-A488-78FC8F754090").pipe((0,f.T)(t=>t.map(n=>({id:n.id,parentId:n.parentId,value:n.name,level:n.treeLevel})))),this.roleObjectives$=this.dataService.getTagChildren("1BE81735-8236-4B49-B824-936899903693").pipe((0,f.T)(t=>t.map(n=>({id:n.id,parentId:n.parentId,value:n.name,level:n.treeLevel})))),this.targetAudience$=this.dataService.getTagChildren("8FDCB339-5853-4FA7-B88E-9C53156CE73E").pipe((0,f.T)(t=>t.map(n=>({id:n.id,parentId:n.parentId,value:n.name,level:n.treeLevel,hasChildren:n.inverseParent??!1})))),this.featureTypes$=this.dataService.getFeatureTypes().pipe((0,f.T)(t=>t.map(n=>({id:n.id,value:n.name})))),this.continuumTimeline$=this.dataService.getTagChildren("F933C359-F5E8-4104-A10D-52FE3F7F5C82").pipe((0,f.T)(t=>t.map(n=>({id:n.id,parentId:n.parentId,value:n.name,level:n.treeLevel})))),this.featureCategory$=this.dataService.getTagChildren("D56770E6-2C23-4C1E-91BD-A4BADDA96617").pipe((0,f.T)(t=>t.map(n=>({id:n.id,parentId:n.parentId,value:n.name,level:n.treeLevel}))))}saveFeature(){this.featureForm.valid&&this.featureIn&&this.dataService.putFeature(this.feature.id,this.featureIn).pipe((0,u.Q)(this.componentDestroyed$)).subscribe(t=>{t&&(this.featureUpdated.emit(t),this.showSave=!1)})}checkRoute(){this.instanceService.openInstance(this.featureSlug.value)}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u),e.rXU(G.b))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-overview"]],inputs:{feature:"feature"},outputs:{featureUpdated:"featureUpdated"},decls:9,vars:1,consts:[[1,"overview-row"],["size","3",1,"ion-padding",2,"background-color","#333333"],["size","9"],[1,"feature-overview","ion-padding"],[3,"formGroup"],[1,"ion-padding-start"],["size","2",1,"save-button"],["fill","clear","color","primary"],["size","4"],["formControlName","code",3,"label","placeHolder","toolTip"],["formControlName","title",3,"label","placeHolder","toolTip"],["formControlName","description",3,"label","placeHolder","toolTip"],["formControlName","featureDescriptors",3,"label","placeHolder","toolTip"],["formControlName","instanceDescriptors",3,"label","placeHolder","toolTip"],[1,"align-to-form"],[1,"checkbox-row"],["title","Append object count to descriptors"],[3,"formControlName"],[1,"ion-align-items-end"],["size","11"],["formControlName","featureSlug",3,"label","placeHolder","toolTip"],["fill","clear",3,"click"],["formControlName","iconAssetId",3,"label","toolTip"],["size","8"],["formControlName","coverMediaAssetId",3,"label","toolTip"],["size","6"],["formControlName","textureAssetId",3,"label","toolTip"],["formControlName","journeyStageId",3,"options","label","placeHolder","backgroundColor","allowClear","toolTip"],["formControlName","roleObjectiveId",3,"options","label","placeHolder","backgroundColor","allowClear","toolTip"],["formControlName","targetAudienceId",3,"options","label","placeHolder","backgroundColor","allowClear","limitTo","toolTip"],["formControlName","featureTypeId",3,"options","label","placeHolder","backgroundColor","allowClear","toolTip"],["formControlName","featureCategoryId",3,"options","label","placeHolder","backgroundColor","allowClear","toolTip"],["formControlName","continuumTimelineId",3,"options","label","placeHolder","backgroundColor","allowClear","toolTip"],["size","6",1,"checkbox-row"],["title","Is Full Width Page"],["fill","clear","color","primary",3,"click"]],template:function(n,i){1&n&&(e.j41(0,"ion-grid")(1,"ion-row",0)(2,"ion-col",1)(3,"p"),e.EFF(4,"Use these fields to update the basic information about this feature. This is what they would see in Workspace."),e.k0s()(),e.j41(5,"ion-col",2)(6,"ion-content")(7,"div",3),e.DNE(8,Po,69,46,"form",4),e.k0s()()()()()),2&n&&(e.R7$(8),e.vxM(i.featureForm?8:-1))},dependencies:[v.c,F.U,A.r,U.u,J.s,s.Jm,s.eY,s.hU,s.W9,s.lO,s.he,s.ln,s.hB,c.qT,c.BC,c.cb,c.j4,c.JD,b.Jj],styles:['[_nghost-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:stretch;align-content:stretch;height:calc(100vh - 260px)}[_nghost-%COMP%]   .overview-row[_ngcontent-%COMP%]{height:100%!important}[_nghost-%COMP%]   .feature-overview[_ngcontent-%COMP%]{flex:1;border-radius:5px;margin:0 16px 16px;background-color:#111}[_nghost-%COMP%]   h1[_ngcontent-%COMP%], [_nghost-%COMP%]   h2[_ngcontent-%COMP%], [_nghost-%COMP%]   h3[_ngcontent-%COMP%], [_nghost-%COMP%]   h4[_ngcontent-%COMP%], [_nghost-%COMP%]   h5[_ngcontent-%COMP%], [_nghost-%COMP%]   h6[_ngcontent-%COMP%]{font-family:"Exo 2";margin:0;color:#fff}[_nghost-%COMP%]   p[_ngcontent-%COMP%]{font-family:Roboto;color:#aaa}[_nghost-%COMP%]   .save-button[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:flex-start}[_nghost-%COMP%]   .align-to-form[_ngcontent-%COMP%]{padding-left:15px;padding-top:15px}[_nghost-%COMP%]   .checkbox-row[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;margin-top:10px;margin-bottom:10px}[_nghost-%COMP%]   .checkbox-row[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-left:10px}']})}}return o})();function Do(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-feature-repository-overview",5),e.bIt("featureUpdated",function(i){e.eBV(t);const a=e.XpG(2);return e.Njj(a.updateFeature(i))}),e.k0s()}if(2&o){const t=e.XpG(2);e.Y8G("feature",t.feature)}}function wo(o,r){if(1&o&&e.DNE(0,Do,1,1,"app-feature-repository-overview",4),2&o){const t=e.XpG();e.vxM(t.feature?0:-1)}}function Io(o,r){if(1&o&&e.nrm(0,"app-feature-repository-builder-fields",4),2&o){const t=e.XpG(2);e.Y8G("feature",t.feature)}}function Ro(o,r){if(1&o&&e.DNE(0,Io,1,1,"app-feature-repository-builder-fields",4),2&o){const t=e.XpG();e.vxM(t.feature?0:-1)}}let he=(()=>{class o{constructor(){this.featureUpdated=new e.bkB}updateFeature(t){this.featureUpdated.emit(t)}static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-template"]],inputs:{feature:"feature"},outputs:{featureUpdated:"featureUpdated"},decls:5,vars:0,consts:[["mat-stretch-tabs","false","mat-align-tabs","start","color","accent"],["label","FEATURE OVERVIEW"],["matTabContent",""],["label","TAB BUILDER"],[3,"feature"],[3,"featureUpdated","feature"]],template:function(n,i){1&n&&(e.j41(0,"mat-tab-group",0)(1,"mat-tab",1),e.DNE(2,wo,1,1,"ng-template",2),e.k0s(),e.j41(3,"mat-tab",3),e.DNE(4,Ro,1,1,"ng-template",2),e.k0s()())},dependencies:[y.$L,y.mq,y.T8,me,_e],styles:["[_nghost-%COMP%]{flex:1;display:flex;flex-direction:column;align-items:stretch;align-content:stretch}[_nghost-%COMP%]   .mat-mdc-tab-group[_ngcontent-%COMP%]{flex:1;color:var(--ion-color-accent)}[_nghost-%COMP%]   .mat-mdc-tab-body-wrapper[_ngcontent-%COMP%]{display:flex;flex-grow:1}[_nghost-%COMP%]   .mdc-tab__content[_ngcontent-%COMP%]{display:flex;flex-grow:1}"]})}}return o})();function So(o,r){if(1&o&&e.nrm(0,"app-feature-repository-dashboard",9),2&o){const t=e.XpG();e.Y8G("feature",t)}}function jo(o,r){if(1&o){const t=e.RV6();e.j41(0,"app-feature-repository-template",10),e.bIt("featureUpdated",function(i){e.eBV(t);const a=e.XpG(3);return e.Njj(a.updateFeature(i))}),e.k0s()}if(2&o){const t=e.XpG(2);e.Y8G("feature",t)}}function $o(o,r){1&o&&(e.j41(0,"mat-tab",6),e.DNE(1,jo,1,1,"ng-template",5),e.k0s())}function Bo(o,r){if(1&o&&e.nrm(0,"app-feature-repository-communications",9),2&o){const t=e.XpG(2);e.Y8G("feature",t)}}function Go(o,r){1&o&&(e.j41(0,"mat-tab",7),e.DNE(1,Bo,1,1,"ng-template",5),e.k0s())}function Eo(o,r){if(1&o&&e.nrm(0,"app-feature-repository-analytics",9),2&o){const t=e.XpG(2);e.Y8G("feature",t)}}function Xo(o,r){1&o&&(e.j41(0,"mat-tab",8),e.DNE(1,Eo,1,1,"ng-template",5),e.k0s())}function No(o,r){if(1&o){const t=e.RV6();e.j41(0,"div",0)(1,"div",1)(2,"h6"),e.EFF(3),e.k0s(),e.j41(4,"h1"),e.EFF(5),e.k0s()(),e.j41(6,"div",2)(7,"mat-tab-group",3),e.bIt("selectedTabChange",function(i){e.eBV(t);const a=e.XpG();return e.Njj(a.tabSelected(i))}),e.j41(8,"mat-tab",4),e.DNE(9,So,1,1,"ng-template",5),e.k0s(),e.DNE(10,$o,2,0,"mat-tab",6)(11,Go,2,0,"mat-tab",7)(12,Xo,2,0,"mat-tab",8),e.k0s()()()}if(2&o){const t=e.XpG();e.R7$(3),e.SpI("You are ",t.tabDescription," the template for"),e.R7$(2),e.JRh(r.title),e.R7$(5),e.vxM(!0===t.isEfManager?10:-1),e.R7$(),e.vxM(!0===t.isEfManager?11:-1),e.R7$(),e.vxM(!0===t.isEfManager?12:-1)}}let X=(()=>{class o{constructor(t,n,i,a){this.dataService=t,this.activatedRoute=n,this.breadcrumbService=i,this.router=a,this.componentDestroyed$=new g.B,this.minimized=!0,this.refresh=new g.B,this.tabDescription="viewing"}onWindowScroll(t){!this.minimized&&t.detail.scrollTop>10&&(this.minimized=!0)}ngOnInit(){const t=JSON.parse(localStorage.getItem("user_context"));this.isEfManager=t?.isEfManager,this.activatedRoute.params.pipe((0,u.Q)(this.componentDestroyed$)).subscribe(n=>{this.featureId=n.id,this.feature$=this.dataService.getFeatureById(this.featureId).pipe((0,f.T)(i=>(this.breadcrumbService.addBreadCrumb(i.id,this.router.url,i.title,null,i.featureType.name),i)))})}updateFeature(t){t&&(this.feature$=new On.c(n=>n.next(t)))}tabSelected(t){switch(t.index){case 0:case 3:this.tabDescription="viewing";break;case 1:case 2:this.tabDescription="updating"}}ngOnDestroy(){this.componentDestroyed$.next(!0),this.componentDestroyed$.complete()}static{this.\u0275fac=function(n){return new(n||o)(e.rXU(C.u),e.rXU(S.nX),e.rXU(Pn.b),e.rXU(S.Ix))}}static{this.\u0275cmp=e.VBU({type:o,selectors:[["app-feature-repository-builder"]],inputs:{featureId:"featureId"},decls:2,vars:3,consts:[[1,"repository"],[1,"header-container",2,"--background-image","url('assets/images/admin-header.jpg')"],[1,"content"],["mat-stretch-tabs","false","mat-align-tabs","start",3,"selectedTabChange"],["id","material-tab","label","REPOSITORY"],["matTabContent",""],["label","TEMPLATE",1,"material-tab"],["label","COMMUNICATIONS",1,"material-tab"],["label","ANALYTICS",1,"material-tab"],[3,"feature"],[3,"featureUpdated","feature"]],template:function(n,i){if(1&n&&(e.DNE(0,No,13,5,"div",0),e.nI1(1,"async")),2&n){let a;e.vxM((a=e.bMT(1,1,i.feature$))?0:-1,a)}},dependencies:[y.$L,y.mq,y.T8,q,K,he,oe,b.Jj],styles:['.repository{flex:1;display:flex;align-items:stretch;align-content:stretch;flex-direction:column;color:#fff;margin-left:var(--page-margin-left-player);margin-right:var(--page-margin-right-player)}.repository .block-view{margin-left:0!important;margin-right:0!important}.repository .header-container{z-index:1000;height:80px;padding-top:8px;padding-left:var(--page-margin-left-player);padding-right:var(--page-margin-right-player);background-color:#111;background-image:var(--background-image);background-size:cover}.repository .header-container h1{font-family:"Exo 2";text-shadow:2px 2px #000;margin:0;font-weight:800;align-self:flex-end;font-size:30px}.repository .header-container h6{font-family:"Exo 2";text-shadow:2px 2px #000;margin:4px 4px 4px 0;font-weight:800;align-self:flex-end;font-size:1em;font-style:italic;font-weight:500}.repository .header-container p{bottom:0;font-family:Roboto;text-shadow:2px 2px #000}.repository .content{flex:1;display:flex;align-items:stretch;align-content:stretch;flex-direction:column}.repository .mat-mdc-tab-group{flex:1}.repository .mat-mdc-tab-body-wrapper{display:flex;flex-grow:1}.repository .mat-mdc-tab{margin-right:35px;padding:0;justify-content:flex-start;min-width:0px;width:auto;color:#fff;border-bottom:#f99e00;font-size:1em}.repository .mat-mdc-tab div#mat-mdc-tab-0-0{padding:0!important;width:auto!important;margin-left:0!important}.repository .mdc-tab__text-label{color:#aaa!important}.repository .mdc-tab--active{color:#f99e00}.repository .mdc-tab--active .mdc-tab__text-label{color:#f99e00!important}\n'],encapsulation:2})}}return o})();const Vo=[R.V],Ao=[v.c,V.M,Y.L,L.i,F.U,Q.$,z.f,A.r,U.u,J.s,ne.k],Uo=[{path:"builder",component:X},{path:"builder/:id",component:X}];let Lo=(()=>{class o{static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275mod=e.$C({type:o})}static{this.\u0275inj=e.G2t({providers:[...Vo],imports:[Ao,ye.G,be.Q,Ce.x,S.iI.forChild(Uo),T.ad,fe.t]})}}return o})()}}]);