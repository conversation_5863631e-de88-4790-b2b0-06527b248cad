-- Additional SQL variations for fetching feature assets
-- These are simpler, more focused queries for specific use cases

-- =============================================================================
-- 1. SIMPLE QUERY: Get all direct feature assets only
-- =============================================================================
DECLARE @FeatureId UNIQUEIDENTIFIER = 'YOUR_FEATURE_ID_HERE';

SELECT 
    f.Id as FeatureId,
    f.Title as FeatureTitle,
    'Icon Asset' as AssetType,
    a.Id as AssetId,
    a.Name as AssetName,
    a.BlobUrl,
    a.ContentType,
    a.Size
FROM Features f
INNER JOIN Assets a ON a.Id = f.IconAssetId
WHERE f.Id = @FeatureId

UNION ALL

SELECT 
    f.Id as FeatureId,
    f.Title as FeatureTitle,
    'Cover Media Asset' as AssetType,
    a.Id as AssetId,
    a.Name as AssetName,
    a.BlobUrl,
    a.ContentType,
    a.Size
FROM Features f
INNER JOIN Assets a ON a.Id = f.CoverMediaAssetId
WHERE f.Id = @FeatureId

UNION ALL

SELECT 
    f.Id as FeatureId,
    f.Title as FeatureTitle,
    'Texture Asset' as AssetType,
    a.Id as AssetId,
    a.Name as AssetName,
    a.BlobUrl,
    a.ContentType,
    a.Size
FROM Features f
INNER JOIN Assets a ON a.Id = f.TextureAssetId
WHERE f.Id = @FeatureId;

-- =============================================================================
-- 2. GET ALL INSTANCE ASSETS FOR A FEATURE
-- =============================================================================
SELECT 
    i.Id as InstanceId,
    i.Title as InstanceTitle,
    CASE 
        WHEN a.Id = i.ImageAssetId THEN 'Instance Image'
        WHEN a.Id = i.IconAssetId THEN 'Instance Icon'
        WHEN a.Id = i.CoverMediaAssetId THEN 'Instance Cover Media'
    END as AssetType,
    a.Id as AssetId,
    a.Name as AssetName,
    a.BlobUrl,
    a.ContentType,
    a.Size,
    a.CreatedDate
FROM Instances i
INNER JOIN Assets a ON (a.Id = i.ImageAssetId OR a.Id = i.IconAssetId OR a.Id = i.CoverMediaAssetId)
WHERE i.FeatureId = @FeatureId
ORDER BY i.Title, AssetType;

-- =============================================================================
-- 3. GET COMPONENT ASSETS USED IN FEATURE INSTANCES
-- =============================================================================
SELECT DISTINCT
    i.Id as InstanceId,
    i.Title as InstanceTitle,
    c.Id as ComponentId,
    ct.Name as ComponentTypeName,
    a.Id as AssetId,
    a.Name as AssetName,
    a.BlobUrl,
    a.ContentType,
    a.Size
FROM Instances i
INNER JOIN InstanceComponents ic ON ic.InstanceId = i.Id
INNER JOIN Components c ON c.Id = ic.ComponentId
INNER JOIN ComponentTypes ct ON ct.Id = c.ComponentTypeId
INNER JOIN Assets a ON a.Id = TRY_CAST(ic.Value AS UNIQUEIDENTIFIER)
WHERE i.FeatureId = @FeatureId
AND ic.Value IS NOT NULL
AND LEN(ic.Value) = 36
ORDER BY i.Title, ct.Name, a.Name;

-- =============================================================================
-- 4. GET MEDIA ASSETS ONLY (Images, Videos, etc.)
-- =============================================================================
WITH FeatureAssets AS (
    -- Direct feature assets
    SELECT a.Id, a.Name, a.BlobUrl, a.ContentType, a.Size, a.CreatedDate, 'Feature' as Source
    FROM Features f
    INNER JOIN Assets a ON (a.Id = f.IconAssetId OR a.Id = f.CoverMediaAssetId OR a.Id = f.TextureAssetId)
    WHERE f.Id = @FeatureId
    
    UNION
    
    -- Instance assets
    SELECT a.Id, a.Name, a.BlobUrl, a.ContentType, a.Size, a.CreatedDate, 'Instance' as Source
    FROM Instances i
    INNER JOIN Assets a ON (a.Id = i.ImageAssetId OR a.Id = i.IconAssetId OR a.Id = i.CoverMediaAssetId)
    WHERE i.FeatureId = @FeatureId
    
    UNION
    
    -- Component assets
    SELECT a.Id, a.Name, a.BlobUrl, a.ContentType, a.Size, a.CreatedDate, 'Component' as Source
    FROM Instances i
    INNER JOIN InstanceComponents ic ON ic.InstanceId = i.Id
    INNER JOIN Assets a ON a.Id = TRY_CAST(ic.Value AS UNIQUEIDENTIFIER)
    WHERE i.FeatureId = @FeatureId
    AND ic.Value IS NOT NULL
    AND LEN(ic.Value) = 36
)
SELECT DISTINCT *
FROM FeatureAssets
WHERE ContentType LIKE 'image/%' 
   OR ContentType LIKE 'video/%' 
   OR ContentType LIKE 'audio/%'
ORDER BY ContentType, Name;

-- =============================================================================
-- 5. GET ASSET SUMMARY BY TYPE
-- =============================================================================
WITH AllFeatureAssets AS (
    SELECT 'Direct Feature Asset' as Category, COUNT(*) as AssetCount, SUM(a.Size) as TotalSize
    FROM Features f
    INNER JOIN Assets a ON (a.Id = f.IconAssetId OR a.Id = f.CoverMediaAssetId OR a.Id = f.TextureAssetId)
    WHERE f.Id = @FeatureId
    
    UNION ALL
    
    SELECT 'Instance Asset' as Category, COUNT(*) as AssetCount, SUM(a.Size) as TotalSize
    FROM Instances i
    INNER JOIN Assets a ON (a.Id = i.ImageAssetId OR a.Id = i.IconAssetId OR a.Id = i.CoverMediaAssetId)
    WHERE i.FeatureId = @FeatureId
    
    UNION ALL
    
    SELECT 'Component Asset' as Category, COUNT(*) as AssetCount, SUM(a.Size) as TotalSize
    FROM Instances i
    INNER JOIN InstanceComponents ic ON ic.InstanceId = i.Id
    INNER JOIN Assets a ON a.Id = TRY_CAST(ic.Value AS UNIQUEIDENTIFIER)
    WHERE i.FeatureId = @FeatureId
    AND ic.Value IS NOT NULL
    AND LEN(ic.Value) = 36
    
    UNION ALL
    
    SELECT 'Campaign Asset' as Category, COUNT(*) as AssetCount, SUM(a.Size) as TotalSize
    FROM Campaigns c
    INNER JOIN Assets a ON a.Id = c.IconAssetId
    WHERE c.FeatureId = @FeatureId
)
SELECT 
    Category,
    AssetCount,
    TotalSize,
    CASE 
        WHEN TotalSize > 1073741824 THEN CAST(TotalSize / 1073741824.0 AS DECIMAL(10,2)) + ' GB'
        WHEN TotalSize > 1048576 THEN CAST(TotalSize / 1048576.0 AS DECIMAL(10,2)) + ' MB'
        WHEN TotalSize > 1024 THEN CAST(TotalSize / 1024.0 AS DECIMAL(10,2)) + ' KB'
        ELSE CAST(TotalSize AS VARCHAR) + ' bytes'
    END as FormattedSize
FROM AllFeatureAssets
WHERE AssetCount > 0
ORDER BY AssetCount DESC;

-- =============================================================================
-- 6. FIND UNUSED ASSETS (Assets not referenced by any feature relationship)
-- =============================================================================
SELECT 
    a.Id,
    a.Name,
    a.BlobUrl,
    a.ContentType,
    a.Size,
    a.CreatedDate
FROM Assets a
WHERE a.Id NOT IN (
    -- Direct feature assets
    SELECT COALESCE(f.IconAssetId, f.CoverMediaAssetId, f.TextureAssetId)
    FROM Features f
    WHERE f.Id = @FeatureId
    AND (f.IconAssetId IS NOT NULL OR f.CoverMediaAssetId IS NOT NULL OR f.TextureAssetId IS NOT NULL)
    
    UNION
    
    -- Instance assets
    SELECT COALESCE(i.ImageAssetId, i.IconAssetId, i.CoverMediaAssetId)
    FROM Instances i
    WHERE i.FeatureId = @FeatureId
    AND (i.ImageAssetId IS NOT NULL OR i.IconAssetId IS NOT NULL OR i.CoverMediaAssetId IS NOT NULL)
    
    UNION
    
    -- Component assets
    SELECT TRY_CAST(ic.Value AS UNIQUEIDENTIFIER)
    FROM Instances i
    INNER JOIN InstanceComponents ic ON ic.InstanceId = i.Id
    WHERE i.FeatureId = @FeatureId
    AND ic.Value IS NOT NULL
    AND LEN(ic.Value) = 36
    AND TRY_CAST(ic.Value AS UNIQUEIDENTIFIER) IS NOT NULL
)
ORDER BY a.CreatedDate DESC;
