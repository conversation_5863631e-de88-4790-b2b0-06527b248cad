namespace EdgeFactor.Common.Models.Asset
{
    public class AssetIn
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public string? ContentType { get; set; }
        public long? Size { get; set; }
        public long? CreatedDate { get; set; }
        public string? ContainerName { get; set; }
        public string? StreamingLocator { get; set; }
        public string? EmbedCode { get; set; }
        public string? UrlUpload { get; set; }
        public string MediaUploadType { get; set; }
    }
}
