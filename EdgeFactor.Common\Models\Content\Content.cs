using EdgeFactor.Common.Models.Completion;

namespace EdgeFactor.Common.Models.Content
{
    public class Content
    {
        public Content() { }
        public Content(Guid Id, Guid? FeatureId, string? FeatureName, string? FeatureType, string? FeatureDescriptors, string? InstanceDescriptors, bool? DisplayObjectCount, string? InstanceName, string? FeatureDescription, string? InstanceDescription, string? Property, string? Property2, Guid? IconAssetId, Guid? CoverAssetId, string? Action, string? ActionUrl, int? ContentOrder, int EntityType, int ActionBW, string? ExternalLogoUrl = null, string? ExternalCoverUrl = null, int? TotalBlockItems = 0, string? JoinCode = null, bool? CanExport = false, string? OrganizationName = null, int? Progress = null, string? Status = null, long? LastModifiedDate = null, bool? IsEnrolled = null, string? Slug = null, string? defaultInstanceSlug = null, bool sameUrlNavigation = false, double? latitude = null, double? longitude = null, long? createdDate = null, bool? isInstanceOwner = null)
        {
            this.Id = Id;
            this.FeatureId = FeatureId;
            this.FeatureName = FeatureName;
            this.FeatureType = FeatureType;
            this.FeatureDescriptors = FeatureDescriptors;
            this.InstanceDescriptors = InstanceDescriptors;
            this.DisplayObjectCount = DisplayObjectCount;
            this.InstanceName = InstanceName;
            this.FeatureDescription = FeatureDescription;
            this.InstanceDescription = InstanceDescription;
            this.Property = Property;
            this.Property2 = Property2;
            this.IconAssetId = IconAssetId;
            this.CoverAssetId = CoverAssetId;
            this.Action = Action;
            this.ActionUrl = ActionUrl;
            this.ContentOrder = ContentOrder;
            this.EntityType = EntityType;
            this.ActionBW = ActionBW;
            this.ExternalLogoUrl = ExternalLogoUrl;
            this.ExternalCoverUrl = ExternalCoverUrl;
            this.TotalBlockItems = TotalBlockItems;
            this.JoinCode = JoinCode;
            this.CanExport = CanExport;
            this.OrganizationName = OrganizationName;
            this.Progress = Progress;
            this.Status = Status;
            this.LastModifiedDate = LastModifiedDate;
            this.IsEnrolled = IsEnrolled;
            this.Slug = Slug;
            this.DefaultInstanceSlug = defaultInstanceSlug;
            this.SameUrlNavigation = sameUrlNavigation;
            this.Latitude = latitude;
            this.Longitude = longitude;
            this.CreatedDate = createdDate;
            this.IsInstanceOwner = isInstanceOwner;
        }

        public Content(Guid Id, Guid? FeatureId, string? FeatureName, string? FeatureType, string? FeatureDescriptors, string? InstanceDescriptors, bool? DisplayObjectCount, string? InstanceName, string? FeatureDescription, string? InstanceDescription, string? Property, string? Property2, Guid? IconAssetId, Guid? CoverAssetId, string? Action, string? ActionUrl, int? ContentOrder, int EntityType, int ActionBW, int? TotalBlockItems, string? JoinCode, bool? CanExport, string? OrganizationName, int? Progress, string? Status, long? LastModifiedDate, bool? IsEnrolled, string? Slug, double? latitude, double? longitude, long? createdDate = null, bool? isInstanceOwner = null)
        {
            this.Id = Id;
            this.FeatureId = FeatureId;
            this.FeatureName = FeatureName;
            this.FeatureType = FeatureType;
            this.FeatureDescriptors = FeatureDescriptors;
            this.InstanceDescriptors = InstanceDescriptors;
            this.DisplayObjectCount = DisplayObjectCount;
            this.InstanceName = InstanceName;
            this.FeatureDescription = FeatureDescription;
            this.InstanceDescription = InstanceDescription;
            this.Property = Property;
            this.Property2 = Property2;
            this.IconAssetId = IconAssetId;
            this.CoverAssetId = CoverAssetId;
            this.Action = Action;
            this.ActionUrl = ActionUrl;
            this.ContentOrder = ContentOrder;
            this.EntityType = EntityType;
            this.ActionBW = ActionBW;
            this.TotalBlockItems = TotalBlockItems;
            this.JoinCode = JoinCode;
            this.CanExport = CanExport;
            this.OrganizationName = OrganizationName;
            this.Progress = Progress;
            this.Status = Status;
            this.LastModifiedDate = LastModifiedDate;
            this.IsEnrolled = IsEnrolled;
            this.Slug = Slug;
            this.Latitude = latitude;
            this.Longitude = longitude;
            this.CreatedDate = createdDate;
            this.IsInstanceOwner = isInstanceOwner;
        }

        public Content(Guid Id, Guid? FeatureId, string? FeatureName, string? FeatureType, string? FeatureDescriptors, string? InstanceDescriptors, bool DisplayObjectCount, string? InstanceName, string? FeatureDescription, string? InstanceDescription, string? Property, string? Property2, Guid? IconAssetId, Guid? CoverAssetId, string? Action, string? ActionUrl, int? ContentOrder, int EntityType, int ActionBW, double? latitude, double? longitude, long? createdDate = null, bool? isInstanceOwner = null)
        {
            this.Id = Id;
            this.FeatureId = FeatureId;
            this.FeatureName = FeatureName;
            this.FeatureType = FeatureType;
            this.FeatureDescriptors = FeatureDescriptors;
            this.InstanceDescriptors = InstanceDescriptors;
            this.DisplayObjectCount = DisplayObjectCount;
            this.InstanceName = InstanceName;
            this.FeatureDescription = FeatureDescription;
            this.InstanceDescription = InstanceDescription;
            this.Property = Property;
            this.Property2 = Property2;
            this.IconAssetId = IconAssetId;
            this.CoverAssetId = CoverAssetId;
            this.Action = Action;
            this.ActionUrl = ActionUrl;
            this.ContentOrder = ContentOrder;
            this.EntityType = EntityType;
            this.ActionBW = ActionBW;
            this.Latitude = latitude;
            this.Longitude = longitude;
            this.CreatedDate = createdDate;
            this.IsInstanceOwner = isInstanceOwner;
        }

        public Guid Id { get; init; }
        public Guid? FeatureId { get; init; }
        public string? FeatureName { get; init; }
        public string? FeatureType { get; init; }
        public string? FeatureDescriptors { get; init; }
        public string? InstanceDescriptors { get; init; }
        public string? InstanceName { get; init; }
        public string? FeatureDescription { get; init; }
        public string? InstanceDescription { get; init; }
        public bool? DisplayObjectCount { get; init; }
        public string? Property { get; init; }
        public string? Property2 { get; init; }
        public Guid? IconAssetId { get; init; }
        public Guid? CoverAssetId { get; init; }
        public string? Action { get; init; }
        public string? ActionUrl { get; init; }
        public int? ContentOrder { get; init; }
        public int EntityType { get; init; }
        public int ActionBW { get; init; }
        public int? TotalBlockItems { get; init; }
        public string? JoinCode { get; init; }
        public bool? CanExport { get; init; }
        public string? OrganizationName { get; init; }
        public int? Progress { get; set; }
        public string? Status { get; set; }
        public long? LastModifiedDate { get; set; }
        public bool? IsEnrolled { get; init; }
        public string? Slug { get; init; }
        public string? DefaultInstanceSlug { get; set; }
        public AchievementCompletion? AchievementCompletion { get; set; }
        public bool IsGraded { get; set; }
        public bool ContainsGrading { get; set; }
        public bool SameUrlNavigation { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public string? ExternalLogoUrl { get; set; }
        public string? ExternalCoverUrl { get; set; }
        public int? InstanceInterest { get; set; }
        public int? OrganizationInterest { get; set; }
        public long? CreatedDate { get; set; }
        public bool? IsInstanceOwner { get; set; }
    }
}
