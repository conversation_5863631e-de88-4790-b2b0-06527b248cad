(self.webpackChunkpublic=self.webpackChunkpublic||[]).push([[1206],{88655:xt=>{var nt=-1,j=1,Q=0;function St(f,T,g,L,x){if(f===T)return f?[[Q,f]]:[];if(null!=g){var E=function Xt(f,T,g){var L="number"==typeof g?{index:g,length:0}:g.oldRange,x="number"==typeof g?null:g.newRange,E=f.length,N=T.length;if(0===L.length&&(null===x||0===x.length)){var R=L.index,I=f.slice(0,R),C=f.slice(R),st=x?x.index:null,J=R+N-E;if(!(null!==st&&st!==J||J<0||J>N)){var rt=T.slice(0,J);if((at=T.slice(J))===C){var $=Math.min(R,J);if((_t=I.slice(0,$))===(Et=rt.slice(0,$)))return It(_t,I.slice($),rt.slice($),C)}}if(null===st||st===R){var ht=R,at=(rt=T.slice(0,ht),T.slice(ht));if(rt===I){var gt=Math.min(E-ht,N-ht);if((pt=C.slice(C.length-gt))===(yt=at.slice(at.length-gt)))return It(I,C.slice(0,C.length-gt),at.slice(0,at.length-gt),pt)}}}if(L.length>0&&x&&0===x.length){var _t=f.slice(0,L.index),pt=f.slice(L.index+L.length);if(!(N<($=_t.length)+(gt=pt.length))){var Et=T.slice(0,$),yt=T.slice(N-gt);if(_t===Et&&pt===yt)return It(_t,f.slice($,E-gt),T.slice($,N-gt),pt)}}return null}(f,T,g);if(E)return E}var N=P(f,T),R=f.substring(0,N);N=H(f=f.substring(N),T=T.substring(N));var I=f.substring(f.length-N),C=function ft(f,T){var g;if(!f)return[[j,T]];if(!T)return[[nt,f]];var L=f.length>T.length?f:T,x=f.length>T.length?T:f,E=L.indexOf(x);if(-1!==E)return g=[[j,L.substring(0,E)],[Q,x],[j,L.substring(E+x.length)]],f.length>T.length&&(g[0][0]=g[2][0]=nt),g;if(1===x.length)return[[nt,f],[j,T]];var N=function w(f,T){var g=f.length>T.length?f:T,L=f.length>T.length?T:f;if(g.length<4||2*L.length<g.length)return null;function x(at,$,_t){for(var ht,gt,pt,yt,Et=at.substring(_t,_t+Math.floor(at.length/4)),it=-1,dt="";-1!==(it=$.indexOf(Et,it+1));){var At=P(at.substring(_t),$.substring(it)),Rt=H(at.substring(0,_t),$.substring(0,it));dt.length<Rt+At&&(dt=$.substring(it-Rt,it)+$.substring(it,it+At),ht=at.substring(0,_t-Rt),gt=at.substring(_t+At),pt=$.substring(0,it-Rt),yt=$.substring(it+At))}return 2*dt.length>=at.length?[ht,gt,pt,yt,dt]:null}var R,I,C,st,J,E=x(g,L,Math.ceil(g.length/4)),N=x(g,L,Math.ceil(g.length/2));return E||N?(R=N?E&&E[4].length>N[4].length?E:N:E,f.length>T.length?(I=R[0],C=R[1],st=R[2],J=R[3]):(st=R[0],J=R[1],I=R[2],C=R[3]),[I,C,st,J,R[4]]):null}(f,T);if(N){var I=N[1],st=N[3],J=N[4],rt=St(N[0],N[2]),at=St(I,st);return rt.concat([[Q,J]],at)}return function X(f,T){for(var g=f.length,L=T.length,x=Math.ceil((g+L)/2),E=x,N=2*x,R=new Array(N),I=new Array(N),C=0;C<N;C++)R[C]=-1,I[C]=-1;R[E+1]=0,I[E+1]=0;for(var st=g-L,J=st%2!=0,rt=0,at=0,$=0,_t=0,Et=0;Et<x;Et++){for(var it=-Et+rt;it<=Et-at;it+=2){for(var dt=E+it,gt=(ht=it===-Et||it!==Et&&R[dt-1]<R[dt+1]?R[dt+1]:R[dt-1]+1)-it;ht<g&&gt<L&&f.charAt(ht)===T.charAt(gt);)ht++,gt++;if(R[dt]=ht,ht>g)at+=2;else if(gt>L)rt+=2;else if(J&&(pt=E+st-it)>=0&&pt<N&&-1!==I[pt]&&ht>=(yt=g-I[pt]))return U(f,T,ht,gt)}for(var At=-Et+$;At<=Et-_t;At+=2){for(var yt,pt=E+At,Rt=(yt=At===-Et||At!==Et&&I[pt-1]<I[pt+1]?I[pt+1]:I[pt-1]+1)-At;yt<g&&Rt<L&&f.charAt(g-yt-1)===T.charAt(L-Rt-1);)yt++,Rt++;if(I[pt]=yt,yt>g)_t+=2;else if(Rt>L)$+=2;else if(!J){var ht;if((dt=E+st-At)>=0&&dt<N&&-1!==R[dt])if(gt=E+(ht=R[dt])-dt,ht>=(yt=g-yt))return U(f,T,ht,gt)}}}return[[nt,f],[j,T]]}(f,T)}(f=f.substring(0,f.length-N),T=T.substring(0,T.length-N));return R&&C.unshift([Q,R]),I&&C.push([Q,I]),k(C,x),L&&function z(f){for(var T=!1,g=[],L=0,x=null,E=0,N=0,R=0,I=0,C=0;E<f.length;)f[E][0]==Q?(g[L++]=E,N=I,R=C,I=0,C=0,x=f[E][1]):(f[E][0]==j?I+=f[E][1].length:C+=f[E][1].length,x&&x.length<=Math.max(N,R)&&x.length<=Math.max(I,C)&&(f.splice(g[L-1],0,[nt,x]),f[g[L-1]+1][0]=j,L--,E=--L>0?g[L-1]:-1,N=0,R=0,I=0,C=0,x=null,T=!0)),E++;for(T&&k(f),function G(f){function T(at,$){if(!at||!$)return 6;var _t=at.charAt(at.length-1),Et=$.charAt(0),it=_t.match(p),dt=Et.match(p),ht=it&&_t.match(m),gt=dt&&Et.match(m),pt=ht&&_t.match(_),yt=gt&&Et.match(_),At=pt&&at.match(A),Rt=yt&&$.match(K);return At||Rt?5:pt||yt?4:it&&!ht&&gt?3:ht||gt?2:it||dt?1:0}for(var g=1;g<f.length-1;){if(f[g-1][0]==Q&&f[g+1][0]==Q){var L=f[g-1][1],x=f[g][1],E=f[g+1][1],N=H(L,x);if(N){var R=x.substring(x.length-N);L=L.substring(0,L.length-N),x=R+x.substring(0,x.length-N),E=R+E}for(var I=L,C=x,st=E,J=T(L,x)+T(x,E);x.charAt(0)===E.charAt(0);){L+=x.charAt(0),x=x.substring(1)+E.charAt(0),E=E.substring(1);var rt=T(L,x)+T(x,E);rt>=J&&(J=rt,I=L,C=x,st=E)}f[g-1][1]!=I&&(I?f[g-1][1]=I:(f.splice(g-1,1),g--),f[g][1]=C,st?f[g+1][1]=st:(f.splice(g+1,1),g--))}g++}}(f),E=1;E<f.length;){if(f[E-1][0]==nt&&f[E][0]==j){var st=f[E-1][1],J=f[E][1],rt=F(st,J),at=F(J,st);rt>=at?(rt>=st.length/2||rt>=J.length/2)&&(f.splice(E,0,[Q,J.substring(0,rt)]),f[E-1][1]=st.substring(0,st.length-rt),f[E+1][1]=J.substring(rt),E++):(at>=st.length/2||at>=J.length/2)&&(f.splice(E,0,[Q,st.substring(0,at)]),f[E-1][0]=j,f[E-1][1]=J.substring(0,J.length-at),f[E+1][0]=nt,f[E+1][1]=st.substring(at),E++),E++}E++}}(C),C}function U(f,T,g,L){var x=f.substring(0,g),E=T.substring(0,L),N=f.substring(g),R=T.substring(L),I=St(x,E),C=St(N,R);return I.concat(C)}function P(f,T){if(!f||!T||f.charAt(0)!==T.charAt(0))return 0;for(var g=0,L=Math.min(f.length,T.length),x=L,E=0;g<x;)f.substring(E,x)==T.substring(E,x)?E=g=x:L=x,x=Math.floor((L-g)/2+g);return tt(f.charCodeAt(x-1))&&x--,x}function F(f,T){var g=f.length,L=T.length;if(0==g||0==L)return 0;g>L?f=f.substring(g-L):g<L&&(T=T.substring(0,g));var x=Math.min(g,L);if(f==T)return x;for(var E=0,N=1;;){var R=f.substring(x-N),I=T.indexOf(R);if(-1==I)return E;N+=I,(0==I||f.substring(x-N)==T.substring(0,N))&&(E=N,N++)}}function H(f,T){if(!f||!T||f.slice(-1)!==T.slice(-1))return 0;for(var g=0,L=Math.min(f.length,T.length),x=L,E=0;g<x;)f.substring(f.length-x,f.length-E)==T.substring(T.length-x,T.length-E)?E=g=x:L=x,x=Math.floor((L-g)/2+g);return et(f.charCodeAt(f.length-x))&&x--,x}var p=/[^a-zA-Z0-9]/,m=/\s/,_=/[\r\n]/,A=/\n\r?\n$/,K=/^\r?\n\r?\n/;function k(f,T){f.push([Q,""]);for(var R,g=0,L=0,x=0,E="",N="";g<f.length;)if(g<f.length-1&&!f[g][1])f.splice(g,1);else switch(f[g][0]){case j:x++,N+=f[g][1],g++;break;case nt:L++,E+=f[g][1],g++;break;case Q:var I=g-x-L-1;if(T){if(I>=0&&vt(f[I][1])){var C=f[I][1].slice(-1);if(f[I][1]=f[I][1].slice(0,-1),E=C+E,N=C+N,!f[I][1]){f.splice(I,1),g--;var st=I-1;f[st]&&f[st][0]===j&&(x++,N=f[st][1]+N,st--),f[st]&&f[st][0]===nt&&(L++,E=f[st][1]+E,st--),I=st}}lt(f[g][1])&&(C=f[g][1].charAt(0),f[g][1]=f[g][1].slice(1),E+=C,N+=C)}if(g<f.length-1&&!f[g][1]){f.splice(g,1);break}if(E.length>0||N.length>0){E.length>0&&N.length>0&&(0!==(R=P(N,E))&&(I>=0?f[I][1]+=N.substring(0,R):(f.splice(0,0,[Q,N.substring(0,R)]),g++),N=N.substring(R),E=E.substring(R)),0!==(R=H(N,E))&&(f[g][1]=N.substring(N.length-R)+f[g][1],N=N.substring(0,N.length-R),E=E.substring(0,E.length-R)));var J=x+L;0===E.length&&0===N.length?(f.splice(g-J,J),g-=J):0===E.length?(f.splice(g-J,J,[j,N]),g=g-J+1):0===N.length?(f.splice(g-J,J,[nt,E]),g=g-J+1):(f.splice(g-J,J,[nt,E],[j,N]),g=g-J+2)}0!==g&&f[g-1][0]===Q?(f[g-1][1]+=f[g][1],f.splice(g,1)):g++,x=0,L=0,E="",N=""}""===f[f.length-1][1]&&f.pop();var rt=!1;for(g=1;g<f.length-1;)f[g-1][0]===Q&&f[g+1][0]===Q&&(f[g][1].substring(f[g][1].length-f[g-1][1].length)===f[g-1][1]?(f[g][1]=f[g-1][1]+f[g][1].substring(0,f[g][1].length-f[g-1][1].length),f[g+1][1]=f[g-1][1]+f[g+1][1],f.splice(g-1,1),rt=!0):f[g][1].substring(0,f[g+1][1].length)==f[g+1][1]&&(f[g-1][1]+=f[g+1][1],f[g][1]=f[g][1].substring(f[g+1][1].length)+f[g+1][1],f.splice(g+1,1),rt=!0)),g++;rt&&k(f,T)}function tt(f){return f>=55296&&f<=56319}function et(f){return f>=56320&&f<=57343}function lt(f){return et(f.charCodeAt(0))}function vt(f){return tt(f.charCodeAt(f.length-1))}function It(f,T,g,L){return vt(f)||lt(L)?null:function Ot(f){for(var T=[],g=0;g<f.length;g++)f[g][1].length>0&&T.push(f[g]);return T}([[Q,f],[nt,T],[j,g],[Q,L]])}function Ht(f,T,g,L){return St(f,T,g,L,!0)}Ht.INSERT=j,Ht.DELETE=nt,Ht.EQUAL=Q,xt.exports=Ht},58328:(xt,nt,j)=>{xt=j.nmd(xt);var St="__lodash_hash_undefined__",ft=9007199254740991,X="[object Arguments]",P="[object Boolean]",F="[object Date]",w="[object Function]",z="[object GeneratorFunction]",p="[object Map]",m="[object Number]",_="[object Object]",A="[object Promise]",K="[object RegExp]",G="[object Set]",k="[object String]",tt="[object Symbol]",et="[object WeakMap]",lt="[object ArrayBuffer]",vt="[object DataView]",Ot="[object Float32Array]",It="[object Float64Array]",Xt="[object Int8Array]",Ht="[object Int16Array]",f="[object Int32Array]",T="[object Uint8Array]",g="[object Uint8ClampedArray]",L="[object Uint16Array]",x="[object Uint32Array]",N=/\w*$/,R=/^\[object .+?Constructor\]$/,I=/^(?:0|[1-9]\d*)$/,C={};C[X]=C["[object Array]"]=C[lt]=C[vt]=C[P]=C[F]=C[Ot]=C[It]=C[Xt]=C[Ht]=C[f]=C[p]=C[m]=C[_]=C[K]=C[G]=C[k]=C[tt]=C[T]=C[g]=C[L]=C[x]=!0,C["[object Error]"]=C[w]=C[et]=!1;var st="object"==typeof global&&global&&global.Object===Object&&global,J="object"==typeof self&&self&&self.Object===Object&&self,rt=st||J||Function("return this")(),at=nt&&!nt.nodeType&&nt,$=at&&xt&&!xt.nodeType&&xt,_t=$&&$.exports===at;function Et(l,c){return l.set(c[0],c[1]),l}function it(l,c){return l.add(c),l}function gt(l,c,h,b){var V=-1,D=l?l.length:0;for(b&&D&&(h=l[++V]);++V<D;)h=c(h,l[V],V,l);return h}function At(l){var c=!1;if(null!=l&&"function"!=typeof l.toString)try{c=!!(l+"")}catch{}return c}function Rt(l){var c=-1,h=Array(l.size);return l.forEach(function(b,V){h[++c]=[V,b]}),h}function Kt(l,c){return function(h){return l(c(h))}}function Tn(l){var c=-1,h=Array(l.size);return l.forEach(function(b){h[++c]=b}),h}var l,Yn=Array.prototype,Qn=Function.prototype,je=Object.prototype,sn=rt["__core-js_shared__"],Pe=(l=/[^.]+$/.exec(sn&&sn.keys&&sn.keys.IE_PROTO||""))?"Symbol(src)_1."+l:"",Nn=Qn.toString,Gt=je.hasOwnProperty,Ne=je.toString,Jn=RegExp("^"+Nn.call(Gt).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),we=_t?rt.Buffer:void 0,Ue=rt.Symbol,rn=rt.Uint8Array,Vt=Kt(Object.getPrototypeOf,Object),se=Object.create,wn=je.propertyIsEnumerable,ts=Yn.splice,ln=Object.getOwnPropertySymbols,Fe=we?we.isBuffer:void 0,xn=Kt(Object.keys,Object),He=Qt(rt,"DataView"),xe=Qt(rt,"Map"),Yt=Qt(rt,"Promise"),$e=Qt(rt,"Set"),on=Qt(rt,"WeakMap"),Se=Qt(Object,"create"),an=jt(He),$t=jt(xe),cn=jt(Yt),ue=jt($e),un=jt(on),Wt=Ue?Ue.prototype:void 0,Sn=Wt?Wt.valueOf:void 0;function he(l){var c=-1,h=l?l.length:0;for(this.clear();++c<h;){var b=l[c];this.set(b[0],b[1])}}function kt(l){var c=-1,h=l?l.length:0;for(this.clear();++c<h;){var b=l[c];this.set(b[0],b[1])}}function Bt(l){var c=-1,h=l?l.length:0;for(this.clear();++c<h;){var b=l[c];this.set(b[0],b[1])}}function Pt(l){this.__data__=new kt(l)}function On(l,c,h){var b=l[c];(!Gt.call(l,c)||!Bn(b,h)||void 0===h&&!(c in l))&&(l[c]=h)}function Ge(l,c){for(var h=l.length;h--;)if(Bn(l[h][0],c))return h;return-1}function fn(l,c,h,b,V,D,ct){var ut;if(b&&(ut=D?b(l,V,D,ct):b(l)),void 0!==ut)return ut;if(!te(l))return l;var Lt=pn(l);if(Lt){if(ut=function Es(l){var c=l.length,h=l.constructor(c);return c&&"string"==typeof l[0]&&Gt.call(l,"index")&&(h.index=l.index,h.input=l.input),h}(l),!c)return function _s(l,c){var h=-1,b=l.length;for(c||(c=Array(b));++h<b;)c[h]=l[h];return c}(l,ut)}else{var mt=le(l),Mt=mt==w||mt==z;if(Dn(l))return function Ve(l,c){if(c)return l.slice();var h=new l.constructor(l.length);return l.copy(h),h}(l,c);if(mt==_||mt==X||Mt&&!D){if(At(l))return D?l:{};if(ut=function Jt(l){return"function"!=typeof l.constructor||Oe(l)?{}:function ms(l){return te(l)?se(l):{}}(Vt(l))}(Mt?{}:l),!c)return function As(l,c){return Le(l,ie(l),c)}(l,function re(l,c){return l&&Le(c,ke(c),l)}(ut,l))}else{if(!C[mt])return D?l:{};ut=function kn(l,c,h,b){var V=l.constructor;switch(c){case lt:return dn(l);case P:case F:return new V(+l);case vt:return function Zt(l,c){var h=c?dn(l.buffer):l.buffer;return new l.constructor(h,l.byteOffset,l.byteLength)}(l,b);case Ot:case It:case Xt:case Ht:case f:case T:case g:case L:case x:return function gn(l,c){var h=c?dn(l.buffer):l.buffer;return new l.constructor(h,l.byteOffset,l.length)}(l,b);case p:return function _e(l,c,h){return gt(c?h(Rt(l),!0):Rt(l),Et,new l.constructor)}(l,b,h);case m:case k:return new V(l);case K:return function In(l){var c=new l.constructor(l.source,N.exec(l));return c.lastIndex=l.lastIndex,c}(l);case G:return function We(l,c,h){return gt(c?h(Tn(l),!0):Tn(l),it,new l.constructor)}(l,b,h);case tt:return function vs(l){return Sn?Object(Sn.call(l)):{}}(l)}}(l,mt,fn,c)}}ct||(ct=new Pt);var Ut=ct.get(l);if(Ut)return Ut;if(ct.set(l,ut),!Lt)var Ct=h?function Us(l){return function bs(l,c,h){var b=c(l);return pn(l)?b:function ht(l,c){for(var h=-1,b=c.length,V=l.length;++h<b;)l[V+h]=c[h];return l}(b,h(l))}(l,ke,ie)}(l):ke(l);return function dt(l,c){for(var h=-1,b=l?l.length:0;++h<b&&!1!==c(l[h],h,l););}(Ct||l,function(Dt,Nt){Ct&&(Dt=l[Nt=Dt]),On(ut,Nt,fn(Dt,c,h,b,Nt,l,ct))}),ut}function dn(l){var c=new l.constructor(l.byteLength);return new rn(c).set(new rn(l)),c}function Le(l,c,h,b){h||(h={});for(var V=-1,D=c.length;++V<D;){var ct=c[V],ut=b?b(h[ct],l[ct],ct,h,l):void 0;On(h,ct,void 0===ut?l[ct]:ut)}return h}function Ce(l,c){var h=l.__data__;return function Rn(l){var c=typeof l;return"string"==c||"number"==c||"symbol"==c||"boolean"==c?"__proto__"!==l:null===l}(c)?h["string"==typeof c?"string":"hash"]:h.map}function Qt(l,c){var h=function yt(l,c){return l?.[c]}(l,c);return function qn(l){return!(!te(l)||function Ns(l){return!!Pe&&Pe in l}(l))&&(mn(l)||At(l)?Jn:R).test(jt(l))}(h)?h:void 0}he.prototype.clear=function es(){this.__data__=Se?Se(null):{}},he.prototype.delete=function ns(l){return this.has(l)&&delete this.__data__[l]},he.prototype.get=function ss(l){var c=this.__data__;if(Se){var h=c[l];return h===St?void 0:h}return Gt.call(c,l)?c[l]:void 0},he.prototype.has=function Ln(l){var c=this.__data__;return Se?void 0!==c[l]:Gt.call(c,l)},he.prototype.set=function hn(l,c){return this.__data__[l]=Se&&void 0===c?St:c,this},kt.prototype.clear=function rs(){this.__data__=[]},kt.prototype.delete=function is(l){var c=this.__data__,h=Ge(c,l);return!(h<0||(h==c.length-1?c.pop():ts.call(c,h,1),0))},kt.prototype.get=function ls(l){var c=this.__data__,h=Ge(c,l);return h<0?void 0:c[h][1]},kt.prototype.has=function os(l){return Ge(this.__data__,l)>-1},kt.prototype.set=function as(l,c){var h=this.__data__,b=Ge(h,l);return b<0?h.push([l,c]):h[b][1]=c,this},Bt.prototype.clear=function cs(){this.__data__={hash:new he,map:new(xe||kt),string:new he}},Bt.prototype.delete=function us(l){return Ce(this,l).delete(l)},Bt.prototype.get=function ye(l){return Ce(this,l).get(l)},Bt.prototype.has=function Cn(l){return Ce(this,l).has(l)},Bt.prototype.set=function hs(l,c){return Ce(this,l).set(l,c),this},Pt.prototype.clear=function fs(){this.__data__=new kt},Pt.prototype.delete=function ds(l){return this.__data__.delete(l)},Pt.prototype.get=function gs(l){return this.__data__.get(l)},Pt.prototype.has=function ze(l){return this.__data__.has(l)},Pt.prototype.set=function ps(l,c){var h=this.__data__;if(h instanceof kt){var b=h.__data__;if(!xe||b.length<199)return b.push([l,c]),this;h=this.__data__=new Bt(b)}return h.set(l,c),this};var ie=ln?Kt(ln,Object):function ws(){return[]},le=function ve(l){return Ne.call(l)};function Ts(l,c){return!!(c=c??ft)&&("number"==typeof l||I.test(l))&&l>-1&&l%1==0&&l<c}function Oe(l){var c=l&&l.constructor;return l===("function"==typeof c&&c.prototype||je)}function jt(l){if(null!=l){try{return Nn.call(l)}catch{}try{return l+""}catch{}}return""}function Bn(l,c){return l===c||l!=l&&c!=c}(He&&le(new He(new ArrayBuffer(1)))!=vt||xe&&le(new xe)!=p||Yt&&le(Yt.resolve())!=A||$e&&le(new $e)!=G||on&&le(new on)!=et)&&(le=function(l){var c=Ne.call(l),h=c==_?l.constructor:void 0,b=h?jt(h):void 0;if(b)switch(b){case an:return vt;case $t:return p;case cn:return A;case ue:return G;case un:return et}return c});var pn=Array.isArray;function Ie(l){return null!=l&&function jn(l){return"number"==typeof l&&l>-1&&l%1==0&&l<=ft}(l.length)&&!mn(l)}var Dn=Fe||function xs(){return!1};function mn(l){var c=te(l)?Ne.call(l):"";return c==w||c==z}function te(l){var c=typeof l;return!!l&&("object"==c||"function"==c)}function ke(l){return Ie(l)?function Ke(l,c){var h=pn(l)||function qe(l){return function Mn(l){return function Pn(l){return!!l&&"object"==typeof l}(l)&&Ie(l)}(l)&&Gt.call(l,"callee")&&(!wn.call(l,"callee")||Ne.call(l)==X)}(l)?function pt(l,c){for(var h=-1,b=Array(l);++h<l;)b[h]=c(h);return b}(l.length,String):[],b=h.length,V=!!b;for(var D in l)(c||Gt.call(l,D))&&(!V||"length"!=D&&!Ts(D,b))&&h.push(D);return h}(l):function ys(l){if(!Oe(l))return xn(l);var c=[];for(var h in Object(l))Gt.call(l,h)&&"constructor"!=h&&c.push(h);return c}(l)}xt.exports=function Ze(l){return fn(l,!0,!0)}},35667:(xt,nt,j)=>{xt=j.nmd(xt);var St="__lodash_hash_undefined__",ft=1,X=2,U=9007199254740991,P="[object Arguments]",F="[object Array]",H="[object AsyncFunction]",w="[object Boolean]",z="[object Date]",p="[object Error]",m="[object Function]",_="[object GeneratorFunction]",A="[object Map]",K="[object Number]",G="[object Null]",k="[object Object]",tt="[object Promise]",et="[object Proxy]",lt="[object RegExp]",vt="[object Set]",Ot="[object String]",Xt="[object Undefined]",Ht="[object WeakMap]",f="[object ArrayBuffer]",T="[object DataView]",rt=/^\[object .+?Constructor\]$/,at=/^(?:0|[1-9]\d*)$/,$={};$["[object Float32Array]"]=$["[object Float64Array]"]=$["[object Int8Array]"]=$["[object Int16Array]"]=$["[object Int32Array]"]=$["[object Uint8Array]"]=$["[object Uint8ClampedArray]"]=$["[object Uint16Array]"]=$["[object Uint32Array]"]=!0,$[P]=$[F]=$[f]=$[w]=$[T]=$[z]=$[p]=$[m]=$[A]=$[K]=$[k]=$[lt]=$[vt]=$[Ot]=$[Ht]=!1;var _t="object"==typeof global&&global&&global.Object===Object&&global,Et="object"==typeof self&&self&&self.Object===Object&&self,it=_t||Et||Function("return this")(),dt=nt&&!nt.nodeType&&nt,ht=dt&&xt&&!xt.nodeType&&xt,gt=ht&&ht.exports===dt,pt=gt&&_t.process,yt=function(){try{return pt&&pt.binding&&pt.binding("util")}catch{}}(),At=yt&&yt.isTypedArray;function Tn(l,c){for(var h=-1,b=null==l?0:l.length;++h<b;)if(c(l[h],h,l))return!0;return!1}function je(l,c){return l.has(c)}function Pe(l){var c=-1,h=Array(l.size);return l.forEach(function(b,V){h[++c]=[V,b]}),h}function Gt(l){var c=-1,h=Array(l.size);return l.forEach(function(b){h[++c]=b}),h}var l,Ne=Array.prototype,we=Object.prototype,Ue=it["__core-js_shared__"],rn=Function.prototype.toString,Vt=we.hasOwnProperty,se=(l=/[^.]+$/.exec(Ue&&Ue.keys&&Ue.keys.IE_PROTO||""))?"Symbol(src)_1."+l:"",wn=we.toString,ts=RegExp("^"+rn.call(Vt).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ln=gt?it.Buffer:void 0,Fe=it.Symbol,xn=it.Uint8Array,He=we.propertyIsEnumerable,xe=Ne.splice,Yt=Fe?Fe.toStringTag:void 0,$e=Object.getOwnPropertySymbols,on=ln?ln.isBuffer:void 0,Se=function Nn(l,c){return function(h){return l(c(h))}}(Object.keys,Object),an=ie(it,"DataView"),$t=ie(it,"Map"),cn=ie(it,"Promise"),ue=ie(it,"Set"),un=ie(it,"WeakMap"),Wt=ie(Object,"create"),Sn=jt(an),he=jt($t),es=jt(cn),ns=jt(ue),ss=jt(un),Ln=Fe?Fe.prototype:void 0,hn=Ln?Ln.valueOf:void 0;function kt(l){var c=-1,h=null==l?0:l.length;for(this.clear();++c<h;){var b=l[c];this.set(b[0],b[1])}}function Bt(l){var c=-1,h=null==l?0:l.length;for(this.clear();++c<h;){var b=l[c];this.set(b[0],b[1])}}function Pt(l){var c=-1,h=null==l?0:l.length;for(this.clear();++c<h;){var b=l[c];this.set(b[0],b[1])}}function Ke(l){var c=-1,h=null==l?0:l.length;for(this.__data__=new Pt;++c<h;)this.add(l[c])}function re(l){var c=this.__data__=new Bt(l);this.size=c.size}function Ve(l,c){for(var h=l.length;h--;)if(Ze(l[h][0],c))return h;return-1}function Zt(l){return null==l?void 0===l?Xt:G:Yt&&Yt in Object(l)?function le(l){var c=Vt.call(l,Yt),h=l[Yt];try{l[Yt]=void 0;var b=!0}catch{}var V=wn.call(l);return b&&(c?l[Yt]=h:delete l[Yt]),V}(l):function Oe(l){return wn.call(l)}(l)}function _e(l){return te(l)&&Zt(l)==P}function In(l,c,h,b,V){return l===c||(null==l||null==c||!te(l)&&!te(c)?l!=l&&c!=c:function We(l,c,h,b,V,D){var ct=qe(l),ut=qe(c),Lt=ct?F:Jt(l),mt=ut?F:Jt(c),Mt=(Lt=Lt==P?k:Lt)==k,Ut=(mt=mt==P?k:mt)==k,Ct=Lt==mt;if(Ct&&Ie(l)){if(!Ie(c))return!1;ct=!0,Mt=!1}if(Ct&&!Mt)return D||(D=new re),ct||Pn(l)?Le(l,c,h,b,V,D):function As(l,c,h,b,V,D,ct){switch(h){case T:if(l.byteLength!=c.byteLength||l.byteOffset!=c.byteOffset)return!1;l=l.buffer,c=c.buffer;case f:return!(l.byteLength!=c.byteLength||!D(new xn(l),new xn(c)));case w:case z:case K:return Ze(+l,+c);case p:return l.name==c.name&&l.message==c.message;case lt:case Ot:return l==c+"";case A:var ut=Pe;case vt:if(ut||(ut=Gt),l.size!=c.size&&!(b&ft))return!1;var mt=ct.get(l);if(mt)return mt==c;b|=X,ct.set(l,c);var Mt=Le(ut(l),ut(c),b,V,D,ct);return ct.delete(l),Mt;case"[object Symbol]":if(hn)return hn.call(l)==hn.call(c)}return!1}(l,c,Lt,h,b,V,D);if(!(h&ft)){var Dt=Mt&&Vt.call(l,"__wrapped__"),Nt=Ut&&Vt.call(c,"__wrapped__");if(Dt||Nt){var Ae=Dt?l.value():l,fe=Nt?c.value():c;return D||(D=new re),V(Ae,fe,h,b,D)}}return!!Ct&&(D||(D=new re),function Us(l,c,h,b,V,D){var ct=h&ft,ut=Ce(l),Lt=ut.length;if(Lt!=Ce(c).length&&!ct)return!1;for(var Ut=Lt;Ut--;){var Ct=ut[Ut];if(!(ct?Ct in c:Vt.call(c,Ct)))return!1}var Dt=D.get(l);if(Dt&&D.get(c))return Dt==c;var Nt=!0;D.set(l,c),D.set(c,l);for(var Ae=ct;++Ut<Lt;){var fe=l[Ct=ut[Ut]],Ee=c[Ct];if(b)var Fs=ct?b(Ee,fe,Ct,c,l,D):b(fe,Ee,Ct,l,c,D);if(!(void 0===Fs?fe===Ee||V(fe,Ee,h,b,D):Fs)){Nt=!1;break}Ae||(Ae="constructor"==Ct)}if(Nt&&!Ae){var Re=l.constructor,Un=c.constructor;Re!=Un&&"constructor"in l&&"constructor"in c&&!("function"==typeof Re&&Re instanceof Re&&"function"==typeof Un&&Un instanceof Un)&&(Nt=!1)}return D.delete(l),D.delete(c),Nt}(l,c,h,b,V,D))}(l,c,h,b,In,V))}function Le(l,c,h,b,V,D){var ct=h&ft,ut=l.length,Lt=c.length;if(ut!=Lt&&!(ct&&Lt>ut))return!1;var mt=D.get(l);if(mt&&D.get(c))return mt==c;var Mt=-1,Ut=!0,Ct=h&X?new Ke:void 0;for(D.set(l,c),D.set(c,l);++Mt<ut;){var Dt=l[Mt],Nt=c[Mt];if(b)var Ae=ct?b(Nt,Dt,Mt,c,l,D):b(Dt,Nt,Mt,l,c,D);if(void 0!==Ae){if(Ae)continue;Ut=!1;break}if(Ct){if(!Tn(c,function(fe,Ee){if(!je(Ct,Ee)&&(Dt===fe||V(Dt,fe,h,b,D)))return Ct.push(Ee)})){Ut=!1;break}}else if(Dt!==Nt&&!V(Dt,Nt,h,b,D)){Ut=!1;break}}return D.delete(l),D.delete(c),Ut}function Ce(l){return function dn(l,c,h){var b=c(l);return qe(l)?b:function Kt(l,c){for(var h=-1,b=c.length,V=l.length;++h<b;)l[V+h]=c[h];return l}(b,h(l))}(l,ke,Es)}function Qt(l,c){var h=l.__data__;return function Ts(l){var c=typeof l;return"string"==c||"number"==c||"symbol"==c||"boolean"==c?"__proto__"!==l:null===l}(c)?h["string"==typeof c?"string":"hash"]:h.map}function ie(l,c){var h=function sn(l,c){return l?.[c]}(l,c);return function vs(l){return!(!jn(l)||function Rn(l){return!!se&&se in l}(l))&&(Dn(l)?ts:rt).test(jt(l))}(h)?h:void 0}kt.prototype.clear=function rs(){this.__data__=Wt?Wt(null):{},this.size=0},kt.prototype.delete=function is(l){var c=this.has(l)&&delete this.__data__[l];return this.size-=c?1:0,c},kt.prototype.get=function ls(l){var c=this.__data__;if(Wt){var h=c[l];return h===St?void 0:h}return Vt.call(c,l)?c[l]:void 0},kt.prototype.has=function os(l){var c=this.__data__;return Wt?void 0!==c[l]:Vt.call(c,l)},kt.prototype.set=function as(l,c){var h=this.__data__;return this.size+=this.has(l)?0:1,h[l]=Wt&&void 0===c?St:c,this},Bt.prototype.clear=function cs(){this.__data__=[],this.size=0},Bt.prototype.delete=function us(l){var c=this.__data__,h=Ve(c,l);return!(h<0||(h==c.length-1?c.pop():xe.call(c,h,1),--this.size,0))},Bt.prototype.get=function ye(l){var c=this.__data__,h=Ve(c,l);return h<0?void 0:c[h][1]},Bt.prototype.has=function Cn(l){return Ve(this.__data__,l)>-1},Bt.prototype.set=function hs(l,c){var h=this.__data__,b=Ve(h,l);return b<0?(++this.size,h.push([l,c])):h[b][1]=c,this},Pt.prototype.clear=function fs(){this.size=0,this.__data__={hash:new kt,map:new($t||Bt),string:new kt}},Pt.prototype.delete=function ds(l){var c=Qt(this,l).delete(l);return this.size-=c?1:0,c},Pt.prototype.get=function gs(l){return Qt(this,l).get(l)},Pt.prototype.has=function ze(l){return Qt(this,l).has(l)},Pt.prototype.set=function ps(l,c){var h=Qt(this,l),b=h.size;return h.set(l,c),this.size+=h.size==b?0:1,this},Ke.prototype.add=Ke.prototype.push=function On(l){return this.__data__.set(l,St),this},Ke.prototype.has=function Ge(l){return this.__data__.has(l)},re.prototype.clear=function fn(){this.__data__=new Bt,this.size=0},re.prototype.delete=function ms(l){var c=this.__data__,h=c.delete(l);return this.size=c.size,h},re.prototype.get=function bs(l){return this.__data__.get(l)},re.prototype.has=function ve(l){return this.__data__.has(l)},re.prototype.set=function qn(l,c){var h=this.__data__;if(h instanceof Bt){var b=h.__data__;if(!$t||b.length<199)return b.push([l,c]),this.size=++h.size,this;h=this.__data__=new Pt(b)}return h.set(l,c),this.size=h.size,this};var Es=$e?function(l){return null==l?[]:(l=Object(l),function Rt(l,c){for(var h=-1,b=null==l?0:l.length,V=0,D=[];++h<b;){var ct=l[h];c(ct,h,l)&&(D[V++]=ct)}return D}($e(l),function(c){return He.call(l,c)}))}:function ws(){return[]},Jt=Zt;function kn(l,c){return!!(c=c??U)&&("number"==typeof l||at.test(l))&&l>-1&&l%1==0&&l<c}function jt(l){if(null!=l){try{return rn.call(l)}catch{}try{return l+""}catch{}}return""}function Ze(l,c){return l===c||l!=l&&c!=c}(an&&Jt(new an(new ArrayBuffer(1)))!=T||$t&&Jt(new $t)!=A||cn&&Jt(cn.resolve())!=tt||ue&&Jt(new ue)!=vt||un&&Jt(new un)!=Ht)&&(Jt=function(l){var c=Zt(l),h=c==k?l.constructor:void 0,b=h?jt(h):"";if(b)switch(b){case Sn:return T;case he:return A;case es:return tt;case ns:return vt;case ss:return Ht}return c});var Bn=_e(function(){return arguments}())?_e:function(l){return te(l)&&Vt.call(l,"callee")&&!He.call(l,"callee")},qe=Array.isArray,Ie=on||function xs(){return!1};function Dn(l){if(!jn(l))return!1;var c=Zt(l);return c==m||c==_||c==H||c==et}function mn(l){return"number"==typeof l&&l>-1&&l%1==0&&l<=U}function jn(l){var c=typeof l;return null!=l&&("object"==c||"function"==c)}function te(l){return null!=l&&"object"==typeof l}var Pn=At?function Qn(l){return function(c){return l(c)}}(At):function gn(l){return te(l)&&mn(l.length)&&!!$[Zt(l)]};function ke(l){return function pn(l){return null!=l&&mn(l.length)&&!Dn(l)}(l)?function ys(l,c){var h=qe(l),b=!h&&Bn(l),V=!h&&!b&&Ie(l),D=!h&&!b&&!V&&Pn(l),ct=h||b||V||D,ut=ct?function Yn(l,c){for(var h=-1,b=Array(l);++h<l;)b[h]=c(h);return b}(l.length,String):[],Lt=ut.length;for(var mt in l)(c||Vt.call(l,mt))&&(!ct||!("length"==mt||V&&("offset"==mt||"parent"==mt)||D&&("buffer"==mt||"byteLength"==mt||"byteOffset"==mt)||kn(mt,Lt)))&&ut.push(mt);return ut}(l):function _s(l){if(!function Ns(l){var c=l&&l.constructor;return l===("function"==typeof c&&c.prototype||we)}(l))return Se(l);var c=[];for(var h in Object(l))Vt.call(l,h)&&"constructor"!=h&&c.push(h);return c}(l)}xt.exports=function Mn(l,c){return In(l,c)}},51561:(xt,nt,j)=>{"use strict";Object.defineProperty(nt,"__esModule",{value:!0});const Q=j(58328),St=j(35667);var ft,X;(X=ft||(ft={})).compose=function U(w={},z={},p=!1){"object"!=typeof w&&(w={}),"object"!=typeof z&&(z={});let m=Q(z);p||(m=Object.keys(m).reduce((_,A)=>(null!=m[A]&&(_[A]=m[A]),_),{}));for(const _ in w)void 0!==w[_]&&void 0===z[_]&&(m[_]=w[_]);return Object.keys(m).length>0?m:void 0},X.diff=function P(w={},z={}){"object"!=typeof w&&(w={}),"object"!=typeof z&&(z={});const p=Object.keys(w).concat(Object.keys(z)).reduce((m,_)=>(St(w[_],z[_])||(m[_]=void 0===z[_]?null:z[_]),m),{});return Object.keys(p).length>0?p:void 0},X.invert=function F(w={},z={}){w=w||{};const p=Object.keys(z).reduce((m,_)=>(z[_]!==w[_]&&void 0!==w[_]&&(m[_]=z[_]),m),{});return Object.keys(w).reduce((m,_)=>(w[_]!==z[_]&&void 0===z[_]&&(m[_]=null),m),p)},X.transform=function H(w,z,p=!1){if("object"!=typeof w)return z;if("object"!=typeof z)return;if(!p)return z;const m=Object.keys(z).reduce((_,A)=>(void 0===w[A]&&(_[A]=z[A]),_),{});return Object.keys(m).length>0?m:void 0},nt.default=ft},42577:(xt,nt,j)=>{"use strict";Object.defineProperty(nt,"__esModule",{value:!0}),nt.AttributeMap=nt.OpIterator=nt.Op=void 0;const Q=j(88655),St=j(58328),ft=j(35667),X=j(51561);nt.AttributeMap=X.default;const U=j(28964);nt.Op=U.default;const P=j(6366);nt.OpIterator=P.default;const H=(z,p)=>{if("object"!=typeof z||null===z)throw new Error("cannot retain a "+typeof z);if("object"!=typeof p||null===p)throw new Error("cannot retain a "+typeof p);const m=Object.keys(z)[0];if(!m||m!==Object.keys(p)[0])throw new Error(`embed types not matched: ${m} != ${Object.keys(p)[0]}`);return[m,z[m],p[m]]};class w{constructor(p){this.ops=Array.isArray(p)?p:null!=p&&Array.isArray(p.ops)?p.ops:[]}static registerEmbed(p,m){this.handlers[p]=m}static unregisterEmbed(p){delete this.handlers[p]}static getHandler(p){const m=this.handlers[p];if(!m)throw new Error(`no handlers for embed type "${p}"`);return m}insert(p,m){const _={};return"string"==typeof p&&0===p.length?this:(_.insert=p,null!=m&&"object"==typeof m&&Object.keys(m).length>0&&(_.attributes=m),this.push(_))}delete(p){return p<=0?this:this.push({delete:p})}retain(p,m){if("number"==typeof p&&p<=0)return this;const _={retain:p};return null!=m&&"object"==typeof m&&Object.keys(m).length>0&&(_.attributes=m),this.push(_)}push(p){let m=this.ops.length,_=this.ops[m-1];if(p=St(p),"object"==typeof _){if("number"==typeof p.delete&&"number"==typeof _.delete)return this.ops[m-1]={delete:_.delete+p.delete},this;if("number"==typeof _.delete&&null!=p.insert&&(m-=1,_=this.ops[m-1],"object"!=typeof _))return this.ops.unshift(p),this;if(ft(p.attributes,_.attributes)){if("string"==typeof p.insert&&"string"==typeof _.insert)return this.ops[m-1]={insert:_.insert+p.insert},"object"==typeof p.attributes&&(this.ops[m-1].attributes=p.attributes),this;if("number"==typeof p.retain&&"number"==typeof _.retain)return this.ops[m-1]={retain:_.retain+p.retain},"object"==typeof p.attributes&&(this.ops[m-1].attributes=p.attributes),this}}return m===this.ops.length?this.ops.push(p):this.ops.splice(m,0,p),this}chop(){const p=this.ops[this.ops.length-1];return p&&"number"==typeof p.retain&&!p.attributes&&this.ops.pop(),this}filter(p){return this.ops.filter(p)}forEach(p){this.ops.forEach(p)}map(p){return this.ops.map(p)}partition(p){const m=[],_=[];return this.forEach(A=>{(p(A)?m:_).push(A)}),[m,_]}reduce(p,m){return this.ops.reduce(p,m)}changeLength(){return this.reduce((p,m)=>m.insert?p+U.default.length(m):m.delete?p-m.delete:p,0)}length(){return this.reduce((p,m)=>p+U.default.length(m),0)}slice(p=0,m=1/0){const _=[],A=new P.default(this.ops);let K=0;for(;K<m&&A.hasNext();){let G;K<p?G=A.next(p-K):(G=A.next(m-K),_.push(G)),K+=U.default.length(G)}return new w(_)}compose(p){const m=new P.default(this.ops),_=new P.default(p.ops),A=[],K=_.peek();if(null!=K&&"number"==typeof K.retain&&null==K.attributes){let k=K.retain;for(;"insert"===m.peekType()&&m.peekLength()<=k;)k-=m.peekLength(),A.push(m.next());K.retain-k>0&&_.next(K.retain-k)}const G=new w(A);for(;m.hasNext()||_.hasNext();)if("insert"===_.peekType())G.push(_.next());else if("delete"===m.peekType())G.push(m.next());else{const k=Math.min(m.peekLength(),_.peekLength()),tt=m.next(k),et=_.next(k);if(et.retain){const lt={};if("number"==typeof tt.retain)lt.retain="number"==typeof et.retain?k:et.retain;else if("number"==typeof et.retain)null==tt.retain?lt.insert=tt.insert:lt.retain=tt.retain;else{const Ot=null==tt.retain?"insert":"retain",[It,Xt,Ht]=H(tt[Ot],et.retain),f=w.getHandler(It);lt[Ot]={[It]:f.compose(Xt,Ht,"retain"===Ot)}}const vt=X.default.compose(tt.attributes,et.attributes,"number"==typeof tt.retain);if(vt&&(lt.attributes=vt),G.push(lt),!_.hasNext()&&ft(G.ops[G.ops.length-1],lt)){const Ot=new w(m.rest());return G.concat(Ot).chop()}}else"number"==typeof et.delete&&("number"==typeof tt.retain||"object"==typeof tt.retain&&null!==tt.retain)&&G.push(et)}return G.chop()}concat(p){const m=new w(this.ops.slice());return p.ops.length>0&&(m.push(p.ops[0]),m.ops=m.ops.concat(p.ops.slice(1))),m}diff(p,m){if(this.ops===p.ops)return new w;const _=[this,p].map(tt=>tt.map(et=>{if(null!=et.insert)return"string"==typeof et.insert?et.insert:"\0";throw new Error("diff() called "+(tt===p?"on":"with")+" non-document")}).join("")),A=new w,K=Q(_[0],_[1],m,!0),G=new P.default(this.ops),k=new P.default(p.ops);return K.forEach(tt=>{let et=tt[1].length;for(;et>0;){let lt=0;switch(tt[0]){case Q.INSERT:lt=Math.min(k.peekLength(),et),A.push(k.next(lt));break;case Q.DELETE:lt=Math.min(et,G.peekLength()),G.next(lt),A.delete(lt);break;case Q.EQUAL:lt=Math.min(G.peekLength(),k.peekLength(),et);const vt=G.next(lt),Ot=k.next(lt);ft(vt.insert,Ot.insert)?A.retain(lt,X.default.diff(vt.attributes,Ot.attributes)):A.push(Ot).delete(lt)}et-=lt}}),A.chop()}eachLine(p,m="\n"){const _=new P.default(this.ops);let A=new w,K=0;for(;_.hasNext();){if("insert"!==_.peekType())return;const G=_.peek(),k=U.default.length(G)-_.peekLength(),tt="string"==typeof G.insert?G.insert.indexOf(m,k)-k:-1;if(tt<0)A.push(_.next());else if(tt>0)A.push(_.next(tt));else{if(!1===p(A,_.next(1).attributes||{},K))return;K+=1,A=new w}}A.length()>0&&p(A,{},K)}invert(p){const m=new w;return this.reduce((_,A)=>{if(A.insert)m.delete(U.default.length(A));else{if("number"==typeof A.retain&&null==A.attributes)return m.retain(A.retain),_+A.retain;if(A.delete||"number"==typeof A.retain){const K=A.delete||A.retain;return p.slice(_,_+K).forEach(k=>{A.delete?m.push(k):A.retain&&A.attributes&&m.retain(U.default.length(k),X.default.invert(A.attributes,k.attributes))}),_+K}if("object"==typeof A.retain&&null!==A.retain){const K=p.slice(_,_+1),G=new P.default(K.ops).next(),[k,tt,et]=H(A.retain,G.insert),lt=w.getHandler(k);return m.retain({[k]:lt.invert(tt,et)},X.default.invert(A.attributes,G.attributes)),_+1}}return _},0),m.chop()}transform(p,m=!1){if(m=!!m,"number"==typeof p)return this.transformPosition(p,m);const _=p,A=new P.default(this.ops),K=new P.default(_.ops),G=new w;for(;A.hasNext()||K.hasNext();)if("insert"!==A.peekType()||!m&&"insert"===K.peekType())if("insert"===K.peekType())G.push(K.next());else{const k=Math.min(A.peekLength(),K.peekLength()),tt=A.next(k),et=K.next(k);if(tt.delete)continue;if(et.delete)G.push(et);else{const lt=tt.retain,vt=et.retain;let Ot="object"==typeof vt&&null!==vt?vt:k;if("object"==typeof lt&&null!==lt&&"object"==typeof vt&&null!==vt){const It=Object.keys(lt)[0];if(It===Object.keys(vt)[0]){const Xt=w.getHandler(It);Xt&&(Ot={[It]:Xt.transform(lt[It],vt[It],m)})}}G.retain(Ot,X.default.transform(tt.attributes,et.attributes,m))}}else G.retain(U.default.length(A.next()));return G.chop()}transformPosition(p,m=!1){m=!!m;const _=new P.default(this.ops);let A=0;for(;_.hasNext()&&A<=p;){const K=_.peekLength(),G=_.peekType();_.next(),"delete"!==G?("insert"===G&&(A<p||!m)&&(p+=K),A+=K):p-=Math.min(K,p-A)}return p}}w.Op=U.default,w.OpIterator=P.default,w.AttributeMap=X.default,w.handlers={},nt.default=w,xt.exports=w,xt.exports.default=w},28964:(xt,nt)=>{"use strict";var j;Object.defineProperty(nt,"__esModule",{value:!0}),(j||(j={})).length=function St(ft){return"number"==typeof ft.delete?ft.delete:"number"==typeof ft.retain?ft.retain:"object"==typeof ft.retain&&null!==ft.retain?1:"string"==typeof ft.insert?ft.insert.length:1},nt.default=j},6366:(xt,nt,j)=>{"use strict";Object.defineProperty(nt,"__esModule",{value:!0});const Q=j(28964);nt.default=class St{constructor(X){this.ops=X,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(X){X||(X=1/0);const U=this.ops[this.index];if(U){const P=this.offset,F=Q.default.length(U);if(X>=F-P?(X=F-P,this.index+=1,this.offset=0):this.offset+=X,"number"==typeof U.delete)return{delete:X};{const H={};return U.attributes&&(H.attributes=U.attributes),"number"==typeof U.retain?H.retain=X:"object"==typeof U.retain&&null!==U.retain?H.retain=U.retain:H.insert="string"==typeof U.insert?U.insert.substr(P,X):U.insert,H}}return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?Q.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const X=this.ops[this.index];return X?"number"==typeof X.delete?"delete":"number"==typeof X.retain||"object"==typeof X.retain&&null!==X.retain?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);{const X=this.offset,U=this.index,P=this.next(),F=this.ops.slice(this.index);return this.offset=X,this.index=U,[P].concat(F)}}return[]}}},49280:xt=>{"use strict";var nt=Object.prototype.hasOwnProperty,j="~";function Q(){}function St(P,F,H){this.fn=P,this.context=F,this.once=H||!1}function ft(P,F,H,w,z){if("function"!=typeof H)throw new TypeError("The listener must be a function");var p=new St(H,w||P,z),m=j?j+F:F;return P._events[m]?P._events[m].fn?P._events[m]=[P._events[m],p]:P._events[m].push(p):(P._events[m]=p,P._eventsCount++),P}function X(P,F){0==--P._eventsCount?P._events=new Q:delete P._events[F]}function U(){this._events=new Q,this._eventsCount=0}Object.create&&(Q.prototype=Object.create(null),(new Q).__proto__||(j=!1)),U.prototype.eventNames=function(){var H,w,F=[];if(0===this._eventsCount)return F;for(w in H=this._events)nt.call(H,w)&&F.push(j?w.slice(1):w);return Object.getOwnPropertySymbols?F.concat(Object.getOwnPropertySymbols(H)):F},U.prototype.listeners=function(F){var w=this._events[j?j+F:F];if(!w)return[];if(w.fn)return[w.fn];for(var z=0,p=w.length,m=new Array(p);z<p;z++)m[z]=w[z].fn;return m},U.prototype.listenerCount=function(F){var w=this._events[j?j+F:F];return w?w.fn?1:w.length:0},U.prototype.emit=function(F,H,w,z,p,m){var _=j?j+F:F;if(!this._events[_])return!1;var G,k,A=this._events[_],K=arguments.length;if(A.fn){switch(A.once&&this.removeListener(F,A.fn,void 0,!0),K){case 1:return A.fn.call(A.context),!0;case 2:return A.fn.call(A.context,H),!0;case 3:return A.fn.call(A.context,H,w),!0;case 4:return A.fn.call(A.context,H,w,z),!0;case 5:return A.fn.call(A.context,H,w,z,p),!0;case 6:return A.fn.call(A.context,H,w,z,p,m),!0}for(k=1,G=new Array(K-1);k<K;k++)G[k-1]=arguments[k];A.fn.apply(A.context,G)}else{var et,tt=A.length;for(k=0;k<tt;k++)switch(A[k].once&&this.removeListener(F,A[k].fn,void 0,!0),K){case 1:A[k].fn.call(A[k].context);break;case 2:A[k].fn.call(A[k].context,H);break;case 3:A[k].fn.call(A[k].context,H,w);break;case 4:A[k].fn.call(A[k].context,H,w,z);break;default:if(!G)for(et=1,G=new Array(K-1);et<K;et++)G[et-1]=arguments[et];A[k].fn.apply(A[k].context,G)}}return!0},U.prototype.on=function(F,H,w){return ft(this,F,H,w,!1)},U.prototype.once=function(F,H,w){return ft(this,F,H,w,!0)},U.prototype.removeListener=function(F,H,w,z){var p=j?j+F:F;if(!this._events[p])return this;if(!H)return X(this,p),this;var m=this._events[p];if(m.fn)m.fn===H&&(!z||m.once)&&(!w||m.context===w)&&X(this,p);else{for(var _=0,A=[],K=m.length;_<K;_++)(m[_].fn!==H||z&&!m[_].once||w&&m[_].context!==w)&&A.push(m[_]);A.length?this._events[p]=1===A.length?A[0]:A:X(this,p)}return this},U.prototype.removeAllListeners=function(F){var H;return F?this._events[H=j?j+F:F]&&X(this,H):(this._events=new Q,this._eventsCount=0),this},U.prototype.off=U.prototype.removeListener,U.prototype.addListener=U.prototype.on,U.prefixed=j,U.EventEmitter=U,xt.exports=U},11206:(xt,nt,j)=>{"use strict";j.r(nt),j.d(nt,{Module:()=>me,Parchment:()=>Q,Range:()=>Je,default:()=>Ed});var Q={};j.r(Q),j.d(Q,{Attributor:()=>Te,AttributorStore:()=>Hs,BlockBlot:()=>qs,ClassAttributor:()=>de,ContainerBlot:()=>$s,EmbedBlot:()=>ee,InlineBlot:()=>dr,LeafBlot:()=>Ft,ParentBlot:()=>ge,Registry:()=>$n,Scope:()=>M,ScrollBlot:()=>pr,StyleAttributor:()=>Ye,TextBlot:()=>zs});const U=function X(s,e){return s===e||s!=s&&e!=e},F=function P(s,e){for(var t=s.length;t--;)if(U(s[t][0],e))return t;return-1};var w=Array.prototype.splice;function tt(s){var e=-1,t=null==s?0:s.length;for(this.clear();++e<t;){var n=s[e];this.set(n[0],n[1])}}tt.prototype.clear=function St(){this.__data__=[],this.size=0},tt.prototype.delete=function z(s){var e=this.__data__,t=F(e,s);return!(t<0||(t==e.length-1?e.pop():w.call(e,t,1),--this.size,0))},tt.prototype.get=function m(s){var e=this.__data__,t=F(e,s);return t<0?void 0:e[t][1]},tt.prototype.has=function A(s){return F(this.__data__,s)>-1},tt.prototype.set=function G(s,e){var t=this.__data__,n=F(t,s);return n<0?(++this.size,t.push([s,e])):t[n][1]=e,this};const et=tt,L="object"==typeof global&&global&&global.Object===Object&&global;var x="object"==typeof self&&self&&self.Object===Object&&self;const N=L||x||Function("return this")(),I=N.Symbol;var C=Object.prototype,st=C.hasOwnProperty,J=C.toString,rt=I?I.toStringTag:void 0;var Et=Object.prototype.toString;var pt=I?I.toStringTag:void 0;const At=function yt(s){return null==s?void 0===s?"[object Undefined]":"[object Null]":pt&&pt in Object(s)?function at(s){var e=st.call(s,rt),t=s[rt];try{s[rt]=void 0;var n=!0}catch{}var r=J.call(s);return n&&(e?s[rt]=t:delete s[rt]),r}(s):function it(s){return Et.call(s)}(s)},Kt=function Rt(s){var e=typeof s;return null!=s&&("object"==e||"function"==e)},Pe=function sn(s){if(!Kt(s))return!1;var e=At(s);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e};var s,Nn=N["__core-js_shared__"],Ne=(s=/[^.]+$/.exec(Nn&&Nn.keys&&Nn.keys.IE_PROTO||""))?"Symbol(src)_1."+s:"";var rn=Function.prototype.toString;const se=function Vt(s){if(null!=s){try{return rn.call(s)}catch{}try{return s+""}catch{}}return""};var ts=/^\[object .+?Constructor\]$/,xe=RegExp("^"+Function.prototype.toString.call(Object.prototype.hasOwnProperty).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const $e=function Yt(s){return!(!Kt(s)||function Jn(s){return!!Ne&&Ne in s}(s))&&(Pe(s)?xe:ts).test(se(s))},$t=function an(s,e){var t=function on(s,e){return s?.[e]}(s,e);return $e(t)?t:void 0},ue=$t(N,"Map"),Wt=$t(Object,"create");var hn=Object.prototype.hasOwnProperty;var ls=Object.prototype.hasOwnProperty;function ye(s){var e=-1,t=null==s?0:s.length;for(this.clear();++e<t;){var n=s[e];this.set(n[0],n[1])}}ye.prototype.clear=function Sn(){this.__data__=Wt?Wt(null):{},this.size=0},ye.prototype.delete=function es(s){var e=this.has(s)&&delete this.__data__[s];return this.size-=e?1:0,e},ye.prototype.get=function kt(s){var e=this.__data__;if(Wt){var t=e[s];return"__lodash_hash_undefined__"===t?void 0:t}return hn.call(e,s)?e[s]:void 0},ye.prototype.has=function os(s){var e=this.__data__;return Wt?void 0!==e[s]:ls.call(e,s)},ye.prototype.set=function cs(s,e){var t=this.__data__;return this.size+=this.has(s)?0:1,t[s]=Wt&&void 0===e?"__lodash_hash_undefined__":e,this};const Cn=ye,ze=function gs(s,e){var t=s.__data__;return function fs(s){var e=typeof s;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==s:null===s}(e)?t["string"==typeof e?"string":"hash"]:t.map};function ve(s){var e=-1,t=null==s?0:s.length;for(this.clear();++e<t;){var n=s[e];this.set(n[0],n[1])}}ve.prototype.clear=function hs(){this.size=0,this.__data__={hash:new Cn,map:new(ue||et),string:new Cn}},ve.prototype.delete=function ps(s){var e=ze(this,s).delete(s);return this.size-=e?1:0,e},ve.prototype.get=function On(s){return ze(this,s).get(s)},ve.prototype.has=function re(s){return ze(this,s).has(s)},ve.prototype.set=function ms(s,e){var t=ze(this,s),n=t.size;return t.set(s,e),this.size+=t.size==n?0:1,this};const qn=ve;function Zt(s){var e=this.__data__=new et(s);this.size=e.size}Zt.prototype.clear=function lt(){this.__data__=new et,this.size=0},Zt.prototype.delete=function Ot(s){var e=this.__data__,t=e.delete(s);return this.size=e.size,t},Zt.prototype.get=function Xt(s){return this.__data__.get(s)},Zt.prototype.has=function f(s){return this.__data__.has(s)},Zt.prototype.set=function Ve(s,e){var t=this.__data__;if(t instanceof et){var n=t.__data__;if(!ue||n.length<199)return n.push([s,e]),this.size=++t.size,this;t=this.__data__=new qn(n)}return t.set(s,e),this.size=t.size,this};const _e=Zt;var In=function(){try{var s=$t(Object,"defineProperty");return s({},"",{}),s}catch{}}();const We=In,gn=function vs(s,e,t){"__proto__"==e&&We?We(s,e,{configurable:!0,enumerable:!0,value:t,writable:!0}):s[e]=t},Le=function _s(s,e,t){(void 0!==t&&!U(s[e],t)||void 0===t&&!(e in s))&&gn(s,e,t)};var Ce=function As(s){return function(e,t,n){for(var r=-1,i=Object(e),o=n(e),a=o.length;a--;){var u=o[s?a:++r];if(!1===t(i[u],u,i))break}return e}}();const Qt=Ce;var ie="object"==typeof exports&&exports&&!exports.nodeType&&exports,le=ie&&"object"==typeof module&&module&&!module.nodeType&&module,Jt=le&&le.exports===ie?N.Buffer:void 0,kn=Jt?Jt.allocUnsafe:void 0;const Rn=function Ts(s,e){if(e)return s.slice();var t=s.length,n=kn?kn(t):new s.constructor(t);return s.copy(n),n},Oe=N.Uint8Array,Ze=function jt(s){var e=new s.constructor(s.byteLength);return new Oe(e).set(new Oe(s)),e},qe=function Bn(s,e){var t=e?Ze(s.buffer):s.buffer;return new s.constructor(t,s.byteOffset,s.length)},Ie=function pn(s,e){var t=-1,n=s.length;for(e||(e=Array(n));++t<n;)e[t]=s[t];return e};var Mn=Object.create,Dn=function(){function s(){}return function(e){if(!Kt(e))return{};if(Mn)return Mn(e);s.prototype=e;var t=new s;return s.prototype=void 0,t}}();const mn=Dn,te=function jn(s,e){return function(t){return s(e(t))}},ke=te(Object.getPrototypeOf,Object);var ws=Object.prototype;const l=function xs(s){var e=s&&s.constructor;return s===("function"==typeof e&&e.prototype||ws)},h=function c(s){return"function"!=typeof s.constructor||l(s)?{}:mn(ke(s))},V=function b(s){return null!=s&&"object"==typeof s},ut=function ct(s){return V(s)&&"[object Arguments]"==At(s)};var Lt=Object.prototype,mt=Lt.hasOwnProperty,Mt=Lt.propertyIsEnumerable,Ut=ut(function(){return arguments}())?ut:function(s){return V(s)&&mt.call(s,"callee")&&!Mt.call(s,"callee")};const Ct=Ut,Nt=Array.isArray,Ee=function fe(s){return"number"==typeof s&&s>-1&&s%1==0&&s<=9007199254740991},Re=function Fs(s){return null!=s&&Ee(s.length)&&!Pe(s)};var ti="object"==typeof exports&&exports&&!exports.nodeType&&exports,ei=ti&&"object"==typeof module&&module&&!module.nodeType&&module,ni=ei&&ei.exports===ti?N.Buffer:void 0;const Ss=(ni?ni.isBuffer:void 0)||function Rl(){return!1};var si=Function.prototype.toString,Hl=Object.prototype.hasOwnProperty,$l=si.call(Object);var wt={};wt["[object Float32Array]"]=wt["[object Float64Array]"]=wt["[object Int8Array]"]=wt["[object Int16Array]"]=wt["[object Int32Array]"]=wt["[object Uint8Array]"]=wt["[object Uint8ClampedArray]"]=wt["[object Uint16Array]"]=wt["[object Uint32Array]"]=!0,wt["[object Arguments]"]=wt["[object Array]"]=wt["[object ArrayBuffer]"]=wt["[object Boolean]"]=wt["[object DataView]"]=wt["[object Date]"]=wt["[object Error]"]=wt["[object Function]"]=wt["[object Map]"]=wt["[object Number]"]=wt["[object Object]"]=wt["[object RegExp]"]=wt["[object Set]"]=wt["[object String]"]=wt["[object WeakMap]"]=!1;const ar=function vo(s){return function(e){return s(e)}};var ri="object"==typeof exports&&exports&&!exports.nodeType&&exports,Ls=ri&&"object"==typeof module&&module&&!module.nodeType&&module,cr=Ls&&Ls.exports===ri&&L.process;const Fn=function(){try{return Ls&&Ls.require&&Ls.require("util").types||cr&&cr.binding&&cr.binding("util")}catch{}}();var ii=Fn&&Fn.isTypedArray;const ur=ii?ar(ii):function bo(s){return V(s)&&Ee(s.length)&&!!wt[At(s)]},hr=function To(s,e){if(("constructor"!==e||"function"!=typeof s[e])&&"__proto__"!=e)return s[e]};var wo=Object.prototype.hasOwnProperty;const li=function xo(s,e,t){var n=s[e];(!wo.call(s,e)||!U(n,t)||void 0===t&&!(e in s))&&gn(s,e,t)},Cs=function So(s,e,t,n){var r=!t;t||(t={});for(var i=-1,o=e.length;++i<o;){var a=e[i],u=n?n(t[a],s[a],a,t,s):void 0;void 0===u&&(u=s[a]),r?gn(t,a,u):li(t,a,u)}return t};var qo=/^(?:0|[1-9]\d*)$/;const oi=function Io(s,e){var t=typeof s;return!!(e=e??9007199254740991)&&("number"==t||"symbol"!=t&&qo.test(s))&&s>-1&&s%1==0&&s<e};var Ro=Object.prototype.hasOwnProperty;const ai=function Bo(s,e){var t=Nt(s),n=!t&&Ct(s),r=!t&&!n&&Ss(s),i=!t&&!n&&!r&&ur(s),o=t||n||r||i,a=o?function Lo(s,e){for(var t=-1,n=Array(s);++t<s;)n[t]=e(t);return n}(s.length,String):[],u=a.length;for(var d in s)(e||Ro.call(s,d))&&(!o||!("length"==d||r&&("offset"==d||"parent"==d)||i&&("buffer"==d||"byteLength"==d||"byteOffset"==d)||oi(d,u)))&&a.push(d);return a};var Po=Object.prototype.hasOwnProperty;const Fo=function Uo(s){if(!Kt(s))return function Mo(s){var e=[];if(null!=s)for(var t in Object(s))e.push(t);return e}(s);var e=l(s),t=[];for(var n in s)"constructor"==n&&(e||!Po.call(s,n))||t.push(n);return t},Os=function Ho(s){return Re(s)?ai(s,!0):Fo(s)},Go=function Ko(s,e,t,n,r,i,o){var a=hr(s,t),u=hr(e,t),d=o.get(u);if(d)Le(s,t,d);else{var v=i?i(a,u,t+"",s,e,o):void 0,S=void 0===v;if(S){var q=Nt(u),W=!q&&Ss(u),Y=!q&&!W&&ur(u);v=u,q||W||Y?Nt(a)?v=a:function Un(s){return V(s)&&Re(s)}(a)?v=Ie(a):W?(S=!1,v=Rn(u,!0)):Y?(S=!1,v=qe(u,!0)):v=[]:function zl(s){if(!V(s)||"[object Object]"!=At(s))return!1;var e=ke(s);if(null===e)return!0;var t=Hl.call(e,"constructor")&&e.constructor;return"function"==typeof t&&t instanceof t&&si.call(t)==$l}(u)||Ct(u)?(v=a,Ct(a)?v=function $o(s){return Cs(s,Os(s))}(a):(!Kt(a)||Pe(a))&&(v=h(u))):S=!1}S&&(o.set(u,v),r(v,u,n,i,o),o.delete(u)),Le(s,t,v)}},Vo=function ci(s,e,t,n,r){s!==e&&Qt(e,function(i,o){if(r||(r=new _e),Kt(i))Go(s,e,o,t,ci,n,r);else{var a=n?n(hr(s,o),i,o+"",s,e,r):void 0;void 0===a&&(a=i),Le(s,o,a)}},Os)},ui=function Wo(s){return s};var hi=Math.max;const ta=function Jo(s){return function(){return s}};var ea=We?function(s,e){return We(s,"toString",{configurable:!0,enumerable:!1,value:ta(e),writable:!0})}:ui,ia=Date.now,oa=function la(s){var e=0,t=0;return function(){var n=ia(),r=16-(n-t);if(t=n,r>0){if(++e>=800)return arguments[0]}else e=0;return s.apply(void 0,arguments)}}(ea);const aa=oa,ua=function ca(s,e){return aa(function Yo(s,e,t){return e=hi(void 0===e?s.length-1:e,0),function(){for(var n=arguments,r=-1,i=hi(n.length-e,0),o=Array(i);++r<i;)o[r]=n[e+r];r=-1;for(var a=Array(e+1);++r<e;)a[r]=n[r];return a[e]=t(o),function Zo(s,e,t){switch(t.length){case 0:return s.call(e);case 1:return s.call(e,t[0]);case 2:return s.call(e,t[0],t[1]);case 3:return s.call(e,t[0],t[1],t[2])}return s.apply(e,t)}(s,this,a)}}(s,e,ui),s+"")};var ga=function da(s){return ua(function(e,t){var n=-1,r=t.length,i=r>1?t[r-1]:void 0,o=r>2?t[2]:void 0;for(i=s.length>3&&"function"==typeof i?(r--,i):void 0,o&&function ha(s,e,t){if(!Kt(t))return!1;var n=typeof e;return!!("number"==n?Re(t)&&oi(e,t.length):"string"==n&&e in t)&&U(t[e],s)}(t[0],t[1],o)&&(i=r<3?void 0:i,r=1),e=Object(e);++n<r;){var a=t[n];a&&s(e,a,n,i)}return e})}(function(s,e,t){Vo(s,e,t)});const Xe=ga;var M=(s=>(s[s.TYPE=3]="TYPE",s[s.LEVEL=12]="LEVEL",s[s.ATTRIBUTE=13]="ATTRIBUTE",s[s.BLOT=14]="BLOT",s[s.INLINE=7]="INLINE",s[s.BLOCK=11]="BLOCK",s[s.BLOCK_BLOT=10]="BLOCK_BLOT",s[s.INLINE_BLOT=6]="INLINE_BLOT",s[s.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",s[s.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",s[s.ANY=15]="ANY",s))(M||{});class Te{constructor(e,t,n={}){this.attrName=e,this.keyName=t,this.scope=null!=n.scope?n.scope&M.LEVEL|M.TYPE&M.ATTRIBUTE:M.ATTRIBUTE,null!=n.whitelist&&(this.whitelist=n.whitelist)}static keys(e){return Array.from(e.attributes).map(t=>t.name)}add(e,t){return!!this.canAdd(e,t)&&(e.setAttribute(this.keyName,t),!0)}canAdd(e,t){return null==this.whitelist||("string"==typeof t?this.whitelist.indexOf(t.replace(/["']/g,""))>-1:this.whitelist.indexOf(t)>-1)}remove(e){e.removeAttribute(this.keyName)}value(e){const t=e.getAttribute(this.keyName);return this.canAdd(e,t)&&t?t:""}}class Hn extends Error{constructor(e){super(e="[Parchment] "+e),this.message=e,this.name=this.constructor.name}}let $n=(()=>{let s=class Qr{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,n=!1){if(null==t)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(n){let r=null;try{r=t.parentNode}catch{return null}return this.find(r,n)}return null}create(t,n,r){const i=this.query(n);if(null==i)throw new Hn(`Unable to create ${n} blot`);const o=i,a=n instanceof Node||n.nodeType===Node.TEXT_NODE?n:o.create(r),u=new o(t,a,r);return Qr.blots.set(u.domNode,u),u}find(t,n=!1){return Qr.find(t,n)}query(t,n=M.ANY){let r;return"string"==typeof t?r=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?r=this.types.text:"number"==typeof t?t&M.LEVEL&M.BLOCK?r=this.types.block:t&M.LEVEL&M.INLINE&&(r=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(i=>(r=this.classes[i],!!r)),r=r||this.tags[t.tagName]),null==r?null:"scope"in r&&n&M.LEVEL&r.scope&&n&M.TYPE&r.scope?r:null}register(...t){return t.map(n=>{const r="blotName"in n,i="attrName"in n;if(!r&&!i)throw new Hn("Invalid definition");if(r&&"abstract"===n.blotName)throw new Hn("Cannot register abstract class");return this.types[r?n.blotName:i?n.attrName:void 0]=n,i?"string"==typeof n.keyName&&(this.attributes[n.keyName]=n):r&&(n.className&&(this.classes[n.className]=n),n.tagName&&(n.tagName=Array.isArray(n.tagName)?n.tagName.map(a=>a.toUpperCase()):n.tagName.toUpperCase(),(Array.isArray(n.tagName)?n.tagName:[n.tagName]).forEach(a=>{(null==this.tags[a]||null==n.className)&&(this.tags[a]=n)}))),n})}};return s.blots=new WeakMap,s})();function fi(s,e){return(s.getAttribute("class")||"").split(/\s+/).filter(t=>0===t.indexOf(`${e}-`))}const de=class pa extends Te{static keys(e){return(e.getAttribute("class")||"").split(/\s+/).map(t=>t.split("-").slice(0,-1).join("-"))}add(e,t){return!!this.canAdd(e,t)&&(this.remove(e),e.classList.add(`${this.keyName}-${t}`),!0)}remove(e){fi(e,this.keyName).forEach(t=>{e.classList.remove(t)}),0===e.classList.length&&e.removeAttribute("class")}value(e){const t=(fi(e,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(e,t)?t:""}};function fr(s){const e=s.split("-"),t=e.slice(1).map(n=>n[0].toUpperCase()+n.slice(1)).join("");return e[0]+t}const Ye=class ma extends Te{static keys(e){return(e.getAttribute("style")||"").split(";").map(t=>t.split(":")[0].trim())}add(e,t){return!!this.canAdd(e,t)&&(e.style[fr(this.keyName)]=t,!0)}remove(e){e.style[fr(this.keyName)]="",e.getAttribute("style")||e.removeAttribute("style")}value(e){const t=e.style[fr(this.keyName)];return this.canAdd(e,t)?t:""}},Hs=class ba{constructor(e){this.attributes={},this.domNode=e,this.build()}attribute(e,t){t?e.add(this.domNode,t)&&(null!=e.value(this.domNode)?this.attributes[e.attrName]=e:delete this.attributes[e.attrName]):(e.remove(this.domNode),delete this.attributes[e.attrName])}build(){this.attributes={};const e=$n.find(this.domNode);if(null==e)return;const t=Te.keys(this.domNode),n=de.keys(this.domNode),r=Ye.keys(this.domNode);t.concat(n).concat(r).forEach(i=>{const o=e.scroll.query(i,M.ATTRIBUTE);o instanceof Te&&(this.attributes[o.attrName]=o)})}copy(e){Object.keys(this.attributes).forEach(t=>{const n=this.attributes[t].value(this.domNode);e.format(t,n)})}move(e){this.copy(e),Object.keys(this.attributes).forEach(t=>{this.attributes[t].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((e,t)=>(e[t]=this.attributes[t].value(this.domNode),e),{})}},di=class{constructor(e,t){this.scroll=e,this.domNode=t,$n.blots.set(t,this),this.prev=null,this.next=null}static create(e){if(null==this.tagName)throw new Hn("Blot definition missing tagName");let t,n;return Array.isArray(this.tagName)?("string"==typeof e?(n=e.toUpperCase(),parseInt(n,10).toString()===n&&(n=parseInt(n,10))):"number"==typeof e&&(n=e),t="number"==typeof n?document.createElement(this.tagName[n-1]):n&&this.tagName.indexOf(n)>-1?document.createElement(n):document.createElement(this.tagName[0])):t=document.createElement(this.tagName),this.className&&t.classList.add(this.className),t}get statics(){return this.constructor}attach(){}clone(){const e=this.domNode.cloneNode(!1);return this.scroll.create(e)}detach(){null!=this.parent&&this.parent.removeChild(this),$n.blots.delete(this.domNode)}deleteAt(e,t){this.isolate(e,t).remove()}formatAt(e,t,n,r){const i=this.isolate(e,t);if(null!=this.scroll.query(n,M.BLOT)&&r)i.wrap(n,r);else if(null!=this.scroll.query(n,M.ATTRIBUTE)){const o=this.scroll.create(this.statics.scope);i.wrap(o),o.format(n,r)}}insertAt(e,t,n){const r=null==n?this.scroll.create("text",t):this.scroll.create(t,n),i=this.split(e);this.parent.insertBefore(r,i||void 0)}isolate(e,t){const n=this.split(e);if(null==n)throw new Error("Attempt to isolate at end");return n.split(t),n}length(){return 1}offset(e=this.parent){return null==this.parent||this===e?0:this.parent.children.offset(this)+this.parent.offset(e)}optimize(e){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(e,t){const n="string"==typeof e?this.scroll.create(e,t):e;return null!=this.parent&&(this.parent.insertBefore(n,this.next||void 0),this.remove()),n}split(e,t){return 0===e?this:this.next}update(e,t){}wrap(e,t){const n="string"==typeof e?this.scroll.create(e,t):e;if(null!=this.parent&&this.parent.insertBefore(n,this.next||void 0),"function"!=typeof n.appendChild)throw new Hn(`Cannot wrap ${e}`);return n.appendChild(this),n}};di.blotName="abstract";let gi=di;const pi=class extends gi{static value(e){return!0}index(e,t){return this.domNode===e||this.domNode.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(t,1):-1}position(e,t){let n=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return e>0&&(n+=1),[this.parent.domNode,n]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};pi.scope=M.INLINE_BLOT;const Ft=pi;class ya{constructor(){this.head=null,this.tail=null,this.length=0}append(...e){if(this.insertBefore(e[0],null),e.length>1){const t=e.slice(1);this.append(...t)}}at(e){const t=this.iterator();let n=t();for(;n&&e>0;)e-=1,n=t();return n}contains(e){const t=this.iterator();let n=t();for(;n;){if(n===e)return!0;n=t()}return!1}indexOf(e){const t=this.iterator();let n=t(),r=0;for(;n;){if(n===e)return r;r+=1,n=t()}return-1}insertBefore(e,t){null!=e&&(this.remove(e),e.next=t,null!=t?(e.prev=t.prev,null!=t.prev&&(t.prev.next=e),t.prev=e,t===this.head&&(this.head=e)):null!=this.tail?(this.tail.next=e,e.prev=this.tail,this.tail=e):(e.prev=null,this.head=this.tail=e),this.length+=1)}offset(e){let t=0,n=this.head;for(;null!=n;){if(n===e)return t;t+=n.length(),n=n.next}return-1}remove(e){this.contains(e)&&(null!=e.prev&&(e.prev.next=e.next),null!=e.next&&(e.next.prev=e.prev),e===this.head&&(this.head=e.next),e===this.tail&&(this.tail=e.prev),this.length-=1)}iterator(e=this.head){return()=>{const t=e;return null!=e&&(e=e.next),t}}find(e,t=!1){const n=this.iterator();let r=n();for(;r;){const i=r.length();if(e<i||t&&e===i&&(null==r.next||0!==r.next.length()))return[r,e];e-=i,r=n()}return[null,0]}forEach(e){const t=this.iterator();let n=t();for(;n;)e(n),n=t()}forEachAt(e,t,n){if(t<=0)return;const[r,i]=this.find(e);let o=e-i;const a=this.iterator(r);let u=a();for(;u&&o<e+t;){const d=u.length();e>o?n(u,e-o,Math.min(t,o+d-e)):n(u,0,Math.min(d,e+t-o)),o+=d,u=a()}}map(e){return this.reduce((t,n)=>(t.push(e(n)),t),[])}reduce(e,t){const n=this.iterator();let r=n();for(;r;)t=e(t,r),r=n();return t}}function mi(s,e){const t=e.find(s);if(t)return t;try{return e.create(s)}catch{const n=e.create(M.INLINE);return Array.from(s.childNodes).forEach(r=>{n.domNode.appendChild(r)}),s.parentNode&&s.parentNode.replaceChild(n.domNode,s),n.attach(),n}}const ge=(()=>{let s=class nn extends gi{constructor(t,n){super(t,n),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(t){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=t,nn.uiClass&&this.uiNode.classList.add(nn.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new ya,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{const n=mi(t,this.scroll);this.insertBefore(n,this.children.head||void 0)}catch(n){if(n instanceof Hn)return;throw n}})}deleteAt(t,n){if(0===t&&n===this.length())return this.remove();this.children.forEachAt(t,n,(r,i,o)=>{r.deleteAt(i,o)})}descendant(t,n=0){const[r,i]=this.children.find(n);return null==t.blotName&&t(r)||null!=t.blotName&&r instanceof t?[r,i]:r instanceof nn?r.descendant(t,i):[null,-1]}descendants(t,n=0,r=Number.MAX_VALUE){let i=[],o=r;return this.children.forEachAt(n,r,(a,u,d)=>{(null==t.blotName&&t(a)||null!=t.blotName&&a instanceof t)&&i.push(a),a instanceof nn&&(i=i.concat(a.descendants(t,u,o))),o-=d}),i}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let t=!1;this.children.forEach(n=>{t||this.statics.allowedChildren.some(r=>n instanceof r)||(n.statics.scope===M.BLOCK_BLOT?(null!=n.next&&this.splitAfter(n),null!=n.prev&&this.splitAfter(n.prev),n.parent.unwrap(),t=!0):n instanceof nn?n.unwrap():n.remove())})}formatAt(t,n,r,i){this.children.forEachAt(t,n,(o,a,u)=>{o.formatAt(a,u,r,i)})}insertAt(t,n,r){const[i,o]=this.children.find(t);if(i)i.insertAt(o,n,r);else{const a=null==r?this.scroll.create("text",n):this.scroll.create(n,r);this.appendChild(a)}}insertBefore(t,n){null!=t.parent&&t.parent.children.remove(t);let r=null;this.children.insertBefore(t,n||null),t.parent=this,null!=n&&(r=n.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==r)&&this.domNode.insertBefore(t.domNode,r),t.attach()}length(){return this.children.reduce((t,n)=>t+n.length(),0)}moveChildren(t,n){this.children.forEach(r=>{t.insertBefore(r,n)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){const n=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(n)}else this.remove()}path(t,n=!1){const[r,i]=this.children.find(t,n),o=[[this,t]];return r instanceof nn?o.concat(r.path(i,n)):(null!=r&&o.push([r,i]),o)}removeChild(t){this.children.remove(t)}replaceWith(t,n){const r="string"==typeof t?this.scroll.create(t,n):t;return r instanceof nn&&this.moveChildren(r),super.replaceWith(r)}split(t,n=!1){if(!n){if(0===t)return this;if(t===this.length())return this.next}const r=this.clone();return this.parent&&this.parent.insertBefore(r,this.next||void 0),this.children.forEachAt(t,this.length(),(i,o,a)=>{const u=i.split(o,n);null!=u&&r.appendChild(u)}),r}splitAfter(t){const n=this.clone();for(;null!=t.next;)n.appendChild(t.next);return this.parent&&this.parent.insertBefore(n,this.next||void 0),n}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,n){const r=[],i=[];t.forEach(o=>{o.target===this.domNode&&"childList"===o.type&&(r.push(...o.addedNodes),i.push(...o.removedNodes))}),i.forEach(o=>{if(null!=o.parentNode&&"IFRAME"!==o.tagName&&document.body.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const a=this.scroll.find(o);null!=a&&(null==a.domNode.parentNode||a.domNode.parentNode===this.domNode)&&a.detach()}),r.filter(o=>o.parentNode===this.domNode&&o!==this.uiNode).sort((o,a)=>o===a?0:o.compareDocumentPosition(a)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(o=>{let a=null;null!=o.nextSibling&&(a=this.scroll.find(o.nextSibling));const u=mi(o,this.scroll);(u.next!==a||null==u.next)&&(null!=u.parent&&u.parent.removeChild(this),this.insertBefore(u,a||void 0))}),this.enforceAllowedChildren()}};return s.uiClass="",s})(),zn=class Xn extends ge{static create(e){return super.create(e)}static formats(e,t){const n=t.query(Xn.blotName);if(null==n||e.tagName!==n.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(e,t){super(e,t),this.attributes=new Hs(this.domNode)}format(e,t){if(e!==this.statics.blotName||t){const n=this.scroll.query(e,M.INLINE);if(null==n)return;n instanceof Te?this.attributes.attribute(n,t):t&&(e!==this.statics.blotName||this.formats()[e]!==t)&&this.replaceWith(e,t)}else this.children.forEach(n=>{n instanceof Xn||(n=n.wrap(Xn.blotName,!0)),this.attributes.copy(n)}),this.unwrap()}formats(){const e=this.attributes.values(),t=this.statics.formats(this.domNode,this.scroll);return null!=t&&(e[this.statics.blotName]=t),e}formatAt(e,t,n,r){null!=this.formats()[n]||this.scroll.query(n,M.ATTRIBUTE)?this.isolate(e,t).format(n,r):super.formatAt(e,t,n,r)}optimize(e){super.optimize(e);const t=this.formats();if(0===Object.keys(t).length)return this.unwrap();const n=this.next;n instanceof Xn&&n.prev===this&&function va(s,e){if(Object.keys(s).length!==Object.keys(e).length)return!1;for(const t in s)if(s[t]!==e[t])return!1;return!0}(t,n.formats())&&(n.moveChildren(this),n.remove())}replaceWith(e,t){const n=super.replaceWith(e,t);return this.attributes.copy(n),n}update(e,t){super.update(e,t),e.some(n=>n.target===this.domNode&&"attributes"===n.type)&&this.attributes.build()}wrap(e,t){const n=super.wrap(e,t);return n instanceof Xn&&this.attributes.move(n),n}};zn.allowedChildren=[zn,Ft],zn.blotName="inline",zn.scope=M.INLINE_BLOT,zn.tagName="SPAN";const dr=zn,Kn=class Jr extends ge{static create(e){return super.create(e)}static formats(e,t){const n=t.query(Jr.blotName);if(null==n||e.tagName!==n.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(e,t){super(e,t),this.attributes=new Hs(this.domNode)}format(e,t){const n=this.scroll.query(e,M.BLOCK);null!=n&&(n instanceof Te?this.attributes.attribute(n,t):e!==this.statics.blotName||t?t&&(e!==this.statics.blotName||this.formats()[e]!==t)&&this.replaceWith(e,t):this.replaceWith(Jr.blotName))}formats(){const e=this.attributes.values(),t=this.statics.formats(this.domNode,this.scroll);return null!=t&&(e[this.statics.blotName]=t),e}formatAt(e,t,n,r){null!=this.scroll.query(n,M.BLOCK)?this.format(n,r):super.formatAt(e,t,n,r)}insertAt(e,t,n){if(null==n||null!=this.scroll.query(t,M.INLINE))super.insertAt(e,t,n);else{const r=this.split(e);if(null==r)throw new Error("Attempt to insertAt after block boundaries");{const i=this.scroll.create(t,n);r.parent.insertBefore(i,r)}}}replaceWith(e,t){const n=super.replaceWith(e,t);return this.attributes.copy(n),n}update(e,t){super.update(e,t),e.some(n=>n.target===this.domNode&&"attributes"===n.type)&&this.attributes.build()}};Kn.blotName="block",Kn.scope=M.BLOCK_BLOT,Kn.tagName="P",Kn.allowedChildren=[dr,Kn,Ft];const qs=Kn,gr=class extends ge{checkMerge(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName}deleteAt(e,t){super.deleteAt(e,t),this.enforceAllowedChildren()}formatAt(e,t,n,r){super.formatAt(e,t,n,r),this.enforceAllowedChildren()}insertAt(e,t,n){super.insertAt(e,t,n),this.enforceAllowedChildren()}optimize(e){super.optimize(e),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};gr.blotName="container",gr.scope=M.BLOCK_BLOT;const $s=gr,ee=class _a extends Ft{static formats(e,t){}format(e,t){super.formatAt(0,this.length(),e,t)}formatAt(e,t,n,r){0===e&&t===this.length()?this.format(n,r):super.formatAt(e,t,n,r)}formats(){return this.statics.formats(this.domNode,this.scroll)}},Aa={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Gn=class extends ge{constructor(e,t){super(null,t),this.registry=e,this.scroll=this,this.build(),this.observer=new MutationObserver(n=>{this.update(n)}),this.observer.observe(this.domNode,Aa),this.attach()}create(e,t){return this.registry.create(this,e,t)}find(e,t=!1){const n=this.registry.find(e,t);return n?n.scroll===this?n:t?this.find(n.scroll.domNode.parentNode,!0):null:null}query(e,t=M.ANY){return this.registry.query(e,t)}register(...e){return this.registry.register(...e)}build(){null!=this.scroll&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(e,t){this.update(),0===e&&t===this.length()?this.children.forEach(n=>{n.remove()}):super.deleteAt(e,t)}formatAt(e,t,n,r){this.update(),super.formatAt(e,t,n,r)}insertAt(e,t,n){this.update(),super.insertAt(e,t,n)}optimize(e=[],t={}){super.optimize(t);const n=t.mutationsMap||new WeakMap;let r=Array.from(this.observer.takeRecords());for(;r.length>0;)e.push(r.pop());const i=(u,d=!0)=>{null==u||u===this||null!=u.domNode.parentNode&&(n.has(u.domNode)||n.set(u.domNode,[]),d&&i(u.parent))},o=u=>{n.has(u.domNode)&&(u instanceof ge&&u.children.forEach(o),n.delete(u.domNode),u.optimize(t))};let a=e;for(let u=0;a.length>0;u+=1){if(u>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(a.forEach(d=>{const v=this.find(d.target,!0);null!=v&&(v.domNode===d.target&&("childList"===d.type?(i(this.find(d.previousSibling,!1)),Array.from(d.addedNodes).forEach(S=>{const q=this.find(S,!1);i(q,!1),q instanceof ge&&q.children.forEach(W=>{i(W,!1)})})):"attributes"===d.type&&i(v.prev)),i(v))}),this.children.forEach(o),a=Array.from(this.observer.takeRecords()),r=a.slice();r.length>0;)e.push(r.pop())}}update(e,t={}){e=e||this.observer.takeRecords();const n=new WeakMap;e.map(r=>{const i=this.find(r.target,!0);return null==i?null:n.has(i.domNode)?(n.get(i.domNode).push(r),null):(n.set(i.domNode,[r]),i)}).forEach(r=>{null!=r&&r!==this&&n.has(r.domNode)&&r.update(n.get(r.domNode)||[],t)}),t.mutationsMap=n,n.has(this.domNode)&&super.update(n.get(this.domNode),t),this.optimize(e,t)}};Gn.blotName="scroll",Gn.defaultChild=qs,Gn.allowedChildren=[qs,$s],Gn.scope=M.BLOCK_BLOT,Gn.tagName="DIV";const pr=Gn,mr=class Il extends Ft{static create(e){return document.createTextNode(e)}static value(e){return e.data}constructor(e,t){super(e,t),this.text=this.statics.value(this.domNode)}deleteAt(e,t){this.domNode.data=this.text=this.text.slice(0,e)+this.text.slice(e+t)}index(e,t){return this.domNode===e?t:-1}insertAt(e,t,n){null==n?(this.text=this.text.slice(0,e)+t+this.text.slice(e),this.domNode.data=this.text):super.insertAt(e,t,n)}length(){return this.text.length}optimize(e){super.optimize(e),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof Il&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(e,t=!1){return[this.domNode,e]}split(e,t=!1){if(!t){if(0===e)return this;if(e===this.length())return this.next}const n=this.scroll.create(this.domNode.splitText(e));return this.parent.insertBefore(n,this.next||void 0),this.text=this.statics.value(this.domNode),n}update(e,t){e.some(n=>"characterData"===n.type&&n.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};mr.blotName="text",mr.scope=M.INLINE_BLOT;const zs=mr;var O=j(42577);const xa=te(Object.keys,Object);var La=Object.prototype.hasOwnProperty;const br=function qa(s){return Re(s)?ai(s):function Ca(s){if(!l(s))return xa(s);var e=[];for(var t in Object(s))La.call(s,t)&&"constructor"!=t&&e.push(t);return e}(s)},bi=function ja(){return[]};var Ua=Object.prototype.propertyIsEnumerable,yi=Object.getOwnPropertySymbols,Fa=yi?function(s){return null==s?[]:(s=Object(s),function Ma(s,e){for(var t=-1,n=null==s?0:s.length,r=0,i=[];++t<n;){var o=s[t];e(o,t,s)&&(i[r++]=o)}return i}(yi(s),function(e){return Ua.call(s,e)}))}:bi;const yr=Fa,vi=function za(s,e){for(var t=-1,n=e.length,r=s.length;++t<n;)s[r+t]=e[t];return s};var Ga=Object.getOwnPropertySymbols?function(s){for(var e=[];s;)vi(e,yr(s)),s=ke(s);return e}:bi;const _i=Ga,Ai=function Za(s,e,t){var n=e(s);return Nt(s)?n:vi(n,t(s))},vr=function Xa(s){return Ai(s,br,yr)},Qa=function Ya(s){return Ai(s,Os,_i)},_r=$t(N,"DataView"),Ar=$t(N,"Promise"),Er=$t(N,"Set"),Tr=$t(N,"WeakMap");var Ei="[object Map]",Ti="[object Promise]",Ni="[object Set]",wi="[object WeakMap]",xi="[object DataView]",rc=se(_r),ic=se(ue),lc=se(Ar),oc=se(Er),ac=se(Tr),bn=At;(_r&&bn(new _r(new ArrayBuffer(1)))!=xi||ue&&bn(new ue)!=Ei||Ar&&bn(Ar.resolve())!=Ti||Er&&bn(new Er)!=Ni||Tr&&bn(new Tr)!=wi)&&(bn=function(s){var e=At(s),t="[object Object]"==e?s.constructor:void 0,n=t?se(t):"";if(n)switch(n){case rc:return xi;case ic:return Ei;case lc:return Ti;case oc:return Ni;case ac:return wi}return e});const Is=bn;var uc=Object.prototype.hasOwnProperty;var pc=/\w*$/;var Si=I?I.prototype:void 0,Li=Si?Si.valueOf:void 0;const Uc=function Pc(s,e,t){var n=s.constructor;switch(e){case"[object ArrayBuffer]":return Ze(s);case"[object Boolean]":case"[object Date]":return new n(+s);case"[object DataView]":return function dc(s,e){var t=e?Ze(s.buffer):s.buffer;return new s.constructor(t,s.byteOffset,s.byteLength)}(s,t);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return qe(s,t);case"[object Map]":case"[object Set]":return new n;case"[object Number]":case"[object String]":return new n(s);case"[object RegExp]":return function mc(s){var e=new s.constructor(s.source,pc.exec(s));return e.lastIndex=s.lastIndex,e}(s);case"[object Symbol]":return function yc(s){return Li?Object(Li.call(s)):{}}(s)}};var Ci=Fn&&Fn.isMap;const Kc=Ci?ar(Ci):function Hc(s){return V(s)&&"[object Map]"==Is(s)};var Oi=Fn&&Fn.isSet;const Xc=Oi?ar(Oi):function Vc(s){return V(s)&&"[object Set]"==Is(s)};var qi="[object Arguments]",Ii="[object Function]",ki="[object Object]",Tt={};Tt[qi]=Tt["[object Array]"]=Tt["[object ArrayBuffer]"]=Tt["[object DataView]"]=Tt["[object Boolean]"]=Tt["[object Date]"]=Tt["[object Float32Array]"]=Tt["[object Float64Array]"]=Tt["[object Int8Array]"]=Tt["[object Int16Array]"]=Tt["[object Int32Array]"]=Tt["[object Map]"]=Tt["[object Number]"]=Tt[ki]=Tt["[object RegExp]"]=Tt["[object Set]"]=Tt["[object String]"]=Tt["[object Symbol]"]=Tt["[object Uint8Array]"]=Tt["[object Uint8ClampedArray]"]=Tt["[object Uint16Array]"]=Tt["[object Uint32Array]"]=!0,Tt["[object Error]"]=Tt[Ii]=Tt["[object WeakMap]"]=!1;const Tu=function Ks(s,e,t,n,r,i){var o,a=1&e,u=2&e,d=4&e;if(t&&(o=r?t(s,n,r,i):t(s)),void 0!==o)return o;if(!Kt(s))return s;var v=Nt(s);if(v){if(o=function hc(s){var e=s.length,t=new s.constructor(e);return e&&"string"==typeof s[0]&&uc.call(s,"index")&&(t.index=s.index,t.input=s.input),t}(s),!a)return Ie(s,o)}else{var S=Is(s),q=S==Ii||"[object GeneratorFunction]"==S;if(Ss(s))return Rn(s,a);if(S==ki||S==qi||q&&!r){if(o=u||q?{}:h(s),!a)return u?function Va(s,e){return Cs(s,_i(s),e)}(s,function Ra(s,e){return s&&Cs(e,Os(e),s)}(o,s)):function Ha(s,e){return Cs(s,yr(s),e)}(s,function Ia(s,e){return s&&Cs(e,br(e),s)}(o,s))}else{if(!Tt[S])return r?s:{};o=Uc(s,S,a)}}i||(i=new _e);var W=i.get(s);if(W)return W;i.set(s,o),Xc(s)?s.forEach(function(bt){o.add(Ks(bt,e,t,bt,s,i))}):Kc(s)&&s.forEach(function(bt,Z){o.set(Z,Ks(bt,e,t,Z,s,i))});var ot=v?void 0:(d?u?Qa:vr:u?Os:br)(s);return function Ta(s,e){for(var t=-1,n=null==s?0:s.length;++t<n&&!1!==e(s[t],t,s););}(ot||s,function(bt,Z){ot&&(bt=s[Z=bt]),li(o,Z,Ks(bt,e,t,Z,s,i))}),o},Vn=function xu(s){return Tu(s,5)};function Gs(s){var e=-1,t=null==s?0:s.length;for(this.__data__=new qn;++e<t;)this.add(s[e])}Gs.prototype.add=Gs.prototype.push=function Lu(s){return this.__data__.set(s,"__lodash_hash_undefined__"),this},Gs.prototype.has=function Ou(s){return this.__data__.has(s)};const Iu=Gs,Ru=function ku(s,e){for(var t=-1,n=null==s?0:s.length;++t<n;)if(e(s[t],t,s))return!0;return!1},Mu=function Bu(s,e){return s.has(e)},Ri=function Pu(s,e,t,n,r,i){var o=1&t,a=s.length,u=e.length;if(a!=u&&!(o&&u>a))return!1;var d=i.get(s),v=i.get(e);if(d&&v)return d==e&&v==s;var S=-1,q=!0,W=2&t?new Iu:void 0;for(i.set(s,e),i.set(e,s);++S<a;){var Y=s[S],ot=e[S];if(n)var bt=o?n(ot,Y,S,e,s,i):n(Y,ot,S,s,e,i);if(void 0!==bt){if(bt)continue;q=!1;break}if(W){if(!Ru(e,function(Z,qt){if(!Mu(W,qt)&&(Y===Z||r(Y,Z,t,n,i)))return W.push(qt)})){q=!1;break}}else if(Y!==ot&&!r(Y,ot,t,n,i)){q=!1;break}}return i.delete(s),i.delete(e),q},Fu=function Uu(s){var e=-1,t=Array(s.size);return s.forEach(function(n,r){t[++e]=[r,n]}),t},$u=function Hu(s){var e=-1,t=Array(s.size);return s.forEach(function(n){t[++e]=n}),t};var Bi=I?I.prototype:void 0,Nr=Bi?Bi.valueOf:void 0;var oh=Object.prototype.hasOwnProperty;var Mi="[object Arguments]",Di="[object Array]",Vs="[object Object]",ji=Object.prototype.hasOwnProperty;const dh=function fh(s,e,t,n,r,i){var o=Nt(s),a=Nt(e),u=o?Di:Is(s),d=a?Di:Is(e),v=(u=u==Mi?Vs:u)==Vs,S=(d=d==Mi?Vs:d)==Vs,q=u==d;if(q&&Ss(s)){if(!Ss(e))return!1;o=!0,v=!1}if(q&&!v)return i||(i=new _e),o||ur(s)?Ri(s,e,t,n,r,i):function sh(s,e,t,n,r,i,o){switch(t){case"[object DataView]":if(s.byteLength!=e.byteLength||s.byteOffset!=e.byteOffset)return!1;s=s.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(s.byteLength!=e.byteLength||!i(new Oe(s),new Oe(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return U(+s,+e);case"[object Error]":return s.name==e.name&&s.message==e.message;case"[object RegExp]":case"[object String]":return s==e+"";case"[object Map]":var a=Fu;case"[object Set]":if(a||(a=$u),s.size!=e.size&&!(1&n))return!1;var d=o.get(s);if(d)return d==e;n|=2,o.set(s,e);var v=Ri(a(s),a(e),n,r,i,o);return o.delete(s),v;case"[object Symbol]":if(Nr)return Nr.call(s)==Nr.call(e)}return!1}(s,e,u,t,n,r,i);if(!(1&t)){var W=v&&ji.call(s,"__wrapped__"),Y=S&&ji.call(e,"__wrapped__");if(W||Y){var ot=W?s.value():s,bt=Y?e.value():e;return i||(i=new _e),r(ot,bt,t,n,i)}}return!!q&&(i||(i=new _e),function ah(s,e,t,n,r,i){var o=1&t,a=vr(s),u=a.length;if(u!=vr(e).length&&!o)return!1;for(var S=u;S--;){var q=a[S];if(!(o?q in e:oh.call(e,q)))return!1}var W=i.get(s),Y=i.get(e);if(W&&Y)return W==e&&Y==s;var ot=!0;i.set(s,e),i.set(e,s);for(var bt=o;++S<u;){var Z=s[q=a[S]],qt=e[q];if(n)var ql=o?n(qt,Z,q,e,s,i):n(Z,qt,q,s,e,i);if(!(void 0===ql?Z===qt||r(Z,qt,t,n,i):ql)){ot=!1;break}bt||(bt="constructor"==q)}if(ot&&!bt){var lr=s.constructor,or=e.constructor;lr!=or&&"constructor"in s&&"constructor"in e&&!("function"==typeof lr&&lr instanceof lr&&"function"==typeof or&&or instanceof or)&&(ot=!1)}return i.delete(s),i.delete(e),ot}(s,e,t,n,r,i))},gh=function Pi(s,e,t,n,r){return s===e||(null==s||null==e||!V(s)&&!V(e)?s!=s&&e!=e:dh(s,e,t,n,Pi,r))},wr=function ph(s,e){return gh(s,e)},Be=(()=>{class s extends ee{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}return s.blotName="break",s.tagName="BR",s})();class pe extends zs{}function Ws(s){return s.replace(/[&<>"']/g,e=>({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}[e]))}const Qe=(()=>{class s extends dr{static allowedChildren=[s,Be,ee,pe];static order=["cursor","inline","link","underline","strike","italic","bold","script","code"];static compare(t,n){const r=s.order.indexOf(t),i=s.order.indexOf(n);return r>=0||i>=0?r-i:t===n?0:t<n?-1:1}formatAt(t,n,r,i){if(s.compare(this.statics.blotName,r)<0&&this.scroll.query(r,M.BLOT)){const o=this.isolate(t,n);i&&o.wrap(r,i)}else super.formatAt(t,n,r,i)}optimize(t){if(super.optimize(t),this.parent instanceof s&&s.compare(this.statics.blotName,this.parent.statics.blotName)>0){const n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}}return s})();let ne=(()=>{class s extends qs{cache={};delta(){return null==this.cache.delta&&(this.cache.delta=Fi(this)),this.cache.delta}deleteAt(t,n){super.deleteAt(t,n),this.cache={}}formatAt(t,n,r,i){n<=0||(this.scroll.query(r,M.BLOCK)?t+n===this.length()&&this.format(r,i):super.formatAt(t,Math.min(n,this.length()-t-1),r,i),this.cache={})}insertAt(t,n,r){if(null!=r)return super.insertAt(t,n,r),void(this.cache={});if(0===n.length)return;const i=n.split("\n"),o=i.shift();o.length>0&&(t<this.length()-1||null==this.children.tail?super.insertAt(Math.min(t,this.length()-1),o):this.children.tail.insertAt(this.children.tail.length(),o),this.cache={});let a=this;i.reduce((u,d)=>(a=a.split(u,!0),a.insertAt(0,d),d.length),t+o.length)}insertBefore(t,n){const{head:r}=this.children;super.insertBefore(t,n),r instanceof Be&&r.remove(),this.cache={}}length(){return null==this.cache.length&&(this.cache.length=super.length()+1),this.cache.length}moveChildren(t,n){super.moveChildren(t,n),this.cache={}}optimize(t){super.optimize(t),this.cache={}}path(t){return super.path(t,!0)}removeChild(t){super.removeChild(t),this.cache={}}split(t){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(n&&(0===t||t>=this.length()-1)){const i=this.clone();return 0===t?(this.parent.insertBefore(i,this),this):(this.parent.insertBefore(i,this.next),i)}const r=super.split(t,n);return this.cache={},r}}return s.blotName="block",s.tagName="P",s.defaultChild=Be,s.allowedChildren=[Be,Qe,ee,pe],s})();class oe extends ee{attach(){super.attach(),this.attributes=new Hs(this.domNode)}delta(){return(new O).insert(this.value(),{...this.formats(),...this.attributes.values()})}format(e,t){const n=this.scroll.query(e,M.BLOCK_ATTRIBUTE);null!=n&&this.attributes.attribute(n,t)}formatAt(e,t,n,r){this.format(n,r)}insertAt(e,t,n){if(null!=n)return void super.insertAt(e,t,n);const r=t.split("\n"),i=r.pop(),o=r.map(u=>{const d=this.scroll.create(ne.blotName);return d.insertAt(0,u),d}),a=this.split(e);o.forEach(u=>{this.parent.insertBefore(u,a)}),i&&this.parent.insertBefore(this.scroll.create("text",i),a)}}function Fi(s){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return s.descendants(Ft).reduce((t,n)=>0===n.length()?t:t.insert(n.value(),ae(n,{},e)),new O).insert("\n",ae(s))}function ae(s){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return null==s||("formats"in s&&"function"==typeof s.formats&&(e={...e,...s.formats()},t&&delete e["code-token"]),null==s.parent||"scroll"===s.parent.statics.blotName||s.parent.statics.scope!==s.statics.scope)?e:ae(s.parent,e,t)}oe.scope=M.BLOCK_BLOT;const Zs=(()=>{class s extends ee{static blotName="cursor";static className="ql-cursor";static tagName="span";static CONTENTS="\ufeff";static value(){}constructor(t,n,r){super(t,n),this.selection=r,this.textNode=document.createTextNode(s.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){null!=this.parent&&this.parent.removeChild(this)}format(t,n){if(0!==this.savedLength)return void super.format(t,n);let r=this,i=0;for(;null!=r&&r.statics.scope!==M.BLOCK_BLOT;)i+=r.offset(r.parent),r=r.parent;null!=r&&(this.savedLength=s.CONTENTS.length,r.optimize(),r.formatAt(i,s.CONTENTS.length,t,n),this.savedLength=0)}index(t,n){return t===this.textNode?0:super.index(t,n)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||null==this.parent)return null;const t=this.selection.getNativeRange();for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const n=this.prev instanceof pe?this.prev:null,r=n?n.length():0,i=this.next instanceof pe?this.next:null,o=i?i.text:"",{textNode:a}=this,u=a.data.split(s.CONTENTS).join("");let d;if(a.data=s.CONTENTS,n)d=n,(u||i)&&(n.insertAt(n.length(),u+o),i&&i.remove());else if(i)d=i,i.insertAt(0,u);else{const v=document.createTextNode(u);d=this.scroll.create(v),this.parent.insertBefore(d,this)}if(this.remove(),t){const v=(W,Y)=>n&&W===n.domNode?Y:W===a?r+Y-1:i&&W===i.domNode?r+u.length+Y:null,S=v(t.start.node,t.start.offset),q=v(t.end.node,t.end.offset);if(null!==S&&null!==q)return{startNode:d.domNode,startOffset:S,endNode:d.domNode,endOffset:q}}return null}update(t,n){if(t.some(r=>"characterData"===r.type&&r.target===this.textNode)){const r=this.restore();r&&(n.range=r)}}optimize(t){super.optimize(t);let{parent:n}=this;for(;n;){if("A"===n.domNode.tagName){this.savedLength=s.CONTENTS.length,n.isolate(this.offset(n),this.length()).unwrap(),this.savedLength=0;break}n=n.parent}}value(){return""}}return s})();var mh=j(49280);const xr=new WeakMap,Sr=["error","warn","log","info"];let Lr="warn";function Hi(s){if(Lr&&Sr.indexOf(s)<=Sr.indexOf(Lr)){for(var e=arguments.length,t=new Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];console[s](...t)}}function Cr(s){return Sr.reduce((e,t)=>(e[t]=Hi.bind(console,t,s),e),{})}Cr.level=s=>{Lr=s},Hi.level=Cr.level;const yn=Cr,Or=yn("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(s=>{document.addEventListener(s,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Array.from(document.querySelectorAll(".ql-container")).forEach(r=>{const i=xr.get(r);i&&i.emitter&&i.emitter.handleDOM(...t)})})});const B=(()=>class s extends mh{static events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"};static sources={API:"api",SILENT:"silent",USER:"user"};constructor(){super(),this.domListeners={},this.on("error",Or.error)}emit(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Or.log.call(Or,...n),super.emit(...n)}handleDOM(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];(this.domListeners[t.type]||[]).forEach(o=>{let{node:a,handler:u}=o;(t.target===a||a.contains(t.target))&&u(t,...r)})}listenDOM(t,n,r){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:n,handler:r})}})(),qr=yn("quill:selection");class Je{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.index=e,this.length=t}}function Ir(s,e){return s.contains(e)}const yh=class bh{constructor(e,t){this.emitter=t,this.scroll=e,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new Je(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,B.sources.USER),1)}),this.emitter.on(B.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;const n=this.getNativeRange();null!=n&&n.start.node!==this.cursor.textNode&&this.emitter.once(B.events.SCROLL_UPDATE,(r,i)=>{try{this.root.contains(n.start.node)&&this.root.contains(n.end.node)&&this.setNativeRange(n.start.node,n.start.offset,n.end.node,n.end.offset);const o=i.some(a=>"characterData"===a.type||"childList"===a.type||"attributes"===a.type&&a.target===this.root);this.update(o?B.sources.SILENT:r)}catch{}})}),this.emitter.on(B.events.SCROLL_OPTIMIZE,(n,r)=>{if(r.range){const{startNode:i,startOffset:o,endNode:a,endOffset:u}=r.range;this.setNativeRange(i,o,a,u),this.update(B.sources.SILENT)}}),this.update(B.sources.SILENT)}handleComposition(){this.emitter.on(B.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(B.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){const e=this.cursor.restore();if(!e)return;setTimeout(()=>{this.setNativeRange(e.startNode,e.startOffset,e.endNode,e.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(B.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(e,t){this.scroll.update();const n=this.getNativeRange();if(null!=n&&n.native.collapsed&&!this.scroll.query(e,M.BLOCK)){if(n.start.node!==this.cursor.textNode){const r=this.scroll.find(n.start.node,!1);if(null==r)return;if(r instanceof Ft){const i=r.split(n.start.offset);r.parent.insertBefore(this.cursor,i)}else r.insertBefore(this.cursor,n.start.node);this.cursor.attach()}this.cursor.format(e,t),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const n=this.scroll.length();e=Math.min(e,n-1),t=Math.min(e+t,n-1)-e;let r,[i,o]=this.scroll.leaf(e);if(null==i)return null;if(t>0&&o===i.length()){const[v]=this.scroll.leaf(e+1);if(v){const[S]=this.scroll.line(e),[q]=this.scroll.line(e+1);S===q&&(i=v,o=0)}}[r,o]=i.position(o,!0);const a=document.createRange();if(t>0)return a.setStart(r,o),[i,o]=this.scroll.leaf(e+t),null==i?null:([r,o]=i.position(o,!0),a.setEnd(r,o),a.getBoundingClientRect());let d,u="left";if(r instanceof Text){if(!r.data.length)return null;o<r.data.length?(a.setStart(r,o),a.setEnd(r,o+1)):(a.setStart(r,o-1),a.setEnd(r,o),u="right"),d=a.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;d=i.domNode.getBoundingClientRect(),o>0&&(u="right")}return{bottom:d.top+d.height,height:d.height,left:d[u],right:d[u],top:d.top,width:0}}getNativeRange(){const e=document.getSelection();if(null==e||e.rangeCount<=0)return null;const t=e.getRangeAt(0);if(null==t)return null;const n=this.normalizeNative(t);return qr.info("getNativeRange",n),n}getRange(){const e=this.scroll.domNode;if("isConnected"in e&&!e.isConnected)return[null,null];const t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]}hasFocus(){return document.activeElement===this.root||null!=document.activeElement&&Ir(this.root,document.activeElement)}normalizedToRange(e){const t=[[e.start.node,e.start.offset]];e.native.collapsed||t.push([e.end.node,e.end.offset]);const n=t.map(o=>{const[a,u]=o,d=this.scroll.find(a,!0),v=d.offset(this.scroll);return 0===u?v:d instanceof Ft?v+d.index(a,u):v+d.length()}),r=Math.min(Math.max(...n),this.scroll.length()-1),i=Math.min(r,...n);return new Je(i,r-i)}normalizeNative(e){if(!Ir(this.root,e.startContainer)||!e.collapsed&&!Ir(this.root,e.endContainer))return null;const t={start:{node:e.startContainer,offset:e.startOffset},end:{node:e.endContainer,offset:e.endOffset},native:e};return[t.start,t.end].forEach(n=>{let{node:r,offset:i}=n;for(;!(r instanceof Text)&&r.childNodes.length>0;)if(r.childNodes.length>i)r=r.childNodes[i],i=0;else{if(r.childNodes.length!==i)break;r=r.lastChild,i=r instanceof Text?r.data.length:r.childNodes.length>0?r.childNodes.length:r.childNodes.length+1}n.node=r,n.offset=i}),t}rangeToNative(e){const t=this.scroll.length(),n=(r,i)=>{r=Math.min(t-1,r);const[o,a]=this.scroll.leaf(r);return o?o.position(a,i):[null,-1]};return[...n(e.index,!1),...n(e.index+e.length,!0)]}setNativeRange(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(qr.info("setNativeRange",e,t,n,r),null!=e&&(null==this.root.parentNode||null==e.parentNode||null==n.parentNode))return;const o=document.getSelection();if(null!=o)if(null!=e){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:a}=this.getNativeRange()||{};if(null==a||i||e!==a.startContainer||t!==a.startOffset||n!==a.endContainer||r!==a.endOffset){e instanceof Element&&"BR"===e.tagName&&(t=Array.from(e.parentNode.childNodes).indexOf(e),e=e.parentNode),n instanceof Element&&"BR"===n.tagName&&(r=Array.from(n.parentNode.childNodes).indexOf(n),n=n.parentNode);const u=document.createRange();u.setStart(e,t),u.setEnd(n,r),o.removeAllRanges(),o.addRange(u)}}else o.removeAllRanges(),this.root.blur()}setRange(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:B.sources.API;if("string"==typeof t&&(n=t,t=!1),qr.info("setRange",e),null!=e){const r=this.rangeToNative(e);this.setNativeRange(...r,t)}else this.setNativeRange(null);this.update(n)}update(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:B.sources.USER;const t=this.lastRange,[n,r]=this.getRange();if(this.lastRange=n,this.lastNative=r,null!=this.lastRange&&(this.savedRange=this.lastRange),!wr(t,this.lastRange)){if(!this.composing&&null!=r&&r.native.collapsed&&r.start.node!==this.cursor.textNode){const o=this.cursor.restore();o&&this.setNativeRange(o.startNode,o.startOffset,o.endNode,o.endOffset)}const i=[B.events.SELECTION_CHANGE,Vn(this.lastRange),Vn(t),e];this.emitter.emit(B.events.EDITOR_CHANGE,...i),e!==B.sources.SILENT&&this.emitter.emit(...i)}}},vh=/^[ -~]*$/;function Wn(s,e,t){if(0===s.length){const[W]=kr(t.pop());return e<=0?`</li></${W}>`:`</li></${W}>${Wn([],e-1,t)}`}const[{child:n,offset:r,length:i,indent:o,type:a},...u]=s,[d,v]=kr(a);if(o>e)return t.push(a),o===e+1?`<${d}><li${v}>${ks(n,r,i)}${Wn(u,o,t)}`:`<${d}><li>${Wn(s,e+1,t)}`;if(o===e&&a===t[t.length-1])return`</li><li${v}>${ks(n,r,i)}${Wn(u,o,t)}`;const[q]=kr(t.pop());return`</li></${q}>${Wn(s,e-1,t)}`}function ks(s,e,t){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if("html"in s&&"function"==typeof s.html)return s.html(e,t);if(s instanceof pe)return Ws(s.value().slice(e,e+t));if(s instanceof ge){if("list-container"===s.statics.blotName){const d=[];return s.children.forEachAt(e,t,(v,S,q)=>{const W="formats"in v&&"function"==typeof v.formats?v.formats():{};d.push({child:v,offset:S,length:q,indent:W.indent||0,type:W.list})}),Wn(d,-1,[])}const r=[];if(s.children.forEachAt(e,t,(d,v,S)=>{r.push(ks(d,v,S))}),n||"list"===s.statics.blotName)return r.join("");const{outerHTML:i,innerHTML:o}=s.domNode,[a,u]=i.split(`>${o}<`);return"<table"===a?`<table style="border: 1px solid #000;">${r.join("")}<${u}`:`${a}>${r.join("")}<${u}`}return s.domNode instanceof Element?s.domNode.outerHTML:""}function Ah(s,e){return Object.keys(e).reduce((t,n)=>{if(null==s[n])return t;const r=e[n];return t[n]=r===s[n]?r:Array.isArray(r)?r.indexOf(s[n])<0?r.concat([s[n]]):r:[r,s[n]],t},{})}function kr(s){const e="ordered"===s?"ol":"ul";switch(s){case"checked":return[e,' data-list="checked"'];case"unchecked":return[e,' data-list="unchecked"'];default:return[e,""]}}function $i(s){return s.reduce((e,t)=>{if("string"==typeof t.insert){const n=t.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return e.insert(n,t.attributes)}return e.push(t)},new O)}function zi(s,e){let{index:t,length:n}=s;return new Je(t+e,n)}const Th=class _h{constructor(e){this.scroll=e,this.delta=this.getDelta()}applyDelta(e){this.scroll.update();let t=this.scroll.length();this.scroll.batchStart();const n=$i(e),r=new O;return function Eh(s){const e=[];return s.forEach(t=>{"string"==typeof t.insert?t.insert.split("\n").forEach((r,i)=>{i&&e.push({insert:"\n",attributes:t.attributes}),r&&e.push({insert:r,attributes:t.attributes})}):e.push(t)}),e}(n.ops.slice()).reduce((o,a)=>{const u=O.Op.length(a);let d=a.attributes||{},v=!1,S=!1;if(null!=a.insert){if(r.retain(u),"string"==typeof a.insert){const Y=a.insert;S=!Y.endsWith("\n")&&(t<=o||!!this.scroll.descendant(oe,o)[0]),this.scroll.insertAt(o,Y);const[ot,bt]=this.scroll.line(o);let Z=Xe({},ae(ot));if(ot instanceof ne){const[qt]=ot.descendant(Ft,bt);qt&&(Z=Xe(Z,ae(qt)))}d=O.AttributeMap.diff(Z,d)||{}}else if("object"==typeof a.insert){const Y=Object.keys(a.insert)[0];if(null==Y)return o;const ot=null!=this.scroll.query(Y,M.INLINE);if(ot)(t<=o||this.scroll.descendant(oe,o)[0])&&(S=!0);else if(o>0){const[bt,Z]=this.scroll.descendant(Ft,o-1);bt instanceof pe?"\n"!==bt.value()[Z]&&(v=!0):bt instanceof ee&&bt.statics.scope===M.INLINE_BLOT&&(v=!0)}if(this.scroll.insertAt(o,Y,a.insert[Y]),ot){const[bt]=this.scroll.descendant(Ft,o);if(bt){const Z=Xe({},ae(bt));d=O.AttributeMap.diff(Z,d)||{}}}}t+=u}else if(r.push(a),null!==a.retain&&"object"==typeof a.retain){const Y=Object.keys(a.retain)[0];if(null==Y)return o;this.scroll.updateEmbedAt(o,Y,a.retain[Y])}Object.keys(d).forEach(Y=>{this.scroll.formatAt(o,u,Y,d[Y])});const q=v?1:0,W=S?1:0;return t+=q+W,r.retain(q),r.delete(W),o+u+q+W},0),r.reduce((o,a)=>"number"==typeof a.delete?(this.scroll.deleteAt(o,a.delete),o):o+O.Op.length(a),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(n)}deleteText(e,t){return this.scroll.deleteAt(e,t),this.update((new O).retain(e).delete(t))}formatLine(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update(),Object.keys(n).forEach(i=>{this.scroll.lines(e,Math.max(t,1)).forEach(o=>{o.format(i,n[i])})}),this.scroll.optimize();const r=(new O).retain(e).retain(t,Vn(n));return this.update(r)}formatText(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Object.keys(n).forEach(i=>{this.scroll.formatAt(e,t,i,n[i])});const r=(new O).retain(e).retain(t,Vn(n));return this.update(r)}getContents(e,t){return this.delta.slice(e,e+t)}getDelta(){return this.scroll.lines().reduce((e,t)=>e.concat(t.delta()),new O)}getFormat(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=[],r=[];0===t?this.scroll.path(e).forEach(a=>{const[u]=a;u instanceof ne?n.push(u):u instanceof Ft&&r.push(u)}):(n=this.scroll.lines(e,t),r=this.scroll.descendants(Ft,e,t));const[i,o]=[n,r].map(a=>{const u=a.shift();if(null==u)return{};let d=ae(u);for(;Object.keys(d).length>0;){const v=a.shift();if(null==v)return d;d=Ah(ae(v),d)}return d});return{...i,...o}}getHTML(e,t){const[n,r]=this.scroll.line(e);if(n){const i=n.length();return n.length()>=r+t&&(0!==r||t!==i)?ks(n,r,t,!0):ks(this.scroll,e,t,!0)}return""}getText(e,t){return this.getContents(e,t).filter(n=>"string"==typeof n.insert).map(n=>n.insert).join("")}insertContents(e,t){const n=$i(t),r=(new O).retain(e).concat(n);return this.scroll.insertContents(e,n),this.update(r)}insertEmbed(e,t,n){return this.scroll.insertAt(e,t,n),this.update((new O).retain(e).insert({[t]:n}))}insertText(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t=t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(e,t),Object.keys(n).forEach(r=>{this.scroll.formatAt(e,t.length,r,n[r])}),this.update((new O).retain(e).insert(t,Vn(n)))}isBlank(){if(0===this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;const e=this.scroll.children.head;return e?.statics.blotName===ne.blotName&&(!(e.children.length>1)&&e.children.head instanceof Be)}removeFormat(e,t){const n=this.getText(e,t),[r,i]=this.scroll.line(e+t);let o=0,a=new O;null!=r&&(o=r.length()-i,a=r.delta().slice(i,i+o-1).insert("\n"));const d=this.getContents(e,t+o).diff((new O).insert(n).concat(a)),v=(new O).retain(e).concat(d);return this.applyDelta(v)}update(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const r=this.delta;if(1===t.length&&"characterData"===t[0].type&&t[0].target.data.match(vh)&&this.scroll.find(t[0].target)){const i=this.scroll.find(t[0].target),o=ae(i),a=i.offset(this.scroll),u=t[0].oldValue.replace(Zs.CONTENTS,""),d=(new O).insert(u),v=(new O).insert(i.value()),S=n&&{oldRange:zi(n.oldRange,-a),newRange:zi(n.newRange,-a)};e=(new O).retain(a).concat(d.diff(v,S)).reduce((W,Y)=>Y.insert?W.insert(Y.insert,o):W.push(Y),new O),this.delta=r.compose(e)}else this.delta=this.getDelta(),(!e||!wr(r.compose(e),this.delta))&&(e=r.diff(this.delta,n));return e}},me=(()=>class s{static DEFAULTS={};constructor(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.quill=t,this.options=n}})(),Rr=class Nh extends ee{constructor(e,t){super(e,t),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(n=>{this.contentNode.appendChild(n)}),this.leftGuard=document.createTextNode("\ufeff"),this.rightGuard=document.createTextNode("\ufeff"),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(e,t){return e===this.leftGuard?0:e===this.rightGuard?1:super.index(e,t)}restore(e){let n,t=null;const r=e.data.split("\ufeff").join("");if(e===this.leftGuard)if(this.prev instanceof pe){const i=this.prev.length();this.prev.insertAt(i,r),t={startNode:this.prev.domNode,startOffset:i+r.length}}else n=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(n),this),t={startNode:n,startOffset:r.length};else e===this.rightGuard&&(this.next instanceof pe?(this.next.insertAt(0,r),t={startNode:this.next.domNode,startOffset:r.length}):(n=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(n),this.next),t={startNode:n,startOffset:r.length}));return e.data="\ufeff",t}update(e,t){e.forEach(n=>{if("characterData"===n.type&&(n.target===this.leftGuard||n.target===this.rightGuard)){const r=this.restore(n.target);r&&(t.range=r)}})}},xh=class wh{isComposing=!1;constructor(e,t){this.scroll=e,this.emitter=t,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",e=>{this.isComposing||this.handleCompositionStart(e)}),this.scroll.domNode.addEventListener("compositionend",e=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(e)})})}handleCompositionStart(e){const t=e.target instanceof Node?this.scroll.find(e.target,!0):null;t&&!(t instanceof Rr)&&(this.emitter.emit(B.events.COMPOSITION_BEFORE_START,e),this.scroll.batchStart(),this.emitter.emit(B.events.COMPOSITION_START,e),this.isComposing=!0)}handleCompositionEnd(e){this.emitter.emit(B.events.COMPOSITION_BEFORE_END,e),this.scroll.batchEnd(),this.emitter.emit(B.events.COMPOSITION_END,e),this.isComposing=!1}},Ys=(()=>{class s{static DEFAULTS={modules:{}};static themes={default:s};modules={};constructor(t,n){this.quill=t,this.options=n}init(){Object.keys(this.options.modules).forEach(t=>{null==this.modules[t]&&this.addModule(t)})}addModule(t){const n=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new n(this.quill,this.options.modules[t]||{}),this.modules[t]}}return s})(),Sh=s=>s.parentElement||s.getRootNode().host||null,Lh=s=>{const e=s.getBoundingClientRect(),t="offsetWidth"in s&&Math.abs(e.width)/s.offsetWidth||1,n="offsetHeight"in s&&Math.abs(e.height)/s.offsetHeight||1;return{top:e.top,right:e.left+s.clientWidth*t,bottom:e.top+s.clientHeight*n,left:e.left}},Qs=s=>{const e=parseInt(s,10);return Number.isNaN(e)?0:e},Ki=(s,e,t,n,r,i)=>s<t&&e>n?0:s<t?-(t-s+r):e>n?e-s>n-t?s+r-t:e-n+i:0,qh=["block","break","cursor","inline","scroll","text"],Zn=yn("quill"),Js=new $n;ge.uiClass="ql-ui";class y{static DEFAULTS={bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:Js,theme:"default"};static events=B.events;static sources=B.sources;static version="2.0.2";static imports={delta:O,parchment:Q,"core/module":me,"core/theme":Ys};static debug(e){!0===e&&(e="log"),yn.level(e)}static find(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return xr.get(e)||Js.find(e,t)}static import(e){return null==this.imports[e]&&Zn.error(`Cannot import ${e}. Are you sure it was registered?`),this.imports[e]}static register(){if("string"!=typeof(arguments.length<=0?void 0:arguments[0])){const e=arguments.length<=0?void 0:arguments[0],t=!(arguments.length<=1||!arguments[1]),n="attrName"in e?e.attrName:e.blotName;"string"==typeof n?this.register(`formats/${n}`,e,t):Object.keys(e).forEach(r=>{this.register(r,e[r],t)})}else{const e=arguments.length<=0?void 0:arguments[0],t=arguments.length<=1?void 0:arguments[1];null!=this.imports[e]&&!(!(arguments.length<=2)&&arguments[2])&&Zn.warn(`Overwriting ${e} with`,t),this.imports[e]=t,(e.startsWith("blots/")||e.startsWith("formats/"))&&t&&"boolean"!=typeof t&&"abstract"!==t.blotName&&Js.register(t),"function"==typeof t.register&&t.register(Js)}}constructor(e){if(this.options=function kh(s,e){const t=Gi(s);if(!t)throw new Error("Invalid Quill container");const r=e.theme&&e.theme!==y.DEFAULTS.theme?y.import(`themes/${e.theme}`):Ys;if(!r)throw new Error(`Invalid theme ${e.theme}. Did you register it?`);const{modules:i,...o}=y.DEFAULTS,{modules:a,...u}=r.DEFAULTS;let d=Br(e.modules);null!=d&&d.toolbar&&d.toolbar.constructor!==Object&&(d={...d,toolbar:{container:d.toolbar}});const v=Xe({},Br(i),Br(a),d),S={...o,...Vi(u),...Vi(e)};let q=e.registry;return q?e.formats&&Zn.warn('Ignoring "formats" option because "registry" is specified'):q=e.formats?((s,e,t)=>{const n=new $n;return qh.forEach(r=>{const i=e.query(r);i&&n.register(i)}),s.forEach(r=>{let i=e.query(r);i||t.error(`Cannot register "${r}" specified in "formats" config. Are you sure it was registered?`);let o=0;for(;i;)if(n.register(i),i="blotName"in i?i.requiredContainer??null:null,o+=1,o>100){t.error(`Cycle detected in registering blot requiredContainer: "${r}"`);break}}),n})(e.formats,S.registry,Zn):S.registry,{...S,registry:q,container:t,theme:r,modules:Object.entries(v).reduce((W,Y)=>{let[ot,bt]=Y;if(!bt)return W;const Z=y.import(`modules/${ot}`);return null==Z?(Zn.error(`Cannot load ${ot} module. Are you sure you registered it?`),W):{...W,[ot]:Xe({},Z.DEFAULTS||{},bt)}},{}),bounds:Gi(S.bounds)}}(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),this.container=this.options.container,null==this.container)return void Zn.error("Invalid Quill container",e);this.options.debug&&y.debug(this.options.debug);const n=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",xr.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new B;const r=pr.blotName,i=this.options.registry.query(r);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${r}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new Th(this.scroll),this.selection=new yh(this.scroll,this.emitter),this.composition=new xh(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(B.events.EDITOR_CHANGE,o=>{o===B.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(B.events.SCROLL_UPDATE,(o,a)=>{const u=this.selection.lastRange,[d]=this.selection.getRange(),v=u&&d?{oldRange:u,newRange:d}:void 0;be.call(this,()=>this.editor.update(null,a,v),o)}),this.emitter.on(B.events.SCROLL_EMBED_UPDATE,(o,a)=>{const u=this.selection.lastRange,[d]=this.selection.getRange(),v=u&&d?{oldRange:u,newRange:d}:void 0;be.call(this,()=>{const S=(new O).retain(o.offset(this)).retain({[o.statics.blotName]:a});return this.editor.update(S,[],v)},y.sources.USER)}),n){const o=this.clipboard.convert({html:`${n}<p><br></p>`,text:"\n"});this.setContents(o)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e){const n=e;(e=document.createElement("div")).classList.add(n)}return this.container.insertBefore(e,t),e}blur(){this.selection.setRange(null)}deleteText(e,t,n){return[e,t,,n]=Me(e,t,n),be.call(this,()=>this.editor.deleteText(e,t),n,e,-1*t)}disable(){this.enable(!1)}editReadOnly(e){this.allowReadOnlyEdits=!0;const t=e();return this.allowReadOnlyEdits=!1,t}enable(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(e),this.container.classList.toggle("ql-disabled",!e)}focus(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selection.focus(),e.preventScroll||this.scrollSelectionIntoView()}format(e,t){return be.call(this,()=>{const r=this.getSelection(!0);let i=new O;if(null==r)return i;if(this.scroll.query(e,M.BLOCK))i=this.editor.formatLine(r.index,r.length,{[e]:t});else{if(0===r.length)return this.selection.format(e,t),i;i=this.editor.formatText(r.index,r.length,{[e]:t})}return this.setSelection(r,B.sources.SILENT),i},arguments.length>2&&void 0!==arguments[2]?arguments[2]:B.sources.API)}formatLine(e,t,n,r,i){let o;return[e,t,o,i]=Me(e,t,n,r,i),be.call(this,()=>this.editor.formatLine(e,t,o),i,e,0)}formatText(e,t,n,r,i){let o;return[e,t,o,i]=Me(e,t,n,r,i),be.call(this,()=>this.editor.formatText(e,t,o),i,e,0)}getBounds(e){let n=null;if(n="number"==typeof e?this.selection.getBounds(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0):this.selection.getBounds(e.index,e.length),!n)return null;const r=this.container.getBoundingClientRect();return{bottom:n.bottom-r.top,height:n.height,left:n.left-r.left,right:n.right-r.left,top:n.top-r.top,width:n.width}}getContents(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-e;return[e,t]=Me(e,t),this.editor.getContents(e,t)}getFormat(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0);return"number"==typeof e?this.editor.getFormat(e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0):this.editor.getFormat(e.index,e.length)}getIndex(e){return e.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(e){return this.scroll.leaf(e)}getLine(e){return this.scroll.line(e)}getLines(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof e?this.scroll.lines(e.index,e.length):this.scroll.lines(e,t)}getModule(e){return this.theme.modules[e]}getSelection(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;return"number"==typeof e&&(t=t??this.getLength()-e),[e,t]=Me(e,t),this.editor.getHTML(e,t)}getText(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;return"number"==typeof e&&(t=t??this.getLength()-e),[e,t]=Me(e,t),this.editor.getText(e,t)}hasFocus(){return this.selection.hasFocus()}insertEmbed(e,t,n){return be.call(this,()=>this.editor.insertEmbed(e,t,n),arguments.length>3&&void 0!==arguments[3]?arguments[3]:y.sources.API,e)}insertText(e,t,n,r,i){let o;return[e,,o,i]=Me(e,0,n,r,i),be.call(this,()=>this.editor.insertText(e,t,o),i,e,t.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(e,t,n){return[e,t,,n]=Me(e,t,n),be.call(this,()=>this.editor.removeFormat(e,t),n,e)}scrollRectIntoView(e){((s,e)=>{const t=s.ownerDocument;let n=e,r=s;for(;r;){const i=r===t.body,o=i?{top:0,right:window.visualViewport?.width??t.documentElement.clientWidth,bottom:window.visualViewport?.height??t.documentElement.clientHeight,left:0}:Lh(r),a=getComputedStyle(r),u=Ki(n.left,n.right,o.left,o.right,Qs(a.scrollPaddingLeft),Qs(a.scrollPaddingRight)),d=Ki(n.top,n.bottom,o.top,o.bottom,Qs(a.scrollPaddingTop),Qs(a.scrollPaddingBottom));if(u||d)if(i)t.defaultView?.scrollBy(u,d);else{const{scrollLeft:v,scrollTop:S}=r;d&&(r.scrollTop+=d),u&&(r.scrollLeft+=u);const q=r.scrollLeft-v,W=r.scrollTop-S;n={left:n.left-q,top:n.top-W,right:n.right-q,bottom:n.bottom-W}}r=i||"fixed"===a.position?null:Sh(r)}})(this.root,e)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const e=this.selection.lastRange,t=e&&this.selection.getBounds(e.index,e.length);t&&this.scrollRectIntoView(t)}setContents(e){return be.call(this,()=>{e=new O(e);const n=this.getLength(),r=this.editor.deleteText(0,n),i=this.editor.insertContents(0,e),o=this.editor.deleteText(this.getLength()-1,1);return r.compose(i).compose(o)},arguments.length>1&&void 0!==arguments[1]?arguments[1]:B.sources.API)}setSelection(e,t,n){null==e?this.selection.setRange(null,t||y.sources.API):([e,t,,n]=Me(e,t,n),this.selection.setRange(new Je(Math.max(0,e),t),n),n!==B.sources.SILENT&&this.scrollSelectionIntoView())}setText(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:B.sources.API;const n=(new O).insert(e);return this.setContents(n,t)}update(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:B.sources.USER;const t=this.scroll.update(e);return this.selection.update(e),t}updateContents(e){return be.call(this,()=>(e=new O(e),this.editor.applyDelta(e)),arguments.length>1&&void 0!==arguments[1]?arguments[1]:B.sources.API,!0)}}function Gi(s){return"string"==typeof s?document.querySelector(s):s}function Br(s){return Object.entries(s??{}).reduce((e,t)=>{let[n,r]=t;return{...e,[n]:!0===r?{}:r}},{})}function Vi(s){return Object.fromEntries(Object.entries(s).filter(e=>void 0!==e[1]))}function be(s,e,t,n){if(!this.isEnabled()&&e===B.sources.USER&&!this.allowReadOnlyEdits)return new O;let r=null==t?null:this.getSelection();const i=this.editor.delta,o=s();if(null!=r&&(!0===t&&(t=r.index),null==n?r=Wi(r,o,e):0!==n&&(r=Wi(r,t,n,e)),this.setSelection(r,B.sources.SILENT)),o.length()>0){const a=[B.events.TEXT_CHANGE,o,i,e];this.emitter.emit(B.events.EDITOR_CHANGE,...a),e!==B.sources.SILENT&&this.emitter.emit(...a)}return o}function Me(s,e,t,n,r){let i={};return"number"==typeof s.index&&"number"==typeof s.length?"number"!=typeof e?(r=n,n=t,t=e,e=s.length,s=s.index):(e=s.length,s=s.index):"number"!=typeof e&&(r=n,n=t,t=e,e=0),"object"==typeof t?(i=t,r=n):"string"==typeof t&&(null!=n?i[t]=n:r=t),[s,e,i,r=r||B.sources.API]}function Wi(s,e,t,n){const r="number"==typeof t?t:0;if(null==s)return null;let i,o;return[i,o]=[s.index,s.index+s.length].map(e&&"function"==typeof e.transformPosition?a=>e.transformPosition(a,n!==B.sources.USER):a=>a<e||a===e&&n===B.sources.USER?a:r>=0?a+r:Math.max(e,a+r)),new Je(i,o-i)}const vn=class Rh extends $s{};function Zi(s){return s instanceof ne||s instanceof oe}function Xi(s){return"function"==typeof s.updateContent}function Mr(s,e,t){t.reduce((n,r)=>{const i=O.Op.length(r);let o=r.attributes||{};if(null!=r.insert)if("string"==typeof r.insert){s.insertAt(n,r.insert);const[u]=s.descendant(Ft,n),d=ae(u);o=O.AttributeMap.diff(d,o)||{}}else if("object"==typeof r.insert){const a=Object.keys(r.insert)[0];if(null==a)return n;if(s.insertAt(n,a,r.insert[a]),null!=s.scroll.query(a,M.INLINE)){const[d]=s.descendant(Ft,n),v=ae(d);o=O.AttributeMap.diff(v,o)||{}}}return Object.keys(o).forEach(a=>{s.formatAt(n,i,a,o[a])}),n+i},e)}const Mh=(()=>class s extends pr{static blotName="scroll";static className="ql-editor";static tagName="DIV";static defaultChild=ne;static allowedChildren=[ne,oe,vn];constructor(t,n,r){let{emitter:i}=r;super(t,n),this.emitter=i,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",o=>this.handleDragStart(o))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(B.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(B.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,n){this.emitter.emit(B.events.SCROLL_EMBED_UPDATE,t,n)}deleteAt(t,n){const[r,i]=this.line(t),[o]=this.line(t+n);if(super.deleteAt(t,n),null!=o&&r!==o&&i>0){if(r instanceof oe||o instanceof oe)return void this.optimize();r.moveChildren(o,o.children.head instanceof Be?null:o.children.head),r.remove()}this.optimize()}enable(){this.domNode.setAttribute("contenteditable",arguments.length>0&&void 0!==arguments[0]&&!arguments[0]?"false":"true")}formatAt(t,n,r,i){super.formatAt(t,n,r,i),this.optimize()}insertAt(t,n,r){if(t>=this.length())if(null==r||null==this.scroll.query(n,M.BLOCK)){const i=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(i),null==r&&n.endsWith("\n")?i.insertAt(0,n.slice(0,-1),r):i.insertAt(0,n,r)}else{const i=this.scroll.create(n,r);this.appendChild(i)}else super.insertAt(t,n,r);this.optimize()}insertBefore(t,n){if(t.statics.scope===M.INLINE_BLOT){const r=this.scroll.create(this.statics.defaultChild.blotName);r.appendChild(t),super.insertBefore(r,n)}else super.insertBefore(t,n)}insertContents(t,n){const r=this.deltaToRenderBlocks(n.concat((new O).insert("\n"))),i=r.pop();if(null==i)return;this.batchStart();const o=r.shift();if(o){const d="block"===o.type&&(0===o.delta.length()||!this.descendant(oe,t)[0]&&t<this.length()),v="block"===o.type?o.delta:(new O).insert({[o.key]:o.value});Mr(this,t,v);const S="block"===o.type?1:0,q=t+v.length()+S;d&&this.insertAt(q-1,"\n");const W=ae(this.line(t)[0]),Y=O.AttributeMap.diff(W,o.attributes)||{};Object.keys(Y).forEach(ot=>{this.formatAt(q-1,1,ot,Y[ot])}),t=q}let[a,u]=this.children.find(t);r.length&&(a&&(a=a.split(u),u=0),r.forEach(d=>{if("block"===d.type)Mr(this.createBlock(d.attributes,a||void 0),0,d.delta);else{const v=this.create(d.key,d.value);this.insertBefore(v,a||void 0),Object.keys(d.attributes).forEach(S=>{v.format(S,d.attributes[S])})}})),"block"===i.type&&i.delta.length()&&Mr(this,a?a.offset(a.scroll)+u:this.length(),i.delta),this.batchEnd(),this.optimize()}isEnabled(){return"true"===this.domNode.getAttribute("contenteditable")}leaf(t){const n=this.path(t).pop();if(!n)return[null,-1];const[r,i]=n;return r instanceof Ft?[r,i]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(Zi,t)}lines(){const r=(i,o,a)=>{let u=[],d=a;return i.children.forEachAt(o,a,(v,S,q)=>{Zi(v)?u.push(v):v instanceof $s&&(u=u.concat(r(v,S,d))),d-=q}),u};return r(this,arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}optimize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.batch||(super.optimize(t,n),t.length>0&&this.emitter.emit(B.events.SCROLL_OPTIMIZE,t,n))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch)return void(Array.isArray(t)&&(this.batch=this.batch.concat(t)));let n=B.sources.USER;"string"==typeof t&&(n=t),Array.isArray(t)||(t=this.observer.takeRecords()),(t=t.filter(r=>{let{target:i}=r;const o=this.find(i,!0);return o&&!Xi(o)})).length>0&&this.emitter.emit(B.events.SCROLL_BEFORE_UPDATE,n,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(B.events.SCROLL_UPDATE,n,t)}updateEmbedAt(t,n,r){const[i]=this.descendant(o=>o instanceof oe,t);i&&i.statics.blotName===n&&Xi(i)&&i.updateContent(r)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const n=[];let r=new O;return t.forEach(i=>{const o=i?.insert;if(o)if("string"==typeof o){const a=o.split("\n");a.slice(0,-1).forEach(d=>{r.insert(d,i.attributes),n.push({type:"block",delta:r,attributes:i.attributes??{}}),r=new O});const u=a[a.length-1];u&&r.insert(u,i.attributes)}else{const a=Object.keys(o)[0];if(!a)return;this.query(a,M.INLINE)?r.push(i):(r.length()&&n.push({type:"block",delta:r,attributes:{}}),r=new O,n.push({type:"blockEmbed",key:a,value:o[a],attributes:i.attributes??{}}))}}),r.length()&&n.push({type:"block",delta:r,attributes:{}}),n}createBlock(t,n){let r;const i={};Object.entries(t).forEach(u=>{let[d,v]=u;null!=this.query(d,M.BLOCK&M.BLOT)?r=d:i[d]=v});const o=this.create(r||this.statics.defaultChild.blotName,r?t[r]:void 0);this.insertBefore(o,n||void 0);const a=o.length();return Object.entries(i).forEach(u=>{let[d,v]=u;o.formatAt(0,a,d,v)}),o}})(),Dr={scope:M.BLOCK,whitelist:["right","center","justify"]},Dh=new Te("align","align",Dr),Yi=new de("align","ql-align",Dr),Qi=new Ye("align","text-align",Dr);class Ji extends Ye{value(e){let t=super.value(e);return t.startsWith("rgb(")?(t=t.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${t.split(",").map(r=>`00${parseInt(r,10).toString(16)}`.slice(-2)).join("")}`):t}}const jh=new de("color","ql-color",{scope:M.INLINE}),jr=new Ji("color","color",{scope:M.INLINE}),Ph=new de("background","ql-bg",{scope:M.INLINE}),Pr=new Ji("background","background-color",{scope:M.INLINE});class _n extends vn{static create(e){const t=super.create(e);return t.setAttribute("spellcheck","false"),t}code(e,t){return this.children.map(n=>n.length()<=1?"":n.domNode.innerText).join("\n").slice(e,e+t)}html(e,t){return`<pre>\n${Ws(this.code(e,t))}\n</pre>`}}let zt=(()=>class s extends ne{static TAB="  ";static register(){y.register(_n)}})(),Uh=(()=>{class s extends Qe{}return s.blotName="code",s.tagName="CODE",s})();zt.blotName="code-block",zt.className="ql-code-block",zt.tagName="DIV",_n.blotName="code-block-container",_n.className="ql-code-block-container",_n.tagName="DIV",_n.allowedChildren=[zt],zt.allowedChildren=[pe,Be,Zs],zt.requiredContainer=_n;const Ur={scope:M.BLOCK,whitelist:["rtl"]},tl=new Te("direction","dir",Ur),el=new de("direction","ql-direction",Ur),nl=new Ye("direction","direction",Ur),sl={scope:M.INLINE,whitelist:["serif","monospace"]},rl=new de("font","ql-font",sl),il=new class Fh extends Ye{value(e){return super.value(e).replace(/["']/g,"")}}("font","font-family",sl),ll=new de("size","ql-size",{scope:M.INLINE,whitelist:["small","large","huge"]}),ol=new Ye("size","font-size",{scope:M.INLINE,whitelist:["10px","18px","32px"]}),Hh=yn("quill:keyboard"),$h=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class tr extends me{static match(e,t){return!["altKey","ctrlKey","metaKey","shiftKey"].some(n=>!!t[n]!==e[n]&&null!==t[n])&&(t.key===e.key||t.key===e.which)}constructor(e,t){super(e,t),this.bindings={},Object.keys(this.options.bindings).forEach(n=>{this.options.bindings[n]&&this.addBinding(this.options.bindings[n])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=function Kh(s){if("string"==typeof s||"number"==typeof s)s={key:s};else{if("object"!=typeof s)return null;s=Vn(s)}return s.shortKey&&(s[$h]=s.shortKey,delete s.shortKey),s}(e);null!=r?("function"==typeof t&&(t={handler:t}),"function"==typeof n&&(n={handler:n}),(Array.isArray(r.key)?r.key:[r.key]).forEach(o=>{const a={...r,key:o,...t,...n};this.bindings[a.key]=this.bindings[a.key]||[],this.bindings[a.key].push(a)})):Hh.warn("Attempted to add invalid keyboard binding",r)}listen(){this.quill.root.addEventListener("keydown",e=>{if(e.defaultPrevented||e.isComposing||229===e.keyCode&&("Enter"===e.key||"Backspace"===e.key))return;const r=(this.bindings[e.key]||[]).concat(this.bindings[e.which]||[]).filter(Z=>tr.match(e,Z));if(0===r.length)return;const i=y.find(e.target,!0);if(i&&i.scroll!==this.quill.scroll)return;const o=this.quill.getSelection();if(null==o||!this.quill.hasFocus())return;const[a,u]=this.quill.getLine(o.index),[d,v]=this.quill.getLeaf(o.index),[S,q]=0===o.length?[d,v]:this.quill.getLeaf(o.index+o.length),W=d instanceof zs?d.value().slice(0,v):"",Y=S instanceof zs?S.value().slice(q):"",ot={collapsed:0===o.length,empty:0===o.length&&a.length()<=1,format:this.quill.getFormat(o),line:a,offset:u,prefix:W,suffix:Y,event:e};r.some(Z=>{if(null!=Z.collapsed&&Z.collapsed!==ot.collapsed||null!=Z.empty&&Z.empty!==ot.empty||null!=Z.offset&&Z.offset!==ot.offset)return!1;if(Array.isArray(Z.format)){if(Z.format.every(qt=>null==ot.format[qt]))return!1}else if("object"==typeof Z.format&&!Object.keys(Z.format).every(qt=>!0===Z.format[qt]?null!=ot.format[qt]:!1===Z.format[qt]?null==ot.format[qt]:wr(Z.format[qt],ot.format[qt])))return!1;return!(null!=Z.prefix&&!Z.prefix.test(ot.prefix)||null!=Z.suffix&&!Z.suffix.test(ot.suffix))&&!0!==Z.handler.call(this,o,ot,Z)})&&e.preventDefault()})}handleBackspace(e,t){const n=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(t.prefix)?2:1;if(0===e.index||this.quill.getLength()<=1)return;let r={};const[i]=this.quill.getLine(e.index);let o=(new O).retain(e.index-n).delete(n);if(0===t.offset){const[a]=this.quill.getLine(e.index-1);if(a&&!("block"===a.statics.blotName&&a.length()<=1)){const d=i.formats(),v=this.quill.getFormat(e.index-1,1);if(r=O.AttributeMap.diff(d,v)||{},Object.keys(r).length>0){const S=(new O).retain(e.index+i.length()-2).retain(1,r);o=o.compose(S)}}}this.quill.updateContents(o,y.sources.USER),this.quill.focus()}handleDelete(e,t){const n=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(t.suffix)?2:1;if(e.index>=this.quill.getLength()-n)return;let r={};const[i]=this.quill.getLine(e.index);let o=(new O).retain(e.index).delete(n);if(t.offset>=i.length()-1){const[a]=this.quill.getLine(e.index+1);if(a){const u=i.formats(),d=this.quill.getFormat(e.index,1);r=O.AttributeMap.diff(u,d)||{},Object.keys(r).length>0&&(o=o.retain(a.length()-1).retain(1,r))}}this.quill.updateContents(o,y.sources.USER),this.quill.focus()}handleDeleteRange(e){Hr({range:e,quill:this.quill}),this.quill.focus()}handleEnter(e,t){const n=Object.keys(t.format).reduce((i,o)=>(this.quill.scroll.query(o,M.BLOCK)&&!Array.isArray(t.format[o])&&(i[o]=t.format[o]),i),{}),r=(new O).retain(e.index).delete(e.length).insert("\n",n);this.quill.updateContents(r,y.sources.USER),this.quill.setSelection(e.index+1,y.sources.SILENT),this.quill.focus()}}const zh={bindings:{bold:Fr("bold"),italic:Fr("italic"),underline:Fr("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(s,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","+1",y.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(s,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","-1",y.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(s,e){null!=e.format.indent?this.quill.format("indent","-1",y.sources.USER):null!=e.format.list&&this.quill.format("list",!1,y.sources.USER)}},"indent code-block":al(!0),"outdent code-block":al(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(s){this.quill.deleteText(s.index-1,1,y.sources.USER)}},tab:{key:"Tab",handler(s,e){if(e.format.table)return!0;this.quill.history.cutoff();const t=(new O).retain(s.index).delete(s.length).insert("\t");return this.quill.updateContents(t,y.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(s.index+1,y.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,y.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(s,e){const t={list:!1};e.format.indent&&(t.indent=!1),this.quill.formatLine(s.index,s.length,t,y.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(s){const[e,t]=this.quill.getLine(s.index),n={...e.formats(),list:"checked"},r=(new O).retain(s.index).insert("\n",n).retain(e.length()-t-1).retain(1,{list:"unchecked"});this.quill.updateContents(r,y.sources.USER),this.quill.setSelection(s.index+1,y.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(s,e){const[t,n]=this.quill.getLine(s.index),r=(new O).retain(s.index).insert("\n",e.format).retain(t.length()-n-1).retain(1,{header:null});this.quill.updateContents(r,y.sources.USER),this.quill.setSelection(s.index+1,y.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(s){const e=this.quill.getModule("table");if(e){const[t,n,r,i]=e.getTable(s),o=function Gh(s,e,t,n){return null==e.prev&&null==e.next?null==t.prev&&null==t.next?0===n?-1:1:null==t.prev?-1:1:null==e.prev?-1:null==e.next?1:null}(0,n,r,i);if(null==o)return;let a=t.offset();if(o<0){const u=(new O).retain(a).insert("\n");this.quill.updateContents(u,y.sources.USER),this.quill.setSelection(s.index+1,s.length,y.sources.SILENT)}else if(o>0){a+=t.length();const u=(new O).retain(a).insert("\n");this.quill.updateContents(u,y.sources.USER),this.quill.setSelection(a,y.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(s,e){const{event:t,line:n}=e,r=n.offset(this.quill.scroll);this.quill.setSelection(t.shiftKey?r-1:r+n.length(),y.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(s,e){if(null==this.quill.scroll.query("list"))return!0;const{length:t}=e.prefix,[n,r]=this.quill.getLine(s.index);if(r>t)return!0;let i;switch(e.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(s.index," ",y.sources.USER),this.quill.history.cutoff();const o=(new O).retain(s.index-r).delete(t+1).retain(n.length()-2-r).retain(1,{list:i});return this.quill.updateContents(o,y.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(s.index-t,y.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(s){const[e,t]=this.quill.getLine(s.index);let n=2,r=e;for(;null!=r&&r.length()<=1&&r.formats()["code-block"];)if(r=r.prev,n-=1,n<=0){const i=(new O).retain(s.index+e.length()-t-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,y.sources.USER),this.quill.setSelection(s.index-1,y.sources.SILENT),!1}return!0}},"embed left":er("ArrowLeft",!1),"embed left shift":er("ArrowLeft",!0),"embed right":er("ArrowRight",!1),"embed right shift":er("ArrowRight",!0),"table down":cl(!1),"table up":cl(!0)}};function al(s){return{key:"Tab",shiftKey:!s,format:{"code-block":!0},handler(e,t){let{event:n}=t;const r=this.quill.scroll.query("code-block"),{TAB:i}=r;if(0===e.length&&!n.shiftKey)return this.quill.insertText(e.index,i,y.sources.USER),void this.quill.setSelection(e.index+i.length,y.sources.SILENT);const o=0===e.length?this.quill.getLines(e.index,1):this.quill.getLines(e);let{index:a,length:u}=e;o.forEach((d,v)=>{s?(d.insertAt(0,i),0===v?a+=i.length:u+=i.length):d.domNode.textContent.startsWith(i)&&(d.deleteAt(0,i.length),0===v?a-=i.length:u-=i.length)}),this.quill.update(y.sources.USER),this.quill.setSelection(a,u,y.sources.SILENT)}}}function er(s,e){return{key:s,shiftKey:e,altKey:null,["ArrowLeft"===s?"prefix":"suffix"]:/^$/,handler(n){let{index:r}=n;"ArrowRight"===s&&(r+=n.length+1);const[i]=this.quill.getLeaf(r);return!(i instanceof ee&&("ArrowLeft"===s?e?this.quill.setSelection(n.index-1,n.length+1,y.sources.USER):this.quill.setSelection(n.index-1,y.sources.USER):e?this.quill.setSelection(n.index,n.length+1,y.sources.USER):this.quill.setSelection(n.index+n.length+1,y.sources.USER),1))}}}function Fr(s){return{key:s[0],shortKey:!0,handler(e,t){this.quill.format(s,!t.format[s],y.sources.USER)}}}function cl(s){return{key:s?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(e,t){const n=s?"prev":"next",r=t.line,i=r.parent[n];if(null!=i){if("table-row"===i.statics.blotName){let o=i.children.head,a=r;for(;null!=a.prev;)a=a.prev,o=o.next;const u=o.offset(this.quill.scroll)+Math.min(t.offset,o.length()-1);this.quill.setSelection(u,0,y.sources.USER)}}else{const o=r.table()[n];null!=o&&this.quill.setSelection(s?o.offset(this.quill.scroll)+o.length()-1:o.offset(this.quill.scroll),0,y.sources.USER)}return!1}}}function Hr(s){let{quill:e,range:t}=s;const n=e.getLines(t);let r={};if(n.length>1){const i=n[0].formats(),o=n[n.length-1].formats();r=O.AttributeMap.diff(o,i)||{}}e.deleteText(t,y.sources.USER),Object.keys(r).length>0&&e.formatLine(t.index,1,r,y.sources.USER),e.setSelection(t.index,y.sources.SILENT)}tr.DEFAULTS=zh;const Vh=/font-weight:\s*normal/,Wh=["P","OL","UL"],ul=s=>s&&Wh.includes(s.tagName),Qh=/\bmso-list:[^;]*ignore/i,Jh=/\bmso-list:[^;]*\bl(\d+)/i,tf=/\bmso-list:[^;]*\blevel(\d+)/i,rf=[function sf(s){"urn:schemas-microsoft-com:office:word"===s.documentElement.getAttribute("xmlns:w")&&(s=>{const e=Array.from(s.querySelectorAll("[style*=mso-list]")),t=[],n=[];e.forEach(o=>{(o.getAttribute("style")||"").match(Qh)?t.push(o):n.push(o)}),t.forEach(o=>o.parentNode?.removeChild(o));const r=s.documentElement.innerHTML,i=n.map(o=>((s,e)=>{const t=s.getAttribute("style"),n=t?.match(Jh);if(!n)return null;const r=Number(n[1]),i=t?.match(tf),o=i?Number(i[1]):1,a=new RegExp(`@list l${r}:level${o}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),u=e.match(a);return{id:r,indent:o,type:u&&"bullet"===u[1]?"bullet":"ordered",element:s}})(o,r)).filter(o=>o);for(;i.length;){const o=[];let a=i.shift();for(;a;)o.push(a),a=i.length&&i[0]?.element===a.element.nextElementSibling&&i[0].id===a.id?i.shift():null;const u=document.createElement("ul");o.forEach(S=>{const q=document.createElement("li");q.setAttribute("data-list",S.type),S.indent>1&&q.setAttribute("class","ql-indent-"+(S.indent-1)),q.innerHTML=S.element.innerHTML,u.appendChild(q)});const d=o[0]?.element,{parentNode:v}=d??{};d&&v?.replaceChild(u,d),o.slice(1).forEach(S=>{let{element:q}=S;v?.removeChild(q)})}})(s)},function Yh(s){s.querySelector('[id^="docs-internal-guid-"]')&&((s=>{Array.from(s.querySelectorAll('b[style*="font-weight"]')).filter(e=>e.getAttribute("style")?.match(Vh)).forEach(e=>{const t=s.createDocumentFragment();t.append(...e.childNodes),e.parentNode?.replaceChild(t,e)})})(s),(s=>{Array.from(s.querySelectorAll("br")).filter(e=>ul(e.previousElementSibling)&&ul(e.nextElementSibling)).forEach(e=>{e.parentNode?.removeChild(e)})})(s))}],of=yn("quill:clipboard"),af=[[Node.TEXT_NODE,function Af(s,e,t){let n=s.data;if("O:P"===s.parentElement?.tagName)return e.insert(n.trim());if(!fl(s)){if(0===n.trim().length&&n.includes("\n")&&!function hf(s,e){return s.previousElementSibling&&s.nextElementSibling&&!tn(s.previousElementSibling,e)&&!tn(s.nextElementSibling,e)}(s,t))return e;const r=(i,o)=>{const a=o.replace(/[^\u00a0]/g,"");return a.length<1&&i?" ":a};n=n.replace(/\r\n/g," ").replace(/\n/g," "),n=n.replace(/\s\s+/g,r.bind(r,!0)),(null==s.previousSibling&&null!=s.parentElement&&tn(s.parentElement,t)||s.previousSibling instanceof Element&&tn(s.previousSibling,t))&&(n=n.replace(/^\s+/,r.bind(r,!1))),(null==s.nextSibling&&null!=s.parentElement&&tn(s.parentElement,t)||s.nextSibling instanceof Element&&tn(s.nextSibling,t))&&(n=n.replace(/\s+$/,r.bind(r,!1)))}return e.insert(n)}],[Node.TEXT_NODE,dl],["br",function gf(s,e){return Rs(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,dl],[Node.ELEMENT_NODE,function df(s,e,t){const n=t.query(s);if(null==n)return e;if(n.prototype instanceof ee){const r={},i=n.value(s);if(null!=i)return r[n.blotName]=i,(new O).insert(r,n.formats(s,t))}else if(n.prototype instanceof qs&&!Rs(e,"\n")&&e.insert("\n"),"blotName"in n&&"formats"in n&&"function"==typeof n.formats)return An(e,n.blotName,n.formats(s,t),t);return e}],[Node.ELEMENT_NODE,function ff(s,e,t){const n=Te.keys(s),r=de.keys(s),i=Ye.keys(s),o={};return n.concat(r).concat(i).forEach(a=>{let u=t.query(a,M.ATTRIBUTE);null!=u&&(o[u.attrName]=u.value(s),o[u.attrName])||(u=cf[a],null!=u&&(u.attrName===a||u.keyName===a)&&(o[u.attrName]=u.value(s)||void 0),u=hl[a],null!=u&&(u.attrName===a||u.keyName===a)&&(u=hl[a],o[u.attrName]=u.value(s)||void 0))}),Object.entries(o).reduce((a,u)=>{let[d,v]=u;return An(a,d,v,t)},e)}],[Node.ELEMENT_NODE,function vf(s,e,t){const n={},r=s.style||{};return"italic"===r.fontStyle&&(n.italic=!0),"underline"===r.textDecoration&&(n.underline=!0),"line-through"===r.textDecoration&&(n.strike=!0),(r.fontWeight?.startsWith("bold")||parseInt(r.fontWeight,10)>=700)&&(n.bold=!0),e=Object.entries(n).reduce((i,o)=>{let[a,u]=o;return An(i,a,u,t)},e),parseFloat(r.textIndent||0)>0?(new O).insert("\t").concat(e):e}],["li",function bf(s,e,t){const n=t.query(s);if(null==n||"list"!==n.blotName||!Rs(e,"\n"))return e;let r=-1,i=s.parentNode;for(;null!=i;)["OL","UL"].includes(i.tagName)&&(r+=1),i=i.parentNode;return r<=0?e:e.reduce((o,a)=>a.insert?a.attributes&&"number"==typeof a.attributes.indent?o.push(a):o.insert(a.insert,{indent:r,...a.attributes||{}}):o,new O)}],["ol, ul",function yf(s,e,t){let r="OL"===s.tagName?"ordered":"bullet";const i=s.getAttribute("data-checked");return i&&(r="true"===i?"checked":"unchecked"),An(e,"list",r,t)}],["pre",function pf(s,e,t){const n=t.query("code-block");return An(e,"code-block",!n||!("formats"in n)||"function"!=typeof n.formats||n.formats(s,t),t)}],["tr",function _f(s,e,t){const n="TABLE"===s.parentElement?.tagName?s.parentElement:s.parentElement?.parentElement;return null!=n?An(e,"table",Array.from(n.querySelectorAll("tr")).indexOf(s)+1,t):e}],["b",zr("bold")],["i",zr("italic")],["strike",zr("strike")],["style",function mf(){return new O}]],cf=[Dh,tl].reduce((s,e)=>(s[e.keyName]=e,s),{}),hl=[Qi,Pr,jr,nl,il,ol].reduce((s,e)=>(s[e.keyName]=e,s),{});let uf=(()=>class s extends me{static DEFAULTS={matchers:[]};constructor(t,n){super(t,n),this.quill.root.addEventListener("copy",r=>this.onCaptureCopy(r,!1)),this.quill.root.addEventListener("cut",r=>this.onCaptureCopy(r,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],af.concat(this.options.matchers??[]).forEach(r=>{let[i,o]=r;this.addMatcher(i,o)})}addMatcher(t,n){this.matchers.push([t,n])}convert(t){let{html:n,text:r}=t,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(i[zt.blotName])return(new O).insert(r||"",{[zt.blotName]:i[zt.blotName]});if(!n)return(new O).insert(r||"",i);const o=this.convertHTML(n);return Rs(o,"\n")&&(null==o.ops[o.ops.length-1].attributes||i.table)?o.compose((new O).retain(o.length()-1).delete(1)):o}normalizeHTML(t){(s=>{s.documentElement&&rf.forEach(e=>{e(s)})})(t)}convertHTML(t){const n=(new DOMParser).parseFromString(t,"text/html");this.normalizeHTML(n);const r=n.body,i=new WeakMap,[o,a]=this.prepareMatching(r,i);return $r(this.quill.scroll,r,o,a,i)}dangerouslyPasteHTML(t,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y.sources.API;if("string"==typeof t){const i=this.convert({html:t,text:""});this.quill.setContents(i,n),this.quill.setSelection(0,y.sources.SILENT)}else{const i=this.convert({html:n,text:""});this.quill.updateContents((new O).retain(t).concat(i),r),this.quill.setSelection(t+i.length(),y.sources.SILENT)}}onCaptureCopy(t){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.defaultPrevented)return;t.preventDefault();const[r]=this.quill.selection.getRange();if(null==r)return;const{html:i,text:o}=this.onCopy(r,n);t.clipboardData?.setData("text/plain",o),t.clipboardData?.setData("text/html",i),n&&Hr({range:r,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(n=>"#"!==n[0]).join("\n")}onCapturePaste(t){if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const n=this.quill.getSelection(!0);if(null==n)return;const r=t.clipboardData?.getData("text/html");let i=t.clipboardData?.getData("text/plain");if(!r&&!i){const a=t.clipboardData?.getData("text/uri-list");a&&(i=this.normalizeURIList(a))}const o=Array.from(t.clipboardData?.files||[]);if(!r&&o.length>0)this.quill.uploader.upload(n,o);else{if(r&&o.length>0){const a=(new DOMParser).parseFromString(r,"text/html");if(1===a.body.childElementCount&&"IMG"===a.body.firstElementChild?.tagName)return void this.quill.uploader.upload(n,o)}this.onPaste(n,{html:r,text:i})}}onCopy(t){const n=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:n}}onPaste(t,n){let{text:r,html:i}=n;const o=this.quill.getFormat(t.index),a=this.convert({text:r,html:i},o);of.log("onPaste",a,{text:r,html:i});const u=(new O).retain(t.index).delete(t.length).concat(a);this.quill.updateContents(u,y.sources.USER),this.quill.setSelection(u.length()-t.length,y.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,n){const r=[],i=[];return this.matchers.forEach(o=>{const[a,u]=o;switch(a){case Node.TEXT_NODE:i.push(u);break;case Node.ELEMENT_NODE:r.push(u);break;default:Array.from(t.querySelectorAll(a)).forEach(d=>{n.has(d)?n.get(d)?.push(u):n.set(d,[u])})}}),[r,i]}})();function An(s,e,t,n){return n.query(e)?s.reduce((r,i)=>i.insert?i.attributes&&i.attributes[e]?r.push(i):r.insert(i.insert,{...t?{[e]:t}:{},...i.attributes}):r,new O):s}function Rs(s,e){let t="";for(let n=s.ops.length-1;n>=0&&t.length<e.length;--n){const r=s.ops[n];if("string"!=typeof r.insert)break;t=r.insert+t}return t.slice(-1*e.length)===e}function tn(s,e){if(!(s instanceof Element))return!1;const t=e.query(s);return!(t&&t.prototype instanceof ee)&&["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(s.tagName.toLowerCase())}const nr=new WeakMap;function fl(s){return null!=s&&(nr.has(s)||nr.set(s,"PRE"===s.tagName||fl(s.parentNode)),nr.get(s))}function $r(s,e,t,n,r){return e.nodeType===e.TEXT_NODE?n.reduce((i,o)=>o(e,i,s),new O):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce((i,o)=>{let a=$r(s,o,t,n,r);return o.nodeType===e.ELEMENT_NODE&&(a=t.reduce((u,d)=>d(o,u,s),a),a=(r.get(o)||[]).reduce((u,d)=>d(o,u,s),a)),i.concat(a)},new O):new O}function zr(s){return(e,t,n)=>An(t,s,!0,n)}function dl(s,e,t){if(!Rs(e,"\n")){if(tn(s,t)&&(s.childNodes.length>0||s instanceof HTMLParagraphElement))return e.insert("\n");if(e.length()>0&&s.nextSibling){let n=s.nextSibling;for(;null!=n;){if(tn(n,t))return e.insert("\n");const r=t.query(n);if(r&&r.prototype instanceof oe)return e.insert("\n");n=n.firstChild}}}return e}let Ef=(()=>class s extends me{static DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};lastRecorded=0;ignoreChange=!1;stack={undo:[],redo:[]};currentRange=null;constructor(t,n){super(t,n),this.quill.on(y.events.EDITOR_CHANGE,(r,i,o,a)=>{r===y.events.SELECTION_CHANGE?i&&a!==y.sources.SILENT&&(this.currentRange=i):r===y.events.TEXT_CHANGE&&(this.ignoreChange||(this.options.userOnly&&a!==y.sources.USER?this.transform(i):this.record(i,o)),this.currentRange=Kr(this.currentRange,i))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",r=>{"historyUndo"===r.inputType?(this.undo(),r.preventDefault()):"historyRedo"===r.inputType&&(this.redo(),r.preventDefault())})}change(t,n){if(0===this.stack[t].length)return;const r=this.stack[t].pop();if(!r)return;const i=this.quill.getContents(),o=r.delta.invert(i);this.stack[n].push({delta:o,range:Kr(r.range,o)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(r.delta,y.sources.USER),this.ignoreChange=!1,this.restoreSelection(r)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(t,n){if(0===t.ops.length)return;this.stack.redo=[];let r=t.invert(n),i=this.currentRange;const o=Date.now();if(this.lastRecorded+this.options.delay>o&&this.stack.undo.length>0){const a=this.stack.undo.pop();a&&(r=r.compose(a.delta),i=a.range)}else this.lastRecorded=o;0!==r.length()&&(this.stack.undo.push({delta:r,range:i}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(t){gl(this.stack.undo,t),gl(this.stack.redo,t)}undo(){this.change("undo","redo")}restoreSelection(t){if(t.range)this.quill.setSelection(t.range,y.sources.USER);else{const n=function Nf(s,e){const t=e.reduce((r,i)=>r+(i.delete||0),0);let n=e.length()-t;return function Tf(s,e){const t=e.ops[e.ops.length-1];return null!=t&&(null!=t.insert?"string"==typeof t.insert&&t.insert.endsWith("\n"):null!=t.attributes&&Object.keys(t.attributes).some(n=>null!=s.query(n,M.BLOCK)))}(s,e)&&(n-=1),n}(this.quill.scroll,t.delta);this.quill.setSelection(n,y.sources.USER)}}})();function gl(s,e){let t=e;for(let n=s.length-1;n>=0;n-=1){const r=s[n];s[n]={delta:t.transform(r.delta,!0),range:r.range&&Kr(r.range,t)},t=r.delta.transform(t),0===s[n].delta.length()&&s.splice(n,1)}}function Kr(s,e){if(!s)return s;const t=e.transformPosition(s.index);return{index:t,length:e.transformPosition(s.index+s.length)-t}}const wf=(()=>{class s extends me{constructor(t,n){super(t,n),t.root.addEventListener("drop",r=>{r.preventDefault();let i=null;if(document.caretRangeFromPoint)i=document.caretRangeFromPoint(r.clientX,r.clientY);else if(document.caretPositionFromPoint){const a=document.caretPositionFromPoint(r.clientX,r.clientY);i=document.createRange(),i.setStart(a.offsetNode,a.offset),i.setEnd(a.offsetNode,a.offset)}const o=i&&t.selection.normalizeNative(i);if(o){const a=t.selection.normalizedToRange(o);r.dataTransfer?.files&&this.upload(a,r.dataTransfer.files)}})}upload(t,n){const r=[];Array.from(n).forEach(i=>{i&&this.options.mimetypes?.includes(i.type)&&r.push(i)}),r.length>0&&this.options.handler.call(this,t,r)}}return s.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(e,t){if(!this.quill.scroll.query("image"))return;const n=t.map(r=>new Promise(i=>{const o=new FileReader;o.onload=()=>{i(o.result)},o.readAsDataURL(r)}));Promise.all(n).then(r=>{const i=r.reduce((o,a)=>o.insert({image:a}),(new O).retain(e.index).delete(e.length));this.quill.updateContents(i,B.sources.USER),this.quill.setSelection(e.index+r.length,B.sources.SILENT)})}},s})(),xf=["insertText","insertReplacementText"],Of=/Mac/i.test(navigator.platform);y.register({"blots/block":ne,"blots/block/embed":oe,"blots/break":Be,"blots/container":vn,"blots/cursor":Zs,"blots/embed":Rr,"blots/inline":Qe,"blots/scroll":Mh,"blots/text":pe,"modules/clipboard":uf,"modules/history":Ef,"modules/keyboard":tr,"modules/uploader":wf,"modules/input":class Sf extends me{constructor(e,t){super(e,t),e.root.addEventListener("beforeinput",n=>{this.handleBeforeInput(n)}),/Android/i.test(navigator.userAgent)||e.on(y.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(e){Hr({range:e,quill:this.quill})}replaceText(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(0===e.length)return!1;if(t){const n=this.quill.getFormat(e.index,1);this.deleteRange(e),this.quill.updateContents((new O).retain(e.index).insert(t,n),y.sources.USER)}else this.deleteRange(e);return this.quill.setSelection(e.index+t.length,0,y.sources.SILENT),!0}handleBeforeInput(e){if(this.quill.composition.isComposing||e.defaultPrevented||!xf.includes(e.inputType))return;const t=e.getTargetRanges?e.getTargetRanges()[0]:null;if(!t||!0===t.collapsed)return;const n=function Lf(s){return"string"==typeof s.data?s.data:s.dataTransfer?.types.includes("text/plain")?s.dataTransfer.getData("text/plain"):null}(e);if(null==n)return;const r=this.quill.selection.normalizeNative(t),i=r?this.quill.selection.normalizedToRange(r):null;i&&this.replaceText(i,n)&&e.preventDefault()}handleCompositionStart(){const e=this.quill.getSelection();e&&this.replaceText(e)}},"modules/uiNode":class kf extends me{isListening=!1;selectionChangeDeadline=0;constructor(e,t){super(e,t),this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,t){let{line:n,event:r}=t;if(!(n instanceof ge&&n.uiNode))return!0;const i="rtl"===getComputedStyle(n.domNode).direction;return!!(i&&"ArrowRight"!==r.key||!i&&"ArrowLeft"!==r.key)||(this.quill.setSelection(e.index-1,e.length+(r.shiftKey?1:0),y.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&(s=>!!("ArrowLeft"===s.key||"ArrowRight"===s.key||"ArrowUp"===s.key||"ArrowDown"===s.key||"Home"===s.key||Of&&"a"===s.key&&!0===s.ctrlKey))(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){this.selectionChangeDeadline=Date.now()+100,this.isListening||(this.isListening=!0,document.addEventListener("selectionchange",()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()},{once:!0}))}handleSelectionChange(){const e=document.getSelection();if(!e)return;const t=e.getRangeAt(0);if(!0!==t.collapsed||0!==t.startOffset)return;const n=this.quill.scroll.find(t.startContainer);if(!(n instanceof ge&&n.uiNode))return;const r=document.createRange();r.setStartAfter(n.uiNode),r.setEndAfter(n.uiNode),e.removeAllRanges(),e.addRange(r)}}});const Gr=y,Mf=new class Bf extends de{add(e,t){let n=0;if("+1"===t||"-1"===t){const r=this.value(e)||0;n="+1"===t?r+1:r-1}else"number"==typeof t&&(n=t);return 0===n?(this.remove(e),!0):super.add(e,n.toString())}canAdd(e,t){return super.canAdd(e,t)||super.canAdd(e,parseInt(t,10))}value(e){return parseInt(super.value(e),10)||void 0}}("indent","ql-indent",{scope:M.BLOCK,whitelist:[1,2,3,4,5,6,7,8]}),Df=(()=>class s extends ne{static blotName="blockquote";static tagName="blockquote"})(),jf=(()=>class s extends ne{static blotName="header";static tagName=["H1","H2","H3","H4","H5","H6"];static formats(t){return this.tagName.indexOf(t.tagName)+1}})();let Vr=(()=>{class s extends vn{}return s.blotName="list-container",s.tagName="OL",s})(),Wr=(()=>{class s extends ne{static create(t){const n=super.create();return n.setAttribute("data-list",t),n}static formats(t){return t.getAttribute("data-list")||void 0}static register(){y.register(Vr)}constructor(t,n){super(t,n);const r=n.ownerDocument.createElement("span"),i=o=>{if(!t.isEnabled())return;const a=this.statics.formats(n,t);"checked"===a?(this.format("list","unchecked"),o.preventDefault()):"unchecked"===a&&(this.format("list","checked"),o.preventDefault())};r.addEventListener("mousedown",i),r.addEventListener("touchstart",i),this.attachUI(r)}format(t,n){t===this.statics.blotName&&n?this.domNode.setAttribute("data-list",n):super.format(t,n)}}return s.blotName="list",s.tagName="LI",s})();Vr.allowedChildren=[Wr],Wr.requiredContainer=Vr;const Zr=(()=>class s extends Qe{static blotName="bold";static tagName=["STRONG","B"];static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}})(),Pf=(()=>class s extends Zr{static blotName="italic";static tagName=["EM","I"]})();let sr=(()=>class s extends Qe{static blotName="link";static tagName="A";static SANITIZED_URL="about:blank";static PROTOCOL_WHITELIST=["http","https","mailto","tel","sms"];static create(t){const n=super.create(t);return n.setAttribute("href",this.sanitize(t)),n.setAttribute("rel","noopener noreferrer"),n.setAttribute("target","_blank"),n}static formats(t){return t.getAttribute("href")}static sanitize(t){return pl(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,n){t===this.statics.blotName&&n?this.domNode.setAttribute("href",this.constructor.sanitize(n)):super.format(t,n)}})();function pl(s,e){const t=document.createElement("a");t.href=s;const n=t.href.slice(0,t.href.indexOf(":"));return e.indexOf(n)>-1}const Uf=(()=>class s extends Qe{static blotName="script";static tagName=["SUB","SUP"];static create(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):super.create(t)}static formats(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}})(),Ff=(()=>class s extends Zr{static blotName="strike";static tagName=["S","STRIKE"]})(),Hf=(()=>class s extends Qe{static blotName="underline";static tagName="U"})(),$f=(()=>class s extends Rr{static blotName="formula";static className="ql-formula";static tagName="SPAN";static create(t){if(null==window.katex)throw new Error("Formula module requires KaTeX.");const n=super.create(t);return"string"==typeof t&&(window.katex.render(t,n,{throwOnError:!1,errorColor:"#f00"}),n.setAttribute("data-value",t)),n}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}})(),ml=["alt","height","width"],zf=(()=>class s extends ee{static blotName="image";static tagName="IMG";static create(t){const n=super.create(t);return"string"==typeof t&&n.setAttribute("src",this.sanitize(t)),n}static formats(t){return ml.reduce((n,r)=>(t.hasAttribute(r)&&(n[r]=t.getAttribute(r)),n),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return pl(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,n){ml.indexOf(t)>-1?n?this.domNode.setAttribute(t,n):this.domNode.removeAttribute(t):super.format(t,n)}})(),bl=["height","width"],Kf=(()=>class s extends oe{static blotName="video";static className="ql-video";static tagName="IFRAME";static create(t){const n=super.create(t);return n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen","true"),n.setAttribute("src",this.sanitize(t)),n}static formats(t){return bl.reduce((n,r)=>(t.hasAttribute(r)&&(n[r]=t.getAttribute(r)),n),{})}static sanitize(t){return sr.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,n){bl.indexOf(t)>-1?n?this.domNode.setAttribute(t,n):this.domNode.removeAttribute(t):super.format(t,n)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}})(),Bs=new de("code-token","hljs",{scope:M.INLINE});let Ms=(()=>{class s extends Qe{static formats(t,n){for(;null!=t&&t!==n.domNode;){if(t.classList&&t.classList.contains(zt.className))return super.formats(t,n);t=t.parentNode}}constructor(t,n,r){super(t,n,r),Bs.add(this.domNode,r)}format(t,n){t!==s.blotName?super.format(t,n):n?Bs.add(this.domNode,n):(Bs.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),Bs.value(this.domNode)||this.unwrap()}}return s.blotName="code-token",s.className="ql-token",s})();class ce extends zt{static create(e){const t=super.create(e);return"string"==typeof e&&t.setAttribute("data-language",e),t}static formats(e){return e.getAttribute("data-language")||"plain"}static register(){}format(e,t){e===this.statics.blotName&&t?this.domNode.setAttribute("data-language",t):super.format(e,t)}replaceWith(e,t){return this.formatAt(0,this.length(),Ms.blotName,!1),super.replaceWith(e,t)}}let rr=(()=>{class s extends _n{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,n){t===ce.blotName&&(this.forceNext=!0,this.children.forEach(r=>{r.format(t,n)}))}formatAt(t,n,r,i){r===ce.blotName&&(this.forceNext=!0),super.formatAt(t,n,r,i)}highlight(t){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==this.children.head)return;const i=`${Array.from(this.domNode.childNodes).filter(a=>a!==this.uiNode).map(a=>a.textContent).join("\n")}\n`,o=ce.formats(this.children.head.domNode);if(n||this.forceNext||this.cachedText!==i){if(i.trim().length>0||null==this.cachedText){const a=this.children.reduce((d,v)=>d.concat(Fi(v,!1)),new O),u=t(i,o);a.diff(u).reduce((d,v)=>{let{retain:S,attributes:q}=v;return S?(q&&Object.keys(q).forEach(W=>{[ce.blotName,Ms.blotName].includes(W)&&this.formatAt(d,S,W,q[W])}),d+S):d},0)}this.cachedText=i,this.forceNext=!1}}html(t,n){const[r]=this.children.find(t);return`<pre data-language="${r?ce.formats(r.domNode):"plain"}">\n${Ws(this.code(t,n))}\n</pre>`}optimize(t){if(super.optimize(t),null!=this.parent&&null!=this.children.head&&null!=this.uiNode){const n=ce.formats(this.children.head.domNode);n!==this.uiNode.value&&(this.uiNode.value=n)}}}return s.allowedChildren=[ce],s})();ce.requiredContainer=rr,ce.allowedChildren=[Ms,Zs,pe,Be];class yl extends me{static register(){y.register(Ms,!0),y.register(ce,!0),y.register(rr,!0)}constructor(e,t){if(super(e,t),null==this.options.hljs)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((n,r)=>{let{key:i}=r;return n[i]=!0,n},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(y.events.SCROLL_BLOT_MOUNT,e=>{if(!(e instanceof rr))return;const t=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(n=>{let{key:r,label:i}=n;const o=t.ownerDocument.createElement("option");o.textContent=i,o.setAttribute("value",r),t.appendChild(o)}),t.addEventListener("change",()=>{e.format(ce.blotName,t.value),this.quill.root.focus(),this.highlight(e,!0)}),null==e.uiNode&&(e.attachUI(t),e.children.head&&(t.value=ce.formats(e.children.head.domNode)))})}initTimer(){let e=null;this.quill.on(y.events.SCROLL_OPTIMIZE,()=>{e&&clearTimeout(e),e=setTimeout(()=>{this.highlight(),e=null},this.options.interval)})}highlight(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.quill.selection.composing)return;this.quill.update(y.sources.USER);const n=this.quill.getSelection();(null==e?this.quill.scroll.descendants(rr):[e]).forEach(i=>{i.highlight(this.highlightBlot,t)}),this.quill.update(y.sources.SILENT),null!=n&&this.quill.setSelection(n,y.sources.SILENT)}highlightBlot(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"plain";if(t=this.languages[t]?t:"plain","plain"===t)return Ws(e).split("\n").reduce((r,i,o)=>(0!==o&&r.insert("\n",{[zt.blotName]:t}),r.insert(i)),new O);const n=this.quill.root.ownerDocument.createElement("div");return n.classList.add(zt.className),n.innerHTML=((s,e,t)=>{if("string"==typeof s.versionString){const n=s.versionString.split(".")[0];if(parseInt(n,10)>=11)return s.highlight(t,{language:e}).value}return s.highlight(e,t).value})(this.options.hljs,t,e),$r(this.quill.scroll,n,[(r,i)=>{const o=Bs.value(r);return o?i.compose((new O).retain(i.length(),{[Ms.blotName]:o})):i}],[(r,i)=>r.data.split("\n").reduce((o,a,u)=>(0!==u&&o.insert("\n",{[zt.blotName]:t}),o.insert(a)),i)],new WeakMap)}}yl.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};let De=(()=>{class s extends ne{static blotName="table";static tagName="TD";static create(t){const n=super.create();return n.setAttribute("data-row",t||Yr()),n}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,n){t===s.blotName&&n?this.domNode.setAttribute("data-row",n):super.format(t,n)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}}return s})(),En=(()=>class s extends vn{static blotName="table-row";static tagName="TR";checkMerge(){if(super.checkMerge()&&null!=this.next.children.head){const t=this.children.head.formats(),n=this.children.tail.formats(),r=this.next.children.head.formats(),i=this.next.children.tail.formats();return t.table===n.table&&t.table===r.table&&t.table===i.table}return!1}optimize(t){super.optimize(t),this.children.forEach(n=>{if(null==n.next)return;const r=n.formats(),i=n.next.formats();if(r.table!==i.table){const o=this.splitAfter(n);o&&o.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}})(),en=(()=>class s extends vn{static blotName="table-body";static tagName="TBODY"})(),Xr=(()=>{class s extends vn{static blotName="table-container";static tagName="TABLE";balanceCells(){const t=this.descendants(En),n=t.reduce((r,i)=>Math.max(i.children.length,r),0);t.forEach(r=>{new Array(n-r.children.length).fill(0).forEach(()=>{let i;null!=r.children.head&&(i=De.formats(r.children.head.domNode));const o=this.scroll.create(De.blotName,i);r.appendChild(o),o.optimize()})})}cells(t){return this.rows().map(n=>n.children.at(t))}deleteColumn(t){const[n]=this.descendant(en);null==n||null==n.children.head||n.children.forEach(r=>{const i=r.children.at(t);i?.remove()})}insertColumn(t){const[n]=this.descendant(en);null==n||null==n.children.head||n.children.forEach(r=>{const i=r.children.at(t),o=De.formats(r.children.head.domNode),a=this.scroll.create(De.blotName,o);r.insertBefore(a,i)})}insertRow(t){const[n]=this.descendant(en);if(null==n||null==n.children.head)return;const r=Yr(),i=this.scroll.create(En.blotName);n.children.head.children.forEach(()=>{const a=this.scroll.create(De.blotName,r);i.appendChild(a)});const o=n.children.at(t);n.insertBefore(i,o)}rows(){const t=this.children.head;return null==t?[]:t.children.map(n=>n)}}return s.allowedChildren=[en],s})();function Yr(){return`row-${Math.random().toString(36).slice(2,6)}`}en.requiredContainer=Xr,en.allowedChildren=[En],En.requiredContainer=en,En.allowedChildren=[De],De.requiredContainer=En;const vl=yn("quill:toolbar");let _l=(()=>{class s extends me{constructor(t,n){if(super(t,n),Array.isArray(this.options.container)){const r=document.createElement("div");r.setAttribute("role","toolbar"),function Zf(s,e){Array.isArray(e[0])||(e=[e]),e.forEach(t=>{const n=document.createElement("span");n.classList.add("ql-formats"),t.forEach(r=>{if("string"==typeof r)Al(n,r);else{const i=Object.keys(r)[0],o=r[i];Array.isArray(o)?function Xf(s,e,t){const n=document.createElement("select");n.classList.add(`ql-${e}`),t.forEach(r=>{const i=document.createElement("option");!1!==r?i.setAttribute("value",String(r)):i.setAttribute("selected","selected"),n.appendChild(i)}),s.appendChild(n)}(n,i,o):Al(n,i,o)}}),s.appendChild(n)})}(r,this.options.container),t.container?.parentNode?.insertBefore(r,t.container),this.container=r}else this.container="string"==typeof this.options.container?document.querySelector(this.options.container):this.options.container;this.container instanceof HTMLElement?(this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(r=>{const i=this.options.handlers?.[r];i&&this.addHandler(r,i)}),Array.from(this.container.querySelectorAll("button, select")).forEach(r=>{this.attach(r)}),this.quill.on(y.events.EDITOR_CHANGE,()=>{const[r]=this.quill.selection.getRange();this.update(r)})):vl.error("Container required for toolbar",this.options)}addHandler(t,n){this.handlers[t]=n}attach(t){let n=Array.from(t.classList).find(i=>0===i.indexOf("ql-"));n&&(n=n.slice(3),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null!=this.handlers[n]||null!=this.quill.scroll.query(n)?(t.addEventListener("SELECT"===t.tagName?"change":"click",i=>{let o;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;const u=t.options[t.selectedIndex];o=!u.hasAttribute("selected")&&(u.value||!1)}else o=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),i.preventDefault();this.quill.focus();const[a]=this.quill.selection.getRange();if(null!=this.handlers[n])this.handlers[n].call(this,o);else if(this.quill.scroll.query(n).prototype instanceof ee){if(o=prompt(`Enter ${n}`),!o)return;this.quill.updateContents((new O).retain(a.index).delete(a.length).insert({[n]:o}),y.sources.USER)}else this.quill.format(n,o,y.sources.USER);this.update(a)}),this.controls.push([n,t])):vl.warn("ignoring attaching to nonexistent format",n,t))}update(t){const n=null==t?{}:this.quill.getFormat(t);this.controls.forEach(r=>{const[i,o]=r;if("SELECT"===o.tagName){let a=null;if(null==t)a=null;else if(null==n[i])a=o.querySelector("option[selected]");else if(!Array.isArray(n[i])){let u=n[i];"string"==typeof u&&(u=u.replace(/"/g,'\\"')),a=o.querySelector(`option[value="${u}"]`)}null==a?(o.value="",o.selectedIndex=-1):a.selected=!0}else if(null==t)o.classList.remove("ql-active"),o.setAttribute("aria-pressed","false");else if(o.hasAttribute("value")){const a=n[i],u=a===o.getAttribute("value")||null!=a&&a.toString()===o.getAttribute("value")||null==a&&!o.getAttribute("value");o.classList.toggle("ql-active",u),o.setAttribute("aria-pressed",u.toString())}else{const a=null!=n[i];o.classList.toggle("ql-active",a),o.setAttribute("aria-pressed",a.toString())}})}}return s.DEFAULTS={},s})();function Al(s,e,t){const n=document.createElement("button");n.setAttribute("type","button"),n.classList.add(`ql-${e}`),n.setAttribute("aria-pressed","false"),null!=t?(n.value=t,n.setAttribute("aria-label",`${e}: ${t}`)):n.setAttribute("aria-label",e),s.appendChild(n)}_l.DEFAULTS={container:null,handlers:{clean(){const s=this.quill.getSelection();if(null!=s)if(0===s.length){const e=this.quill.getFormat();Object.keys(e).forEach(t=>{null!=this.quill.scroll.query(t,M.INLINE)&&this.quill.format(t,!1,y.sources.USER)})}else this.quill.removeFormat(s.index,s.length,y.sources.USER)},direction(s){const{align:e}=this.quill.getFormat();"rtl"===s&&null==e?this.quill.format("align","right",y.sources.USER):!s&&"right"===e&&this.quill.format("align",!1,y.sources.USER),this.quill.format("direction",s,y.sources.USER)},indent(s){const e=this.quill.getSelection(),t=this.quill.getFormat(e),n=parseInt(t.indent||0,10);if("+1"===s||"-1"===s){let r="+1"===s?1:-1;"rtl"===t.direction&&(r*=-1),this.quill.format("indent",n+r,y.sources.USER)}},link(s){!0===s&&(s=prompt("Enter link URL:")),this.quill.format("link",s,y.sources.USER)},list(s){const e=this.quill.getSelection(),t=this.quill.getFormat(e);this.quill.format("list","check"===s?"checked"!==t.list&&"unchecked"!==t.list&&"unchecked":s,y.sources.USER)}}};const El='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',Ds={align:{"":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',center:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',right:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',justify:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>'},background:'<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',blockquote:'<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',bold:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',clean:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',code:El,"code-block":El,color:'<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',direction:{"":'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',rtl:'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>'},formula:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',header:{1:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',2:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',3:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',4:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',5:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',6:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>'},italic:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',image:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',indent:{"+1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',"-1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>'},link:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',list:{bullet:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',check:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',ordered:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>'},script:{sub:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',super:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>'},strike:'<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',table:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',underline:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',video:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>'};let Tl=0;function Nl(s,e){s.setAttribute(e,`${"true"!==s.getAttribute(e)}`)}const ir=class ld{constructor(e){this.select=e,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",t=>{switch(t.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),t.preventDefault()}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),Nl(this.label,"aria-expanded"),Nl(this.options,"aria-hidden")}buildItem(e){const t=document.createElement("span");t.tabIndex="0",t.setAttribute("role","button"),t.classList.add("ql-picker-item");const n=e.getAttribute("value");return n&&t.setAttribute("data-value",n),e.textContent&&t.setAttribute("data-label",e.textContent),t.addEventListener("click",()=>{this.selectItem(t,!0)}),t.addEventListener("keydown",r=>{switch(r.key){case"Enter":this.selectItem(t,!0),r.preventDefault();break;case"Escape":this.escape(),r.preventDefault()}}),t}buildLabel(){const e=document.createElement("span");return e.classList.add("ql-picker-label"),e.innerHTML='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>',e.tabIndex="0",e.setAttribute("role","button"),e.setAttribute("aria-expanded","false"),this.container.appendChild(e),e}buildOptions(){const e=document.createElement("span");e.classList.add("ql-picker-options"),e.setAttribute("aria-hidden","true"),e.tabIndex="-1",e.id=`ql-picker-options-${Tl}`,Tl+=1,this.label.setAttribute("aria-controls",e.id),this.options=e,Array.from(this.select.options).forEach(t=>{const n=this.buildItem(t);e.appendChild(n),!0===t.selected&&this.selectItem(n)}),this.container.appendChild(e)}buildPicker(){Array.from(this.select.attributes).forEach(e=>{this.container.setAttribute(e.name,e.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.container.querySelector(".ql-selected");e!==n&&(n?.classList.remove("ql-selected"),null!=e&&(e.classList.add("ql-selected"),this.select.selectedIndex=Array.from(e.parentNode.children).indexOf(e),e.hasAttribute("data-value")?this.label.setAttribute("data-value",e.getAttribute("data-value")):this.label.removeAttribute("data-value"),e.hasAttribute("data-label")?this.label.setAttribute("data-label",e.getAttribute("data-label")):this.label.removeAttribute("data-label"),t&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let e;if(this.select.selectedIndex>-1){const n=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];e=this.select.options[this.select.selectedIndex],this.selectItem(n)}else this.selectItem(null);const t=null!=e&&e!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",t)}},wl=class od extends ir{constructor(e,t){super(e),this.label.innerHTML=t,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(n=>{n.classList.add("ql-primary")})}buildItem(e){const t=super.buildItem(e);return t.style.backgroundColor=e.getAttribute("value")||"",t}selectItem(e,t){super.selectItem(e,t);const n=this.label.querySelector(".ql-color-label"),r=e&&e.getAttribute("data-value")||"";n&&("line"===n.tagName?n.style.stroke=r:n.style.fill=r)}},xl=class ad extends ir{constructor(e,t){super(e),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(n=>{n.innerHTML=t[n.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(e,t){super.selectItem(e,t);const n=e||this.defaultItem;if(null!=n){if(this.label.innerHTML===n.innerHTML)return;this.label.innerHTML=n.innerHTML}}},Sl=class ud{constructor(e,t){this.quill=e,this.boundsContainer=t||document.body,this.root=e.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,(s=>{const{overflowY:e}=getComputedStyle(s,null);return"visible"!==e&&"clip"!==e})(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=-1*this.quill.root.scrollTop+"px"}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(e){const t=e.left+e.width/2-this.root.offsetWidth/2,n=e.bottom+this.quill.root.scrollTop;this.root.style.left=`${t}px`,this.root.style.top=`${n}px`,this.root.classList.remove("ql-flip");const r=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect();let o=0;return i.right>r.right&&(o=r.right-i.right,this.root.style.left=`${t+o}px`),i.left<r.left&&(o=r.left-i.left,this.root.style.left=`${t+o}px`),i.bottom>r.bottom&&(this.root.style.top=n-(e.bottom-e.top+(i.bottom-i.top))+"px",this.root.classList.add("ql-flip")),o}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}},hd=[!1,"center","right","justify"],fd=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],dd=[!1,"serif","monospace"],gd=["1","2","3",!1],pd=["small",!1,"large","huge"];class js extends Ys{constructor(e,t){super(e,t);const n=r=>{document.body.contains(e.root)?(null!=this.tooltip&&!this.tooltip.root.contains(r.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),null!=this.pickers&&this.pickers.forEach(i=>{i.container.contains(r.target)||i.close()})):document.body.removeEventListener("click",n)};e.emitter.listenDOM("click",document.body,n)}addModule(e){const t=super.addModule(e);return"toolbar"===e&&this.extendToolbar(t),t}buildButtons(e,t){Array.from(e).forEach(n=>{(n.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&(i=i.slice(3),null!=t[i]))if("direction"===i)n.innerHTML=t[i][""]+t[i].rtl;else if("string"==typeof t[i])n.innerHTML=t[i];else{const o=n.value||"";null!=o&&t[i][o]&&(n.innerHTML=t[i][o])}})})}buildPickers(e,t){this.pickers=Array.from(e).map(r=>{if(r.classList.contains("ql-align")&&(null==r.querySelector("option")&&Ps(r,hd),"object"==typeof t.align))return new xl(r,t.align);if(r.classList.contains("ql-background")||r.classList.contains("ql-color")){const i=r.classList.contains("ql-background")?"background":"color";return null==r.querySelector("option")&&Ps(r,fd,"background"===i?"#ffffff":"#000000"),new wl(r,t[i])}return null==r.querySelector("option")&&(r.classList.contains("ql-font")?Ps(r,dd):r.classList.contains("ql-header")?Ps(r,gd):r.classList.contains("ql-size")&&Ps(r,pd)),new ir(r)}),this.quill.on(B.events.EDITOR_CHANGE,()=>{this.pickers.forEach(r=>{r.update()})})}}js.DEFAULTS=Xe({},Ys.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let s=this.container.querySelector("input.ql-image[type=file]");null==s&&(s=document.createElement("input"),s.setAttribute("type","file"),s.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),s.classList.add("ql-image"),s.addEventListener("change",()=>{const e=this.quill.getSelection(!0);this.quill.uploader.upload(e,s.files),s.value=""}),this.container.appendChild(s)),s.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class Ll extends Sl{constructor(e,t){super(e,t),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",e=>{"Enter"===e.key?(this.save(),e.preventDefault()):"Escape"===e.key&&(this.cancel(),e.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null==this.textbox)return;null!=t?this.textbox.value=t:e!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const n=this.quill.getBounds(this.quill.selection.savedRange);null!=n&&this.position(n),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${e}`)||""),this.root.setAttribute("data-mode",e)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:e}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:t}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",e,B.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",e,B.sources.USER)),this.quill.root.scrollTop=t;break}case"video":e=function md(s){let e=s.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||s.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return e?`${e[1]||"https"}://www.youtube.com/embed/${e[2]}?showinfo=0`:(e=s.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${e[1]||"https"}://player.vimeo.com/video/${e[2]}/`:s}(e);case"formula":{if(!e)break;const t=this.quill.getSelection(!0);if(null!=t){const n=t.index+t.length;this.quill.insertEmbed(n,this.root.getAttribute("data-mode"),e,B.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(n+1," ",B.sources.USER),this.quill.setSelection(n+2,B.sources.USER)}break}}this.textbox.value="",this.hide()}}function Ps(s,e){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach(n=>{const r=document.createElement("option");n===t?r.setAttribute("selected","selected"):r.setAttribute("value",String(n)),s.appendChild(r)})}const bd=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class yd extends Ll{static TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join("");constructor(e,t){super(e,t),this.quill.on(B.events.EDITOR_CHANGE,(n,r,i,o)=>{if(n===B.events.SELECTION_CHANGE)if(null!=r&&r.length>0&&o===B.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const a=this.quill.getLines(r.index,r.length);if(1===a.length){const u=this.quill.getBounds(r);null!=u&&this.position(u)}else{const u=a[a.length-1],d=this.quill.getIndex(u),v=Math.min(u.length()-1,r.index+r.length-d),S=this.quill.getBounds(new Je(d,v));null!=S&&this.position(S)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(B.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;const e=this.quill.getSelection();if(null!=e){const t=this.quill.getBounds(e);null!=t&&this.position(t)}},1)})}cancel(){this.show()}position(e){const t=super.position(e),n=this.root.querySelector(".ql-tooltip-arrow");return n.style.marginLeft="",0!==t&&(n.style.marginLeft=-1*t-n.offsetWidth/2+"px"),t}}class Cl extends js{constructor(e,t){null!=t.modules.toolbar&&null==t.modules.toolbar.container&&(t.modules.toolbar.container=bd),super(e,t),this.quill.container.classList.add("ql-bubble")}extendToolbar(e){this.tooltip=new yd(this.quill,this.options.bounds),null!=e.container&&(this.tooltip.root.appendChild(e.container),this.buildButtons(e.container.querySelectorAll("button"),Ds),this.buildPickers(e.container.querySelectorAll("select"),Ds))}}Cl.DEFAULTS=Xe({},js.DEFAULTS,{modules:{toolbar:{handlers:{link(s){s?this.quill.theme.tooltip.edit():this.quill.format("link",!1,y.sources.USER)}}}}});const vd=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class _d extends Ll{static TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join("");preview=this.root.querySelector("a.ql-preview");listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(null!=this.linkRange){const t=this.linkRange;this.restoreFocus(),this.quill.formatText(t,"link",!1,B.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on(B.events.SELECTION_CHANGE,(e,t,n)=>{if(null!=e){if(0===e.length&&n===B.sources.USER){const[r,i]=this.quill.scroll.descendant(sr,e.index);if(null!=r){this.linkRange=new Je(e.index-i,r.length());const o=sr.formats(r.domNode);this.preview.textContent=o,this.preview.setAttribute("href",o),this.show();const a=this.quill.getBounds(this.linkRange);return void(null!=a&&this.position(a))}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}class Ol extends js{constructor(e,t){null!=t.modules.toolbar&&null==t.modules.toolbar.container&&(t.modules.toolbar.container=vd),super(e,t),this.quill.container.classList.add("ql-snow")}extendToolbar(e){null!=e.container&&(e.container.classList.add("ql-snow"),this.buildButtons(e.container.querySelectorAll("button"),Ds),this.buildPickers(e.container.querySelectorAll("select"),Ds),this.tooltip=new _d(this.quill,this.options.bounds),e.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(t,n)=>{e.handlers.link.call(e,!n.format.link)}))}}Ol.DEFAULTS=Xe({},js.DEFAULTS,{modules:{toolbar:{handlers:{link(s){if(s){const e=this.quill.getSelection();if(null==e||0===e.length)return;let t=this.quill.getText(e);/^\S+@\S+\.\S+$/.test(t)&&0!==t.indexOf("mailto:")&&(t=`mailto:${t}`);const{tooltip:n}=this.quill.theme;n.edit("link",t)}else this.quill.format("link",!1,y.sources.USER)}}}}});const Ad=Ol;Gr.register({"attributors/attribute/direction":tl,"attributors/class/align":Yi,"attributors/class/background":Ph,"attributors/class/color":jh,"attributors/class/direction":el,"attributors/class/font":rl,"attributors/class/size":ll,"attributors/style/align":Qi,"attributors/style/background":Pr,"attributors/style/color":jr,"attributors/style/direction":nl,"attributors/style/font":il,"attributors/style/size":ol},!0),Gr.register({"formats/align":Yi,"formats/direction":el,"formats/indent":Mf,"formats/background":Pr,"formats/color":jr,"formats/font":rl,"formats/size":ll,"formats/blockquote":Df,"formats/code-block":zt,"formats/header":jf,"formats/list":Wr,"formats/bold":Zr,"formats/code":Uh,"formats/italic":Pf,"formats/link":sr,"formats/script":Uf,"formats/strike":Ff,"formats/underline":Hf,"formats/formula":$f,"formats/image":zf,"formats/video":Kf,"modules/syntax":yl,"modules/table":class Vf extends me{static register(){y.register(De),y.register(En),y.register(en),y.register(Xr)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(Xr).forEach(e=>{e.balanceCells()})}deleteColumn(){const[e,,t]=this.getTable();null!=t&&(e.deleteColumn(t.cellOffset()),this.quill.update(y.sources.USER))}deleteRow(){const[,e]=this.getTable();null!=e&&(e.remove(),this.quill.update(y.sources.USER))}deleteTable(){const[e]=this.getTable();if(null==e)return;const t=e.offset();e.remove(),this.quill.update(y.sources.USER),this.quill.setSelection(t,y.sources.SILENT)}getTable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.quill.getSelection();if(null==e)return[null,null,null,-1];const[t,n]=this.quill.getLine(e.index);if(null==t||t.statics.blotName!==De.blotName)return[null,null,null,-1];const r=t.parent;return[r.parent.parent,r,t,n]}insertColumn(e){const t=this.quill.getSelection();if(!t)return;const[n,r,i]=this.getTable(t);if(null==i)return;const o=i.cellOffset();n.insertColumn(o+e),this.quill.update(y.sources.USER);let a=r.rowOffset();0===e&&(a+=1),this.quill.setSelection(t.index+a,t.length,y.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(e){const t=this.quill.getSelection();if(!t)return;const[n,r,i]=this.getTable(t);if(null==i)return;const o=r.rowOffset();n.insertRow(o+e),this.quill.update(y.sources.USER),e>0?this.quill.setSelection(t,y.sources.SILENT):this.quill.setSelection(t.index+r.children.length,t.length,y.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(e,t){const n=this.quill.getSelection();if(null==n)return;const r=new Array(e).fill(0).reduce(i=>{const o=new Array(t).fill("\n").join("");return i.insert(o,{table:Yr()})},(new O).retain(n.index));this.quill.updateContents(r,y.sources.USER),this.quill.setSelection(n.index,y.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(y.events.SCROLL_OPTIMIZE,e=>{e.some(t=>!!["TD","TR","TBODY","TABLE"].includes(t.target.tagName)&&(this.quill.once(y.events.TEXT_CHANGE,(n,r,i)=>{i===y.sources.USER&&this.balanceTables()}),!0))})}},"modules/toolbar":_l,"themes/bubble":Cl,"themes/snow":Ad,"ui/icons":Ds,"ui/picker":ir,"ui/icon-picker":xl,"ui/color-picker":wl,"ui/tooltip":Sl},!0);const Ed=Gr}}]);